#!/usr/bin/env node

/**
 * GitHub Repository Management Script
 * Creates and manages GitHub repositories for the portfolio projects
 */

import { Octokit } from '@octokit/rest';
import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';

// Repository configuration based on architecture strategy
const REPOSITORIES = [
  {
    name: 'portfolio-hub',
    description: 'Professional portfolio website showcasing 6+ enterprise-grade projects with live demos and technical documentation',
    topics: ['portfolio', 'react', 'typescript', 'cloudflare-pages', 'professional-showcase'],
    type: 'frontend',
    framework: 'React + TypeScript + Vite',
    deployment: 'Cloudflare Pages'
  },
  {
    name: 'ai-portfolio-platform',
    description: 'AI-powered portfolio platform enabling dynamic content generation and intelligent user interactions',
    topics: ['ai', 'machine-learning', 'portfolio', 'react', 'cloudflare-workers', 'openai'],
    type: 'fullstack',
    framework: 'React + TypeScript + Hono',
    deployment: 'Cloudflare Pages + Workers'
  },
  {
    name: 'enterprise-data-pipeline',
    description: 'Scalable enterprise data pipeline with real-time ETL processing and monitoring dashboard',
    topics: ['data-pipeline', 'etl', 'enterprise', 'vue', 'cloudflare-workers', 'data-processing'],
    type: 'fullstack',
    framework: 'Vue.js + TypeScript + Workers',
    deployment: 'Cloudflare Pages + Workers'
  },
  {
    name: 'predictive-analytics-platform',
    description: 'Advanced predictive analytics platform with machine learning models and interactive visualizations',
    topics: ['machine-learning', 'analytics', 'data-visualization', 'react', 'd3js', 'ml-js'],
    type: 'frontend',
    framework: 'React + D3.js + ML.js',
    deployment: 'Cloudflare Pages'
  },
  {
    name: 'cfd-analysis-platform',
    description: 'Computational Fluid Dynamics analysis platform with 3D visualization and WebAssembly processing',
    topics: ['cfd', 'webassembly', 'threejs', 'engineering', 'simulation', '3d-visualization'],
    type: 'frontend',
    framework: 'React + Three.js + WebAssembly',
    deployment: 'Cloudflare Pages'
  },
  {
    name: 'realtime-bi-dashboard',
    description: 'Real-time business intelligence dashboard with live data streaming and interactive analytics',
    topics: ['business-intelligence', 'real-time', 'dashboard', 'react', 'websockets', 'analytics'],
    type: 'fullstack',
    framework: 'React + Recharts + WebSockets',
    deployment: 'Cloudflare Pages + Workers'
  },
  {
    name: 'enterprise-llm-solution',
    description: 'Enterprise LLM solution with document processing, conversation management, and AI workflows',
    topics: ['llm', 'ai', 'enterprise', 'document-processing', 'react', 'cloudflare-ai'],
    type: 'fullstack',
    framework: 'React + TypeScript + AI Workers',
    deployment: 'Cloudflare Pages + Workers'
  }
];

class GitHubRepoManager {
  constructor(token, username) {
    this.octokit = new Octokit({ auth: token });
    this.username = username;
    this.token = token;
  }

  /**
   * Test GitHub API connection
   */
  async testConnection() {
    try {
      const { data } = await this.octokit.rest.users.getAuthenticated();
      console.log(`✅ GitHub API connection successful for user: ${data.login}`);
      return { success: true, user: data };
    } catch (error) {
      console.error('❌ GitHub API connection failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if repository exists
   */
  async repositoryExists(repoName) {
    try {
      await this.octokit.rest.repos.get({
        owner: this.username,
        repo: repoName
      });
      return true;
    } catch (error) {
      if (error.status === 404) {
        return false;
      }
      throw error;
    }
  }

  /**
   * Create a single repository
   */
  async createRepository(repoConfig) {
    try {
      console.log(`📦 Creating repository: ${repoConfig.name}`);

      // Check if repository already exists
      if (await this.repositoryExists(repoConfig.name)) {
        console.log(`⚠️  Repository ${repoConfig.name} already exists, skipping creation`);
        return { success: true, existed: true };
      }

      // Create repository
      const { data: repo } = await this.octokit.rest.repos.createForAuthenticatedUser({
        name: repoConfig.name,
        description: repoConfig.description,
        homepage: `https://${repoConfig.name}.dev`,
        topics: repoConfig.topics,
        private: false,
        has_issues: true,
        has_projects: true,
        has_wiki: false,
        allow_squash_merge: true,
        allow_merge_commit: false,
        allow_rebase_merge: false,
        delete_branch_on_merge: true,
        auto_init: true,
        gitignore_template: 'Node',
        license_template: 'mit'
      });

      console.log(`✅ Repository created: ${repo.html_url}`);
      return { success: true, repo, existed: false };

    } catch (error) {
      console.error(`❌ Failed to create repository ${repoConfig.name}:`, error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create all repositories
   */
  async createAllRepositories() {
    console.log('🚀 Starting repository creation process...\n');

    const results = {
      created: [],
      existed: [],
      failed: []
    };

    for (const repoConfig of REPOSITORIES) {
      const result = await this.createRepository(repoConfig);
      
      if (result.success) {
        if (result.existed) {
          results.existed.push(repoConfig.name);
        } else {
          results.created.push(repoConfig.name);
        }
      } else {
        results.failed.push({ name: repoConfig.name, error: result.error });
      }

      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    return results;
  }

  /**
   * Setup repository with initial structure
   */
  async setupRepositoryStructure(repoName, repoConfig) {
    try {
      console.log(`🏗️  Setting up structure for: ${repoName}`);

      // Create basic file structure
      const files = [
        {
          path: 'README.md',
          content: await this.generateReadme(repoConfig)
        },
        {
          path: 'package.json',
          content: await this.generatePackageJson(repoConfig)
        },
        {
          path: 'vite.config.ts',
          content: await this.generateViteConfig(repoConfig)
        },
        {
          path: 'tailwind.config.ts',
          content: await this.generateTailwindConfig()
        },
        {
          path: 'tsconfig.json',
          content: await this.generateTsConfig()
        },
        {
          path: '.env.example',
          content: await this.generateEnvExample(repoConfig)
        },
        {
          path: 'src/main.tsx',
          content: await this.generateMainTsx()
        },
        {
          path: 'src/App.tsx',
          content: await this.generateAppTsx(repoConfig)
        },
        {
          path: 'index.html',
          content: await this.generateIndexHtml(repoConfig)
        }
      ];

      // Add Cloudflare Workers files for fullstack projects
      if (repoConfig.type === 'fullstack') {
        files.push(
          {
            path: 'wrangler.toml',
            content: await this.generateWranglerConfig(repoConfig)
          },
          {
            path: 'src/workers/api.ts',
            content: await this.generateWorkerCode(repoConfig)
          }
        );
      }

      // Create files in repository
      for (const file of files) {
        await this.createOrUpdateFile(repoName, file.path, file.content, `Add ${file.path}`);
      }

      console.log(`✅ Repository structure setup complete for: ${repoName}`);
      return { success: true };

    } catch (error) {
      console.error(`❌ Failed to setup repository structure for ${repoName}:`, error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create or update a file in repository
   */
  async createOrUpdateFile(repoName, filePath, content, message) {
    try {
      // Check if file exists
      let sha = null;
      try {
        const { data } = await this.octokit.rest.repos.getContent({
          owner: this.username,
          repo: repoName,
          path: filePath
        });
        sha = data.sha;
      } catch (error) {
        if (error.status !== 404) throw error;
      }

      // Create or update file
      await this.octokit.rest.repos.createOrUpdateFileContents({
        owner: this.username,
        repo: repoName,
        path: filePath,
        message,
        content: Buffer.from(content).toString('base64'),
        sha
      });

    } catch (error) {
      console.error(`Failed to create/update file ${filePath}:`, error.message);
      throw error;
    }
  }

  /**
   * Generate README content
   */
  async generateReadme(repoConfig) {
    const template = await fs.readFile(path.join(process.cwd(), 'templates/README_TEMPLATE.md'), 'utf-8');
    
    return template
      .replace(/{PROJECT_NAME}/g, repoConfig.name.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' '))
      .replace(/{project-domain}/g, repoConfig.name)
      .replace(/{username}/g, this.username)
      .replace(/{repo-name}/g, repoConfig.name)
      .replace(/{PROJECT_DESCRIPTION}/g, repoConfig.description)
      .replace(/{TECHNOLOGY_FOCUS}/g, repoConfig.framework);
  }

  /**
   * Generate package.json content
   */
  async generatePackageJson(repoConfig) {
    const basePackage = {
      name: repoConfig.name,
      version: "1.0.0",
      description: repoConfig.description,
      type: "module",
      scripts: {
        dev: "vite",
        build: "tsc && vite build",
        preview: "vite preview",
        test: "vitest",
        "test:coverage": "vitest --coverage",
        lint: "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
        "lint:fix": "eslint . --ext ts,tsx --fix"
      },
      dependencies: {
        react: "^18.3.1",
        "react-dom": "^18.3.1",
        "react-router-dom": "^6.21.1",
        "@radix-ui/react-icons": "^1.3.0",
        "lucide-react": "^0.311.0",
        "framer-motion": "^10.16.16",
        "class-variance-authority": "^0.7.0",
        "clsx": "^2.0.0",
        "tailwind-merge": "^2.2.0"
      },
      devDependencies: {
        "@types/react": "^18.2.43",
        "@types/react-dom": "^18.2.17",
        "@typescript-eslint/eslint-plugin": "^6.14.0",
        "@typescript-eslint/parser": "^6.14.0",
        "@vitejs/plugin-react": "^4.2.1",
        autoprefixer: "^10.4.16",
        eslint: "^8.55.0",
        "eslint-plugin-react-hooks": "^4.6.0",
        "eslint-plugin-react-refresh": "^0.4.5",
        postcss: "^8.4.32",
        tailwindcss: "^3.4.0",
        typescript: "^5.2.2",
        vite: "^5.0.8",
        vitest: "^1.1.0"
      }
    };

    // Add specific dependencies based on project type
    if (repoConfig.type === 'fullstack') {
      basePackage.scripts['dev:worker'] = 'wrangler dev';
      basePackage.scripts.deploy = 'wrangler deploy';
      basePackage.devDependencies['@cloudflare/workers-types'] = '^4.20231218.0';
      basePackage.dependencies.hono = '^4.0.0';
    }

    // Add project-specific dependencies
    if (repoConfig.name.includes('analytics') || repoConfig.name.includes('bi')) {
      basePackage.dependencies.recharts = '^2.8.0';
      basePackage.dependencies.d3 = '^7.8.5';
    }

    if (repoConfig.name.includes('three') || repoConfig.name.includes('cfd')) {
      basePackage.dependencies['@react-three/fiber'] = '^8.15.12';
      basePackage.dependencies['@react-three/drei'] = '^9.92.7';
      basePackage.dependencies.three = '^0.159.0';
    }

    return JSON.stringify(basePackage, null, 2);
  }

  /**
   * Generate other configuration files
   */
  async generateViteConfig(repoConfig) {
    return `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 3000,
    host: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom']
        }
      }
    }
  }
})`;
  }

  async generateTailwindConfig() {
    return `import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [],
}

export default config`;
  }

  async generateTsConfig() {
    return `{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}`;
  }

  async generateEnvExample(repoConfig) {
    let envVars = `# ${repoConfig.name.toUpperCase()} Environment Variables

# Application
VITE_APP_NAME="${repoConfig.name}"
VITE_APP_VERSION="1.0.0"
VITE_API_BASE_URL="https://${repoConfig.name}.dev/api"

# Cloudflare (for production)
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_ACCOUNT_ID=your_account_id`;

    if (repoConfig.type === 'fullstack') {
      envVars += `

# Worker-specific
DATABASE_URL=your_d1_database_url
R2_BUCKET_NAME=your_r2_bucket`;
    }

    if (repoConfig.name.includes('ai') || repoConfig.name.includes('llm')) {
      envVars += `

# AI Services
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key`;
    }

    return envVars;
  }

  async generateMainTsx() {
    return `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`;
  }

  async generateAppTsx(repoConfig) {
    const componentName = repoConfig.name.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join('');
    
    return `import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/demo" element={<DemoPage />} />
          <Route path="/docs" element={<DocsPage />} />
        </Routes>
      </div>
    </Router>
  )
}

function HomePage() {
  return (
    <div className="container mx-auto px-4 py-16">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-6">
          ${componentName}
        </h1>
        <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
          ${repoConfig.description}
        </p>
        <div className="flex gap-4 justify-center">
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
            Try Demo
          </button>
          <button className="border border-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors">
            View Docs
          </button>
        </div>
      </div>
    </div>
  )
}

function DemoPage() {
  return (
    <div className="container mx-auto px-4 py-16">
      <h1 className="text-3xl font-bold text-white mb-8">Demo</h1>
      <div className="bg-gray-800 rounded-lg p-6">
        <p className="text-gray-300">Demo functionality coming soon...</p>
      </div>
    </div>
  )
}

function DocsPage() {
  return (
    <div className="container mx-auto px-4 py-16">
      <h1 className="text-3xl font-bold text-white mb-8">Documentation</h1>
      <div className="bg-gray-800 rounded-lg p-6">
        <p className="text-gray-300">Documentation coming soon...</p>
      </div>
    </div>
  )
}

export default App`;
  }

  async generateIndexHtml(repoConfig) {
    return `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="${repoConfig.description}" />
    <meta name="keywords" content="${repoConfig.topics.join(', ')}" />
    <title>${repoConfig.name.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ')}</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`;
  }

  async generateWranglerConfig(repoConfig) {
    return `name = "${repoConfig.name}-worker"
main = "src/workers/api.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "${repoConfig.name}-worker"
routes = ["${repoConfig.name}.dev/api/*"]

[[d1_databases]]
binding = "DB"
database_name = "${repoConfig.name}-db"
database_id = "your-database-id"

[[r2_buckets]]
binding = "BUCKET"
bucket_name = "${repoConfig.name}-storage"`;
  }

  async generateWorkerCode(repoConfig) {
    return `import { Hono } from 'hono'
import { cors } from 'hono/cors'

type Bindings = {
  DB: D1Database
  BUCKET: R2Bucket
}

const app = new Hono<{ Bindings: Bindings }>()

// CORS middleware
app.use('/api/*', cors({
  origin: ['https://${repoConfig.name}.dev', 'http://localhost:3000'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}))

// Health check endpoint
app.get('/api/health', (c) => {
  return c.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: '${repoConfig.name}'
  })
})

// Example API endpoint
app.get('/api/data', async (c) => {
  try {
    // Example database query
    const result = await c.env.DB.prepare('SELECT * FROM example_table LIMIT 10').all()
    
    return c.json({
      success: true,
      data: result.results || []
    })
  } catch (error) {
    return c.json({
      success: false,
      error: 'Database query failed'
    }, 500)
  }
})

// Example POST endpoint
app.post('/api/data', async (c) => {
  try {
    const body = await c.req.json()
    
    // Example database insert
    const result = await c.env.DB.prepare(
      'INSERT INTO example_table (name, value) VALUES (?, ?)'
    ).bind(body.name, body.value).run()
    
    return c.json({
      success: true,
      id: result.meta.last_row_id
    })
  } catch (error) {
    return c.json({
      success: false,
      error: 'Failed to create record'
    }, 500)
  }
})

export default app`;
  }

  /**
   * Generate summary report
   */
  generateReport(results) {
    const total = REPOSITORIES.length;
    const { created, existed, failed } = results;

    console.log('\n📊 REPOSITORY CREATION SUMMARY');
    console.log('═'.repeat(50));
    console.log(`Total repositories: ${total}`);
    console.log(`✅ Successfully created: ${created.length}`);
    console.log(`⚠️  Already existed: ${existed.length}`);
    console.log(`❌ Failed: ${failed.length}`);

    if (created.length > 0) {
      console.log('\n🆕 NEWLY CREATED REPOSITORIES:');
      created.forEach(name => console.log(`  • ${name}`));
    }

    if (existed.length > 0) {
      console.log('\n📁 EXISTING REPOSITORIES:');
      existed.forEach(name => console.log(`  • ${name}`));
    }

    if (failed.length > 0) {
      console.log('\n💥 FAILED REPOSITORIES:');
      failed.forEach(({ name, error }) => console.log(`  • ${name}: ${error}`));
    }

    console.log('\n🔗 REPOSITORY LINKS:');
    REPOSITORIES.forEach(repo => {
      console.log(`  • https://github.com/${this.username}/${repo.name}`);
    });
  }
}

// CLI functionality
async function main() {
  const token = process.env.GITHUB_TOKEN || process.argv[2];
  const username = process.env.GITHUB_USERNAME || process.argv[3];

  if (!token) {
    console.error('❌ GitHub token required. Set GITHUB_TOKEN environment variable or pass as argument.');
    process.exit(1);
  }

  if (!username) {
    console.error('❌ GitHub username required. Set GITHUB_USERNAME environment variable or pass as argument.');
    process.exit(1);
  }

  const manager = new GitHubRepoManager(token, username);

  // Test connection
  const connectionTest = await manager.testConnection();
  if (!connectionTest.success) {
    process.exit(1);
  }

  // Create repositories
  const results = await manager.createAllRepositories();

  // Generate report
  manager.generateReport(results);

  console.log('\n🎉 Repository creation process completed!');
  console.log('💡 Next steps:');
  console.log('  1. Run setup-project-structures.js to initialize project files');
  console.log('  2. Setup GitHub Actions workflows');
  console.log('  3. Configure Cloudflare deployments');
}

// Export for use as module
export { GitHubRepoManager, REPOSITORIES };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}