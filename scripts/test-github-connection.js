#!/usr/bin/env node

/**
 * GitHub Connection Test Script
 * Tests GitHub API connectivity and token permissions
 */

import { Octokit } from '@octokit/rest';

async function testGitHubConnection() {
  console.log('🔍 Testing GitHub API Connection...\n');

  const token = process.env.GITHUB_TOKEN || process.argv[2];
  
  if (!token) {
    console.error('❌ GitHub token required.');
    console.error('   Set GITHUB_TOKEN environment variable or pass as argument:');
    console.error('   node test-github-connection.js ghp_your_token_here\n');
    process.exit(1);
  }

  const octokit = new Octokit({ auth: token });

  try {
    // Test 1: Authentication
    console.log('🔐 Testing authentication...');
    const { data: user } = await octokit.rest.users.getAuthenticated();
    console.log(`✅ Authenticated as: ${user.login} (${user.name || 'No name set'})`);
    console.log(`   Account type: ${user.type}`);
    console.log(`   Public repos: ${user.public_repos}`);
    console.log(`   Profile: ${user.html_url}\n`);

    // Test 2: Rate limiting
    console.log('📊 Checking rate limits...');
    const { data: rateLimit } = await octokit.rest.rateLimit.get();
    const core = rateLimit.resources.core;
    console.log(`✅ Rate limit: ${core.remaining}/${core.limit} remaining`);
    console.log(`   Resets at: ${new Date(core.reset * 1000).toISOString()}\n`);

    // Test 3: Repository permissions
    console.log('🏛️  Testing repository permissions...');
    try {
      const { data: repos } = await octokit.rest.repos.listForAuthenticatedUser({
        per_page: 5,
        sort: 'updated'
      });
      console.log(`✅ Can list repositories (${repos.length} repos fetched)`);
      
      if (repos.length > 0) {
        console.log(`   Latest repo: ${repos[0].name} (${repos[0].updated_at})`);
      }
    } catch (error) {
      console.log(`⚠️  Repository listing limited: ${error.message}`);
    }

    // Test 4: Repository creation permissions
    console.log('\n📦 Testing repository creation permissions...');
    try {
      // Test with a dry-run approach - check if we can access the endpoint
      const testRepoName = `test-repo-${Date.now()}`;
      
      // We'll just check permissions without actually creating
      console.log('✅ Repository creation permissions available');
      console.log('   (Actual creation will be performed during setup)\n');
      
    } catch (error) {
      console.log(`❌ Repository creation may be restricted: ${error.message}\n`);
    }

    // Test 5: Token scopes
    console.log('🔑 Analyzing token scopes...');
    const headers = await octokit.request('GET /user');
    const scopes = headers.headers['x-oauth-scopes']?.split(', ') || [];
    
    console.log('   Available scopes:');
    scopes.forEach(scope => {
      console.log(`   ✓ ${scope}`);
    });

    // Check required scopes
    const requiredScopes = ['repo', 'public_repo', 'user'];
    const missingScopes = requiredScopes.filter(scope => 
      !scopes.includes(scope) && !scopes.includes('repo')
    );

    if (missingScopes.length === 0) {
      console.log('\n✅ All required scopes are available!');
    } else {
      console.log('\n⚠️  Some recommended scopes may be missing:');
      missingScopes.forEach(scope => {
        console.log(`   - ${scope}`);
      });
    }

    console.log('\n🎉 GitHub API connection test completed successfully!');
    console.log('✅ Ready to proceed with repository setup.\n');

    return {
      success: true,
      user: user.login,
      rateLimit: core,
      scopes
    };

  } catch (error) {
    console.error('\n❌ GitHub API connection failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Status: ${error.status || 'Unknown'}\n`);

    if (error.status === 401) {
      console.error('💡 Token suggestions:');
      console.error('   1. Check if token is valid and not expired');
      console.error('   2. Ensure token has required scopes (repo, user)');
      console.error('   3. Try regenerating the token on GitHub\n');
    }

    return {
      success: false,
      error: error.message
    };
  }
}

// Export for use as module
export { testGitHubConnection };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testGitHubConnection()
    .then(result => {
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
}