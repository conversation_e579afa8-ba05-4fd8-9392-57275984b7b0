{"name": "portfolio-repository-setup", "version": "1.0.0", "description": "GitHub repository creation and management scripts for portfolio projects", "type": "module", "scripts": {"setup": "node setup-portfolio-repositories.js", "create-repos": "node github-repo-manager.js", "test-connection": "node test-github-connection.js", "install-deps": "npm install"}, "dependencies": {"@octokit/rest": "^20.0.2"}, "devDependencies": {"@types/node": "^20.11.16"}, "keywords": ["github", "repository", "automation", "portfolio", "cloudflare"], "author": "Portfolio Setup Automation", "license": "MIT"}