#!/usr/bin/env node

/**
 * Master Portfolio Repository Setup Script
 * Coordinates the entire GitHub repository creation and setup process
 */

import { GitHubRepoManager, REPOSITORIES } from './github-repo-manager.js';
import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';

class PortfolioSetupCoordinator {
  constructor(githubToken, githubUsername) {
    this.repoManager = new GitHubRepoManager(githubToken, githubUsername);
    this.githubToken = githubToken;
    this.githubUsername = githubUsername;
  }

  /**
   * Run the complete setup process
   */
  async runCompleteSetup() {
    console.log('🚀 Starting Portfolio Repository Setup Process...\n');
    console.log('═'.repeat(60));

    const setupResults = {
      connectionTest: null,
      repositoryCreation: null,
      projectStructures: null,
      githubActions: null,
      cloudflareSetup: null,
      verification: null
    };

    try {
      // Step 1: Test GitHub connection
      console.log('\n📡 Step 1: Testing GitHub API Connection...');
      setupResults.connectionTest = await this.repoManager.testConnection();
      
      if (!setupResults.connectionTest.success) {
        throw new Error('GitHub API connection failed');
      }

      // Step 2: Create repositories
      console.log('\n📦 Step 2: Creating GitHub Repositories...');
      setupResults.repositoryCreation = await this.repoManager.createAllRepositories();

      // Step 3: Setup project structures
      console.log('\n🏗️  Step 3: Setting up Project Structures...');
      setupResults.projectStructures = await this.setupAllProjectStructures();

      // Step 4: Setup GitHub Actions
      console.log('\n⚙️  Step 4: Setting up GitHub Actions Workflows...');
      setupResults.githubActions = await this.setupGitHubActions();

      // Step 5: Prepare Cloudflare integration
      console.log('\n☁️  Step 5: Preparing Cloudflare Integration...');
      setupResults.cloudflareSetup = await this.prepareCloudflareIntegration();

      // Step 6: Verification
      console.log('\n✅ Step 6: Verifying Setup...');
      setupResults.verification = await this.verifySetup();

      // Generate final report
      this.generateFinalReport(setupResults);

      return setupResults;

    } catch (error) {
      console.error('\n❌ Setup process failed:', error.message);
      this.generateErrorReport(setupResults, error);
      throw error;
    }
  }

  /**
   * Setup project structures for all repositories
   */
  async setupAllProjectStructures() {
    console.log('Setting up project structures for all repositories...');
    
    const results = {
      completed: [],
      failed: []
    };

    for (const repoConfig of REPOSITORIES) {
      try {
        console.log(`📁 Setting up structure for: ${repoConfig.name}`);
        
        await this.setupSingleProjectStructure(repoConfig);
        results.completed.push(repoConfig.name);
        
        console.log(`✅ Structure setup complete for: ${repoConfig.name}`);
        
        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        console.error(`❌ Failed to setup structure for ${repoConfig.name}:`, error.message);
        results.failed.push({ name: repoConfig.name, error: error.message });
      }
    }

    return results;
  }

  /**
   * Setup structure for a single project
   */
  async setupSingleProjectStructure(repoConfig) {
    // Create directory structure files
    const files = await this.generateProjectFiles(repoConfig);
    
    // Upload files to repository
    for (const file of files) {
      await this.repoManager.createOrUpdateFile(
        repoConfig.name,
        file.path,
        file.content,
        `Add ${file.path} - Initial project setup`
      );
    }
  }

  /**
   * Generate all project files for a repository
   */
  async generateProjectFiles(repoConfig) {
    const files = [];

    // README.md - Use project-specific template if exists
    const readmeTemplate = await this.getReadmeTemplate(repoConfig.name);
    files.push({
      path: 'README.md',
      content: readmeTemplate
    });

    // package.json
    files.push({
      path: 'package.json',
      content: await this.generatePackageJson(repoConfig)
    });

    // Project configuration files
    files.push(
      { path: 'vite.config.ts', content: await this.generateViteConfig(repoConfig) },
      { path: 'tailwind.config.ts', content: await this.generateTailwindConfig() },
      { path: 'tsconfig.json', content: await this.generateTsConfig() },
      { path: 'postcss.config.js', content: await this.generatePostcssConfig() },
      { path: '.env.example', content: await this.generateEnvExample(repoConfig) },
      { path: '.gitignore', content: await this.generateGitignore() }
    );

    // Source files
    files.push(
      { path: 'index.html', content: await this.generateIndexHtml(repoConfig) },
      { path: 'src/main.tsx', content: await this.generateMainTsx() },
      { path: 'src/App.tsx', content: await this.generateAppTsx(repoConfig) },
      { path: 'src/index.css', content: await this.generateIndexCss() },
      { path: 'src/lib/utils.ts', content: await this.generateUtilsTs() }
    );

    // Component files
    files.push(
      { path: 'src/components/Header.tsx', content: await this.generateHeaderComponent(repoConfig) },
      { path: 'src/components/Footer.tsx', content: await this.generateFooterComponent() },
      { path: 'src/components/LoadingSpinner.tsx', content: await this.generateLoadingSpinner() }
    );

    // Add Cloudflare Workers files for fullstack projects
    if (repoConfig.type === 'fullstack') {
      files.push(
        { path: 'wrangler.toml', content: await this.generateWranglerConfig(repoConfig) },
        { path: 'src/workers/api.ts', content: await this.generateWorkerCode(repoConfig) },
        { path: 'src/workers/types.ts', content: await this.generateWorkerTypes() }
      );
    }

    // GitHub Actions workflows
    files.push(
      { path: '.github/workflows/deploy.yml', content: await this.generateDeployWorkflow(repoConfig) },
      { path: '.github/workflows/test.yml', content: await this.generateTestWorkflow() }
    );

    // Documentation
    files.push(
      { path: 'DEMO.md', content: await this.generateDemoDocumentation(repoConfig) },
      { path: 'ARCHITECTURE.md', content: await this.generateArchitectureDoc(repoConfig) }
    );

    return files;
  }

  /**
   * Get README template for specific project
   */
  async getReadmeTemplate(projectName) {
    const templatePath = path.join(process.cwd(), 'templates', 'readmes', `${projectName}-README.md`);
    
    try {
      return await fs.readFile(templatePath, 'utf-8');
    } catch (error) {
      // Fallback to generic template
      console.log(`⚠️  Using generic README template for ${projectName}`);
      const genericTemplate = await fs.readFile(
        path.join(process.cwd(), 'templates', 'README_TEMPLATE.md'), 
        'utf-8'
      );
      
      // Replace placeholders
      return this.replaceTemplatePlaceholders(genericTemplate, projectName);
    }
  }

  /**
   * Replace template placeholders
   */
  replaceTemplatePlaceholders(template, projectName) {
    const repo = REPOSITORIES.find(r => r.name === projectName);
    if (!repo) return template;

    return template
      .replace(/{PROJECT_NAME}/g, repo.name.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' '))
      .replace(/{project-domain}/g, repo.name)
      .replace(/{username}/g, this.githubUsername)
      .replace(/{repo-name}/g, repo.name)
      .replace(/{PROJECT_DESCRIPTION}/g, repo.description)
      .replace(/{TECHNOLOGY_FOCUS}/g, repo.framework)
      .replace(/{FEATURE_1}/g, 'Modern Architecture')
      .replace(/{FEATURE_2}/g, 'Scalable Design')
      .replace(/{FEATURE_3}/g, 'Professional Implementation');
  }

  /**
   * Generate package.json for project
   */
  async generatePackageJson(repoConfig) {
    const basePackage = {
      name: repoConfig.name,
      version: "1.0.0",
      description: repoConfig.description,
      type: "module",
      scripts: {
        dev: "vite",
        build: "tsc && vite build",
        preview: "vite preview",
        test: "vitest",
        "test:coverage": "vitest --coverage",
        "test:e2e": "playwright test",
        lint: "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
        "lint:fix": "eslint . --ext ts,tsx --fix",
        "type-check": "tsc --noEmit"
      },
      dependencies: {
        react: "^18.3.1",
        "react-dom": "^18.3.1",
        "react-router-dom": "^6.21.1",
        "@radix-ui/react-icons": "^1.3.0",
        "lucide-react": "^0.311.0",
        "framer-motion": "^11.0.0",
        "class-variance-authority": "^0.7.0",
        clsx: "^2.1.0",
        "tailwind-merge": "^2.2.0"
      },
      devDependencies: {
        "@types/react": "^18.2.55",
        "@types/react-dom": "^18.2.19",
        "@typescript-eslint/eslint-plugin": "^6.21.0",
        "@typescript-eslint/parser": "^6.21.0",
        "@vitejs/plugin-react": "^4.2.1",
        "@playwright/test": "^1.41.2",
        autoprefixer: "^10.4.17",
        eslint: "^8.56.0",
        "eslint-plugin-react-hooks": "^4.6.0",
        "eslint-plugin-react-refresh": "^0.4.5",
        postcss: "^8.4.33",
        tailwindcss: "^3.4.1",
        typescript: "^5.3.3",
        vite: "^5.1.0",
        vitest: "^1.2.2",
        "@vitest/coverage-v8": "^1.2.2"
      }
    };

    // Add project-specific dependencies
    this.addProjectSpecificDependencies(basePackage, repoConfig);

    return JSON.stringify(basePackage, null, 2);
  }

  /**
   * Add project-specific dependencies
   */
  addProjectSpecificDependencies(packageJson, repoConfig) {
    // Fullstack projects
    if (repoConfig.type === 'fullstack') {
      packageJson.scripts['dev:worker'] = 'wrangler dev';
      packageJson.scripts.deploy = 'wrangler deploy';
      packageJson.devDependencies['@cloudflare/workers-types'] = '^4.20240205.0';
      packageJson.dependencies.hono = '^4.0.10';
    }

    // AI projects
    if (repoConfig.name.includes('ai') || repoConfig.name.includes('llm')) {
      packageJson.dependencies['@ai-sdk/openai'] = '^0.0.9';
      packageJson.dependencies['ai'] = '^3.0.0';
    }

    // Analytics/Data visualization projects
    if (repoConfig.name.includes('analytics') || repoConfig.name.includes('bi')) {
      packageJson.dependencies.recharts = '^2.10.3';
      packageJson.dependencies.d3 = '^7.8.5';
      packageJson.dependencies['@types/d3'] = '^7.4.3';
    }

    // 3D/CFD projects
    if (repoConfig.name.includes('cfd') || repoConfig.name.includes('three')) {
      packageJson.dependencies['@react-three/fiber'] = '^8.15.16';
      packageJson.dependencies['@react-three/drei'] = '^9.96.1';
      packageJson.dependencies.three = '^0.160.1';
      packageJson.devDependencies['@types/three'] = '^0.160.0';
    }
  }

  /**
   * Setup GitHub Actions workflows
   */
  async setupGitHubActions() {
    console.log('Setting up GitHub Actions workflows...');
    
    const results = {
      completed: [],
      failed: []
    };

    for (const repoConfig of REPOSITORIES) {
      try {
        console.log(`⚙️  Setting up GitHub Actions for: ${repoConfig.name}`);
        
        // Deploy workflow
        const deployWorkflow = await fs.readFile(
          path.join(process.cwd(), 'templates', 'github-actions', 'deploy.yml'),
          'utf-8'
        );
        
        await this.repoManager.createOrUpdateFile(
          repoConfig.name,
          '.github/workflows/deploy.yml',
          deployWorkflow,
          'Add GitHub Actions deploy workflow'
        );

        // Test workflow
        const testWorkflow = await fs.readFile(
          path.join(process.cwd(), 'templates', 'github-actions', 'test.yml'),
          'utf-8'
        );
        
        await this.repoManager.createOrUpdateFile(
          repoConfig.name,
          '.github/workflows/test.yml',
          testWorkflow,
          'Add GitHub Actions test workflow'
        );

        results.completed.push(repoConfig.name);
        
        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.error(`❌ Failed to setup GitHub Actions for ${repoConfig.name}:`, error.message);
        results.failed.push({ name: repoConfig.name, error: error.message });
      }
    }

    return results;
  }

  /**
   * Prepare Cloudflare integration setup
   */
  async prepareCloudflareIntegration() {
    console.log('Preparing Cloudflare integration documentation...');
    
    const cloudflareSetupGuide = await this.generateCloudflareSetupGuide();
    
    // Save setup guide
    await fs.writeFile(
      path.join(process.cwd(), 'CLOUDFLARE_SETUP_GUIDE.md'),
      cloudflareSetupGuide
    );

    // Generate wrangler configuration templates
    const wranglerConfigs = await this.generateWranglerConfigs();
    
    return {
      setupGuide: 'CLOUDFLARE_SETUP_GUIDE.md',
      wranglerConfigs: wranglerConfigs.length,
      domains: REPOSITORIES.map(repo => `${repo.name}.dev`)
    };
  }

  /**
   * Verify setup completion
   */
  async verifySetup() {
    console.log('Verifying repository setup...');
    
    const verification = {
      repositories: [],
      overall: true
    };

    for (const repoConfig of REPOSITORIES) {
      try {
        const repoExists = await this.repoManager.repositoryExists(repoConfig.name);
        
        verification.repositories.push({
          name: repoConfig.name,
          exists: repoExists,
          url: `https://github.com/${this.githubUsername}/${repoConfig.name}`,
          liveUrl: `https://${repoConfig.name}.dev`
        });

        if (!repoExists) {
          verification.overall = false;
        }
        
      } catch (error) {
        verification.repositories.push({
          name: repoConfig.name,
          exists: false,
          error: error.message
        });
        verification.overall = false;
      }
    }

    return verification;
  }

  /**
   * Generate final setup report
   */
  generateFinalReport(results) {
    console.log('\n' + '█'.repeat(60));
    console.log('🎉 PORTFOLIO REPOSITORY SETUP COMPLETE!');
    console.log('█'.repeat(60));

    console.log('\n📊 SETUP SUMMARY:');
    console.log(`✅ GitHub Connection: ${results.connectionTest.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`📦 Repositories Created: ${results.repositoryCreation.created.length}/${REPOSITORIES.length}`);
    console.log(`🏗️  Project Structures: ${results.projectStructures.completed.length}/${REPOSITORIES.length}`);
    console.log(`⚙️  GitHub Actions: ${results.githubActions.completed.length}/${REPOSITORIES.length}`);
    console.log(`☁️  Cloudflare Setup: ${results.cloudflareSetup ? 'PREPARED' : 'FAILED'}`);
    console.log(`✅ Verification: ${results.verification.overall ? 'PASSED' : 'PARTIAL'}`);

    console.log('\n🔗 REPOSITORY LINKS:');
    results.verification.repositories.forEach(repo => {
      const status = repo.exists ? '✅' : '❌';
      console.log(`  ${status} ${repo.name}: ${repo.url}`);
    });

    console.log('\n🌐 FUTURE LIVE URLS:');
    REPOSITORIES.forEach(repo => {
      console.log(`  🚀 ${repo.name}: https://${repo.name}.dev`);
    });

    console.log('\n📋 NEXT STEPS:');
    console.log('  1. ☁️  Setup Cloudflare accounts and domains (see CLOUDFLARE_SETUP_GUIDE.md)');
    console.log('  2. 🔑 Configure GitHub secrets for Cloudflare deployment');
    console.log('  3. 🚀 Deploy projects to Cloudflare Pages + Workers');
    console.log('  4. 🔗 Update portfolio hub with live project links');
    console.log('  5. 📊 Setup monitoring and analytics');

    console.log('\n💡 PROFESSIONAL IMPACT:');
    console.log('  • 7 enterprise-grade repositories created');
    console.log('  • Full CI/CD pipelines configured');
    console.log('  • Professional documentation standards');
    console.log('  • Scalable serverless architecture');
    console.log('  • Live demo capabilities for client presentations');
  }

  /**
   * Generate error report
   */
  generateErrorReport(results, error) {
    console.log('\n' + '█'.repeat(60));
    console.log('❌ SETUP PROCESS ENCOUNTERED ERRORS');
    console.log('█'.repeat(60));
    
    console.log('\n🐛 PRIMARY ERROR:');
    console.log(`   ${error.message}`);
    
    if (results.repositoryCreation?.failed?.length > 0) {
      console.log('\n📦 REPOSITORY CREATION FAILURES:');
      results.repositoryCreation.failed.forEach(failure => {
        console.log(`   ❌ ${failure.name}: ${failure.error}`);
      });
    }

    console.log('\n🔧 RECOVERY STEPS:');
    console.log('   1. Check GitHub token permissions');
    console.log('   2. Verify internet connection');
    console.log('   3. Check GitHub API rate limits');
    console.log('   4. Re-run the setup script');
  }

  // Additional utility methods for generating configuration files...
  async generateViteConfig(repoConfig) {
    return `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 3000,
    host: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom']
        }
      }
    }
  }
})`;
  }

  async generateTailwindConfig() {
    return `import type { Config } from 'tailwindcss'

const config: Config = {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
      },
    },
  },
  plugins: [],
}

export default config`;
  }

  async generateTsConfig() {
    return `{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}`;
  }

  async generatePostcssConfig() {
    return `export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}`;
  }

  async generateEnvExample(repoConfig) {
    return `# ${repoConfig.name.toUpperCase()} Environment Variables
VITE_APP_NAME="${repoConfig.name}"
VITE_APP_VERSION="1.0.0"
VITE_API_BASE_URL="https://${repoConfig.name}.dev/api"

# Cloudflare
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_ACCOUNT_ID=your_account_id`;
  }

  async generateGitignore() {
    return `# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output

# Production
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Cloudflare
.wrangler/`;
  }

  async generateIndexHtml(repoConfig) {
    return `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="${repoConfig.description}" />
    <title>${repoConfig.name.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ')}</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`;
  }

  async generateMainTsx() {
    return `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`;
  }

  async generateAppTsx(repoConfig) {
    return `import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Header from './components/Header'
import Footer from './components/Footer'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <Header />
        <main>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/demo" element={<DemoPage />} />
            <Route path="/docs" element={<DocsPage />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  )
}

function HomePage() {
  return (
    <div className="container mx-auto px-4 py-16">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-6">
          ${repoConfig.name.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ')}
        </h1>
        <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
          ${repoConfig.description}
        </p>
        <div className="flex gap-4 justify-center">
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
            Try Demo
          </button>
          <button className="border border-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors">
            View Docs
          </button>
        </div>
      </div>
    </div>
  )
}

function DemoPage() {
  return (
    <div className="container mx-auto px-4 py-16">
      <h1 className="text-3xl font-bold text-white mb-8">Demo</h1>
      <div className="bg-gray-800 rounded-lg p-6">
        <p className="text-gray-300">Demo functionality coming soon...</p>
      </div>
    </div>
  )
}

function DocsPage() {
  return (
    <div className="container mx-auto px-4 py-16">
      <h1 className="text-3xl font-bold text-white mb-8">Documentation</h1>
      <div className="bg-gray-800 rounded-lg p-6">
        <p className="text-gray-300">Documentation coming soon...</p>
      </div>
    </div>
  )
}

export default App`;
  }

  async generateIndexCss() {
    return `@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}`;
  }

  async generateUtilsTs() {
    return `import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}`;
  }

  async generateHeaderComponent(repoConfig) {
    return `import React from 'react'
import { Link } from 'react-router-dom'

export default function Header() {
  return (
    <header className="bg-gray-800/50 backdrop-blur-sm border-b border-gray-700">
      <div className="container mx-auto px-4 py-4">
        <nav className="flex justify-between items-center">
          <Link to="/" className="text-xl font-bold text-white">
            ${repoConfig.name.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ')}
          </Link>
          <div className="flex gap-6">
            <Link to="/" className="text-gray-300 hover:text-white transition-colors">
              Home
            </Link>
            <Link to="/demo" className="text-gray-300 hover:text-white transition-colors">
              Demo
            </Link>
            <Link to="/docs" className="text-gray-300 hover:text-white transition-colors">
              Docs
            </Link>
          </div>
        </nav>
      </div>
    </header>
  )
}`;
  }

  async generateFooterComponent() {
    return `import React from 'react'

export default function Footer() {
  return (
    <footer className="bg-gray-800/50 border-t border-gray-700 mt-auto">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center text-gray-400">
          <p>&copy; 2024 Professional Portfolio Project. Built with React + TypeScript + Cloudflare.</p>
          <p className="mt-2">
            <a href="https://portfolio-hub.dev" className="text-blue-400 hover:text-blue-300 transition-colors">
              View Full Portfolio
            </a>
          </p>
        </div>
      </div>
    </footer>
  )
}`;
  }

  async generateLoadingSpinner() {
    return `import React from 'react'

export default function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>
  )
}`;
  }

  async generateWranglerConfig(repoConfig) {
    return `name = "${repoConfig.name}-worker"
main = "src/workers/api.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "${repoConfig.name}-worker"
routes = ["${repoConfig.name}.dev/api/*"]

[[d1_databases]]
binding = "DB"
database_name = "${repoConfig.name}-db"
database_id = "your-database-id"

[[r2_buckets]]
binding = "BUCKET"
bucket_name = "${repoConfig.name}-storage"`;
  }

  async generateWorkerCode(repoConfig) {
    return `import { Hono } from 'hono'
import { cors } from 'hono/cors'

type Bindings = {
  DB: D1Database
  BUCKET: R2Bucket
}

const app = new Hono<{ Bindings: Bindings }>()

app.use('/api/*', cors({
  origin: ['https://${repoConfig.name}.dev', 'http://localhost:3000'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}))

app.get('/api/health', (c) => {
  return c.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: '${repoConfig.name}'
  })
})

export default app`;
  }

  async generateWorkerTypes() {
    return `export interface Bindings {
  DB: D1Database
  BUCKET: R2Bucket
  KV: KVNamespace
}

export interface Environment {
  Bindings: Bindings
}`;
  }

  async generateDeployWorkflow(repoConfig) {
    const template = await fs.readFile(
      path.join(process.cwd(), 'templates', 'github-actions', 'deploy.yml'),
      'utf-8'
    );
    return template.replace(/\{PROJECT_NAME\}/g, repoConfig.name);
  }

  async generateTestWorkflow() {
    return await fs.readFile(
      path.join(process.cwd(), 'templates', 'github-actions', 'test.yml'),
      'utf-8'
    );
  }

  async generateDemoDocumentation(repoConfig) {
    return `# ${repoConfig.name.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ')} - Demo Guide

## Quick Demo

1. Visit the live demo at: https://${repoConfig.name}.dev
2. Explore the main features and functionality
3. Test the interactive components

## Features Demonstration

### Core Functionality
- Feature 1: Description and usage
- Feature 2: Description and usage
- Feature 3: Description and usage

### Technical Highlights
- Modern React architecture
- TypeScript for type safety
- Responsive design
- Performance optimization

## Demo Data

This demo uses realistic sample data to showcase the platform's capabilities without requiring real user accounts or sensitive information.

## Professional Use Cases

This project demonstrates:
- ${repoConfig.framework} implementation
- Scalable architecture patterns
- Professional code organization
- Modern development practices
`;
  }

  async generateArchitectureDoc(repoConfig) {
    return `# ${repoConfig.name.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ')} - Architecture

## Overview

${repoConfig.description}

## Technology Stack

- **Frontend**: ${repoConfig.framework}
- **Deployment**: ${repoConfig.deployment}
- **Type**: ${repoConfig.type}

## Architecture Patterns

### Component Structure
- React functional components with hooks
- TypeScript for type safety
- Modular component organization

### State Management
- Local state with useState/useReducer
- Server state with React Query
- Global state with Zustand (if needed)

### Performance Optimization
- Code splitting with React.lazy
- Bundle optimization with Vite
- Image optimization and lazy loading

## Deployment Architecture

### ${repoConfig.deployment}
- Static site hosting for optimal performance
- Global CDN distribution
- Automatic deployments from GitHub

${repoConfig.type === 'fullstack' ? `
### Cloudflare Workers
- Serverless API endpoints
- Edge computing for low latency
- Database integration with D1
` : ''}

## Security Considerations

- HTTPS enforcement
- Content Security Policy
- Input validation and sanitization
- Environment variable management
`;
  }

  async generateCloudflareSetupGuide() {
    return `# Cloudflare Setup Guide

## Prerequisites

1. Cloudflare account
2. GitHub repositories created
3. Domain names ready (or use .dev domains)

## Setup Steps

### 1. Cloudflare Account Setup

1. Create account at https://cloudflare.com
2. Get API token from https://dash.cloudflare.com/profile/api-tokens
3. Note your Account ID from the dashboard

### 2. Domain Configuration

For each project:
- portfolio-hub.dev
- ai-portfolio-platform.dev
- enterprise-data-pipeline.dev
- predictive-analytics-platform.dev
- cfd-analysis-platform.dev
- realtime-bi-dashboard.dev
- enterprise-llm-solution.dev

### 3. GitHub Secrets Configuration

Add these secrets to each repository:

\`\`\`
CLOUDFLARE_API_TOKEN=your_api_token
CLOUDFLARE_ACCOUNT_ID=your_account_id
\`\`\`

### 4. Cloudflare Pages Setup

For each frontend project:
1. Connect GitHub repository
2. Set build command: \`npm run build\`
3. Set build output directory: \`dist\`
4. Configure custom domain

### 5. Cloudflare Workers Setup

For fullstack projects:
1. Create D1 database
2. Create R2 bucket
3. Deploy worker with \`wrangler deploy\`

## Environment Variables

Each project needs:
- \`CLOUDFLARE_API_TOKEN\`
- \`CLOUDFLARE_ACCOUNT_ID\`
- Project-specific variables

## Monitoring Setup

1. Enable Web Analytics
2. Configure uptime monitoring
3. Set up error tracking

## Next Steps

1. Deploy all projects
2. Test live functionality
3. Configure monitoring
4. Update portfolio hub with live links
`;
  }

  async generateWranglerConfigs() {
    const configs = [];
    
    for (const repo of REPOSITORIES.filter(r => r.type === 'fullstack')) {
      configs.push({
        project: repo.name,
        config: await this.generateWranglerConfig(repo)
      });
    }
    
    return configs;
  }
}

// CLI functionality
async function main() {
  const githubToken = process.env.GITHUB_TOKEN || process.argv[2];
  const githubUsername = process.env.GITHUB_USERNAME || process.argv[3];

  if (!githubToken) {
    console.error('❌ GitHub token required. Set GITHUB_TOKEN environment variable or pass as argument.');
    console.error('   Example: GITHUB_TOKEN=ghp_xxx npm run setup');
    process.exit(1);
  }

  if (!githubUsername) {
    console.error('❌ GitHub username required. Set GITHUB_USERNAME environment variable or pass as argument.');
    console.error('   Example: GITHUB_USERNAME=yourusername npm run setup');
    process.exit(1);
  }

  const coordinator = new PortfolioSetupCoordinator(githubToken, githubUsername);
  
  try {
    await coordinator.runCompleteSetup();
    process.exit(0);
  } catch (error) {
    process.exit(1);
  }
}

// Export for use as module
export { PortfolioSetupCoordinator };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}