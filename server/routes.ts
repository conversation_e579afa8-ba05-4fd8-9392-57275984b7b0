import type { Express } from "express";
import { createServer, type Server } from "http";
import { z } from "zod";
import { storage } from "./storage";
import { 
  insertContactSchema,
  insertBlogPostSchema,
  insertProjectSchema,
  insertAIPlaygroundItemSchema,
  insertConsultingServiceSchema 
} from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  // Contact form submission
  app.post("/api/contact", async (req, res) => {
    try {
      const contactData = insertContactSchema.parse(req.body);
      const contact = await storage.createContact(contactData);
      res.json({ success: true, contact });
    } catch (error) {
      console.error("Contact form error:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ 
          error: "Validation failed", 
          details: error.errors 
        });
      } else {
        res.status(500).json({ 
          error: "Failed to submit contact form" 
        });
      }
    }
  });

  // Get all contacts (for admin purposes)
  app.get("/api/contacts", async (req, res) => {
    try {
      const contacts = await storage.getContacts();
      res.json(contacts);
    } catch (error) {
      console.error("Get contacts error:", error);
      res.status(500).json({ 
        error: "Failed to fetch contacts" 
      });
    }
  });

  // Blog Posts Routes
  app.get("/api/blog-posts", async (req, res) => {
    try {
      const posts = await storage.getBlogPosts();
      const publishedPosts = posts.filter(post => post.published === "true");
      res.json(publishedPosts);
    } catch (error) {
      console.error("Get blog posts error:", error);
      res.status(500).json({ error: "Failed to fetch blog posts" });
    }
  });

  app.get("/api/blog-posts/:slug", async (req, res) => {
    try {
      const post = await storage.getBlogPostBySlug(req.params.slug);
      if (!post) {
        return res.status(404).json({ error: "Blog post not found" });
      }
      res.json(post);
    } catch (error) {
      console.error("Get blog post error:", error);
      res.status(500).json({ error: "Failed to fetch blog post" });
    }
  });

  app.post("/api/blog-posts", async (req, res) => {
    try {
      const postData = insertBlogPostSchema.parse(req.body);
      const post = await storage.createBlogPost(postData);
      res.json({ success: true, post });
    } catch (error) {
      console.error("Create blog post error:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Validation failed", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to create blog post" });
      }
    }
  });

  app.put("/api/blog-posts/:id", async (req, res) => {
    try {
      const updateData = insertBlogPostSchema.partial().parse(req.body);
      const post = await storage.updateBlogPost(req.params.id, updateData);
      if (!post) {
        return res.status(404).json({ error: "Blog post not found" });
      }
      res.json({ success: true, post });
    } catch (error) {
      console.error("Update blog post error:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Validation failed", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to update blog post" });
      }
    }
  });

  app.delete("/api/blog-posts/:id", async (req, res) => {
    try {
      const success = await storage.deleteBlogPost(req.params.id);
      if (!success) {
        return res.status(404).json({ error: "Blog post not found" });
      }
      res.json({ success: true });
    } catch (error) {
      console.error("Delete blog post error:", error);
      res.status(500).json({ error: "Failed to delete blog post" });
    }
  });

  // Projects Routes
  app.get("/api/projects", async (req, res) => {
    try {
      const projects = await storage.getProjects();
      res.json(projects);
    } catch (error) {
      console.error("Get projects error:", error);
      res.status(500).json({ error: "Failed to fetch projects" });
    }
  });

  app.get("/api/projects/:slug", async (req, res) => {
    try {
      const project = await storage.getProjectBySlug(req.params.slug);
      if (!project) {
        return res.status(404).json({ error: "Project not found" });
      }
      res.json(project);
    } catch (error) {
      console.error("Get project error:", error);
      res.status(500).json({ error: "Failed to fetch project" });
    }
  });

  app.post("/api/projects", async (req, res) => {
    try {
      const projectData = insertProjectSchema.parse(req.body);
      const project = await storage.createProject(projectData);
      res.json({ success: true, project });
    } catch (error) {
      console.error("Create project error:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Validation failed", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to create project" });
      }
    }
  });

  app.put("/api/projects/:id", async (req, res) => {
    try {
      const updateData = insertProjectSchema.partial().parse(req.body);
      const project = await storage.updateProject(req.params.id, updateData);
      if (!project) {
        return res.status(404).json({ error: "Project not found" });
      }
      res.json({ success: true, project });
    } catch (error) {
      console.error("Update project error:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Validation failed", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to update project" });
      }
    }
  });

  app.delete("/api/projects/:id", async (req, res) => {
    try {
      const success = await storage.deleteProject(req.params.id);
      if (!success) {
        return res.status(404).json({ error: "Project not found" });
      }
      res.json({ success: true });
    } catch (error) {
      console.error("Delete project error:", error);
      res.status(500).json({ error: "Failed to delete project" });
    }
  });

  // AI Playground Routes
  app.get("/api/ai-playground", async (req, res) => {
    try {
      const items = await storage.getAIPlaygroundItems();
      const activeItems = items.filter(item => item.active === "true");
      res.json(activeItems);
    } catch (error) {
      console.error("Get AI playground items error:", error);
      res.status(500).json({ error: "Failed to fetch AI playground items" });
    }
  });

  app.get("/api/ai-playground/:slug", async (req, res) => {
    try {
      const item = await storage.getAIPlaygroundItemBySlug(req.params.slug);
      if (!item) {
        return res.status(404).json({ error: "AI playground item not found" });
      }
      res.json(item);
    } catch (error) {
      console.error("Get AI playground item error:", error);
      res.status(500).json({ error: "Failed to fetch AI playground item" });
    }
  });

  app.post("/api/ai-playground", async (req, res) => {
    try {
      const itemData = insertAIPlaygroundItemSchema.parse(req.body);
      const item = await storage.createAIPlaygroundItem(itemData);
      res.json({ success: true, item });
    } catch (error) {
      console.error("Create AI playground item error:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Validation failed", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to create AI playground item" });
      }
    }
  });

  app.put("/api/ai-playground/:id", async (req, res) => {
    try {
      const updateData = insertAIPlaygroundItemSchema.partial().parse(req.body);
      const item = await storage.updateAIPlaygroundItem(req.params.id, updateData);
      if (!item) {
        return res.status(404).json({ error: "AI playground item not found" });
      }
      res.json({ success: true, item });
    } catch (error) {
      console.error("Update AI playground item error:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Validation failed", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to update AI playground item" });
      }
    }
  });

  app.delete("/api/ai-playground/:id", async (req, res) => {
    try {
      const success = await storage.deleteAIPlaygroundItem(req.params.id);
      if (!success) {
        return res.status(404).json({ error: "AI playground item not found" });
      }
      res.json({ success: true });
    } catch (error) {
      console.error("Delete AI playground item error:", error);
      res.status(500).json({ error: "Failed to delete AI playground item" });
    }
  });

  // Consulting Services Routes
  app.get("/api/consulting-services", async (req, res) => {
    try {
      const services = await storage.getConsultingServices();
      const activeServices = services.filter(service => service.active === "true");
      res.json(activeServices);
    } catch (error) {
      console.error("Get consulting services error:", error);
      res.status(500).json({ error: "Failed to fetch consulting services" });
    }
  });

  app.get("/api/consulting-services/:slug", async (req, res) => {
    try {
      const service = await storage.getConsultingServiceBySlug(req.params.slug);
      if (!service) {
        return res.status(404).json({ error: "Consulting service not found" });
      }
      res.json(service);
    } catch (error) {
      console.error("Get consulting service error:", error);
      res.status(500).json({ error: "Failed to fetch consulting service" });
    }
  });

  app.post("/api/consulting-services", async (req, res) => {
    try {
      const serviceData = insertConsultingServiceSchema.parse(req.body);
      const service = await storage.createConsultingService(serviceData);
      res.json({ success: true, service });
    } catch (error) {
      console.error("Create consulting service error:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Validation failed", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to create consulting service" });
      }
    }
  });

  app.put("/api/consulting-services/:id", async (req, res) => {
    try {
      const updateData = insertConsultingServiceSchema.partial().parse(req.body);
      const service = await storage.updateConsultingService(req.params.id, updateData);
      if (!service) {
        return res.status(404).json({ error: "Consulting service not found" });
      }
      res.json({ success: true, service });
    } catch (error) {
      console.error("Update consulting service error:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Validation failed", details: error.errors });
      } else {
        res.status(500).json({ error: "Failed to update consulting service" });
      }
    }
  });

  app.delete("/api/consulting-services/:id", async (req, res) => {
    try {
      const success = await storage.deleteConsultingService(req.params.id);
      if (!success) {
        return res.status(404).json({ error: "Consulting service not found" });
      }
      res.json({ success: true });
    } catch (error) {
      console.error("Delete consulting service error:", error);
      res.status(500).json({ error: "Failed to delete consulting service" });
    }
  });

  // Admin Routes (all content for admin dashboard)
  app.get("/api/admin/blog-posts", async (req, res) => {
    try {
      const posts = await storage.getBlogPosts();
      res.json(posts);
    } catch (error) {
      console.error("Get admin blog posts error:", error);
      res.status(500).json({ error: "Failed to fetch blog posts" });
    }
  });

  app.get("/api/admin/projects", async (req, res) => {
    try {
      const projects = await storage.getProjects();
      res.json(projects);
    } catch (error) {
      console.error("Get admin projects error:", error);
      res.status(500).json({ error: "Failed to fetch projects" });
    }
  });

  app.get("/api/admin/ai-playground", async (req, res) => {
    try {
      const items = await storage.getAIPlaygroundItems();
      res.json(items);
    } catch (error) {
      console.error("Get admin AI playground items error:", error);
      res.status(500).json({ error: "Failed to fetch AI playground items" });
    }
  });

  app.get("/api/admin/consulting-services", async (req, res) => {
    try {
      const services = await storage.getConsultingServices();
      res.json(services);
    } catch (error) {
      console.error("Get admin consulting services error:", error);
      res.status(500).json({ error: "Failed to fetch consulting services" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
