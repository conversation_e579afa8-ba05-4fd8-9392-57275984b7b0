import { storage } from "../storage";
import type { InsertProject } from "@shared/schema";

export async function seedProjects() {
  const projects: InsertProject[] = [
    {
      title: "AI-Powered Portfolio Platform",
      slug: "ai-portfolio-platform",
      description: "Next.js 14 portfolio with integrated AI playground, featuring FastAPI backend and modern architecture",
      content: `
        <h2>Project Overview</h2>
        <p>This AI-powered portfolio platform represents a cutting-edge approach to personal branding and project showcasing. Built with Next.js 14 and featuring an integrated AI playground, it demonstrates advanced web development skills combined with machine learning capabilities.</p>

        <h3>Key Features</h3>
        <ul>
          <li>Interactive AI playground with multiple demo scenarios</li>
          <li>Real-time data visualization and analytics</li>
          <li>Responsive design with smooth animations</li>
          <li>FastAPI backend with PostgreSQL database</li>
          <li>Modern CI/CD pipeline with automated testing</li>
        </ul>

        <h3>Technical Implementation</h3>
        <p>The platform utilizes a microservices architecture with containerized deployment. The frontend is built with React and TypeScript, while the backend leverages Python's FastAPI framework for high-performance API endpoints.</p>

        <h3>Performance Metrics</h3>
        <ul>
          <li>Page load time: &lt;2 seconds</li>
          <li>API response time: &lt;100ms</li>
          <li>Lighthouse score: 95+</li>
          <li>Mobile responsiveness: 100%</li>
        </ul>
      `,
      techs: ["Next.js", "FastAPI", "AI/ML", "TypeScript", "PostgreSQL"],
      status: "Live",
      statusColor: "tech-purple",
      gradient: "from-tech-purple to-tech-cyan",
      iconName: "Laptop",
      demoUrl: "/projects/ai-portfolio-platform",
      githubUrl: "https://github.com/khiwniti/portfolio-platform",
      featured: "true"
    },
    {
      title: "Enterprise Data Pipeline",
      slug: "enterprise-data-pipeline",
      description: "Scalable Azure Data Factory pipeline processing 1TB+ daily with real-time analytics",
      content: `
        <h2>Project Overview</h2>
        <p>A comprehensive enterprise data pipeline solution built on Azure cloud infrastructure, capable of processing over 1TB of data daily with real-time analytics and monitoring capabilities.</p>

        <h3>Architecture Components</h3>
        <ul>
          <li>Azure Data Factory for ETL orchestration</li>
          <li>Azure Synapse Analytics for data warehousing</li>
          <li>Apache Spark for big data processing</li>
          <li>Power BI for business intelligence</li>
          <li>Azure Monitor for system observability</li>
        </ul>

        <h3>Data Processing Capabilities</h3>
        <p>The pipeline handles multiple data sources including CRM systems, ERP platforms, and external APIs. Data is processed in both batch and streaming modes to ensure near real-time availability for business intelligence.</p>

        <h3>Performance Achievements</h3>
        <ul>
          <li>Data processing throughput: 1.2TB daily</li>
          <li>Pipeline success rate: 99.2%</li>
          <li>Average latency: 2.3 seconds</li>
          <li>Cost optimization: 35% reduction</li>
        </ul>
      `,
      techs: ["Azure ADF", "Synapse", "Apache Spark", "Python", "Power BI"],
      status: "Production",
      statusColor: "tech-cyan",
      gradient: "from-tech-cyan to-primary-600",
      iconName: "Database",
      demoUrl: "/projects/enterprise-data-pipeline",
      githubUrl: "https://github.com/khiwniti/azure-data-pipeline",
      featured: "true"
    },
    {
      title: "Predictive Analytics Platform",
      slug: "predictive-analytics-platform",
      description: "Production ML pipeline with automated retraining and A/B testing for business intelligence",
      content: `
        <h2>Project Overview</h2>
        <p>A sophisticated machine learning platform that provides predictive analytics capabilities for business intelligence. Features automated model retraining, A/B testing, and production-ready MLOps practices.</p>

        <h3>ML Operations Features</h3>
        <ul>
          <li>Automated model retraining pipeline</li>
          <li>A/B testing framework for model validation</li>
          <li>Real-time prediction serving</li>
          <li>Model drift detection and alerting</li>
          <li>Comprehensive model versioning</li>
        </ul>

        <h3>Prediction Capabilities</h3>
        <p>The platform supports multiple prediction scenarios including customer churn analysis, sales forecasting, inventory demand prediction, and price optimization strategies.</p>

        <h3>Technical Stack</h3>
        <ul>
          <li>TensorFlow/Keras for deep learning models</li>
          <li>Kubernetes for container orchestration</li>
          <li>MLflow for experiment tracking</li>
          <li>Apache Kafka for real-time data streaming</li>
          <li>Prometheus/Grafana for monitoring</li>
        </ul>
      `,
      techs: ["TensorFlow", "MLOps", "Kubernetes", "Python", "Apache Kafka"],
      status: "AI/ML",
      statusColor: "tech-purple",
      gradient: "from-tech-purple to-tech-emerald",
      iconName: "Brain",
      demoUrl: "/projects/predictive-analytics-platform",
      githubUrl: "https://github.com/khiwniti/ml-platform",
      featured: "true"
    },
    {
      title: "CFD Analysis Platform",
      slug: "cfd-analysis-platform",
      description: "Web-based CFD simulation tool with ANSYS integration for nuclear reactor thermal analysis",
      content: `
        <h2>Project Overview</h2>
        <p>A specialized web-based computational fluid dynamics (CFD) platform designed for nuclear reactor thermal analysis. Integrates with ANSYS and COMSOL for professional-grade simulation capabilities.</p>

        <h3>Simulation Capabilities</h3>
        <ul>
          <li>Nuclear reactor thermal hydraulics</li>
          <li>Heat transfer analysis</li>
          <li>Fluid flow visualization</li>
          <li>Safety margin calculations</li>
          <li>Transient analysis simulations</li>
        </ul>

        <h3>Integration Features</h3>
        <p>The platform provides seamless integration with industry-standard CFD software while offering a modern web interface for simulation setup, monitoring, and results visualization.</p>

        <h3>Engineering Applications</h3>
        <ul>
          <li>Reactor core thermal analysis</li>
          <li>Steam generator modeling</li>
          <li>Emergency cooling system design</li>
          <li>Thermal stress analysis</li>
          <li>Safety system validation</li>
        </ul>
      `,
      techs: ["ANSYS", "COMSOL", "React", "Python", "OpenFOAM"],
      status: "Engineering",
      statusColor: "tech-orange",
      gradient: "from-tech-orange to-primary-700",
      iconName: "Wind",
      demoUrl: "/projects/cfd-analysis-platform",
      githubUrl: "https://github.com/khiwniti/cfd-platform",
      featured: "false"
    },
    {
      title: "Real-time BI Dashboard",
      slug: "realtime-bi-dashboard",
      description: "Interactive business intelligence platform with real-time data visualization and alerting",
      content: `
        <h2>Project Overview</h2>
        <p>A comprehensive business intelligence dashboard that provides real-time data visualization, automated alerting, and interactive analytics for enterprise decision-making.</p>

        <h3>Dashboard Features</h3>
        <ul>
          <li>Real-time data streaming and visualization</li>
          <li>Customizable KPI monitoring</li>
          <li>Automated alert system</li>
          <li>Interactive chart and graph components</li>
          <li>Export and reporting capabilities</li>
        </ul>

        <h3>Data Source Integration</h3>
        <p>The platform connects to multiple data sources including Google Analytics, Salesforce, HubSpot, and custom APIs to provide a unified view of business metrics.</p>

        <h3>Analytics Capabilities</h3>
        <ul>
          <li>Revenue and sales analytics</li>
          <li>Customer behavior tracking</li>
          <li>Marketing campaign performance</li>
          <li>Operational metrics monitoring</li>
          <li>Predictive trend analysis</li>
        </ul>
      `,
      techs: ["Power BI", "Real-time", "Analytics", "JavaScript", "D3.js"],
      status: "Dashboard",
      statusColor: "tech-emerald",
      gradient: "from-tech-emerald to-tech-cyan",
      iconName: "BarChart",
      demoUrl: "/projects/realtime-bi-dashboard",
      githubUrl: "https://github.com/khiwniti/bi-dashboard",
      featured: "false"
    },
    {
      title: "Enterprise LLM Solution",
      slug: "enterprise-llm-solution",
      description: "Custom LLM application for document analysis and automated report generation",
      content: `
        <h2>Project Overview</h2>
        <p>An enterprise-grade Large Language Model solution designed for document analysis, automated report generation, and intelligent content processing with security and compliance features.</p>

        <h3>LLM Capabilities</h3>
        <ul>
          <li>Multi-document analysis and summarization</li>
          <li>Automated report generation</li>
          <li>Entity recognition and extraction</li>
          <li>Sentiment analysis and classification</li>
          <li>Custom fine-tuning for domain-specific tasks</li>
        </ul>

        <h3>Security & Compliance</h3>
        <p>The solution implements enterprise-grade security measures including data encryption, GDPR compliance, and SOC 2 Type II certification to ensure data privacy and regulatory compliance.</p>

        <h3>Integration Features</h3>
        <ul>
          <li>REST API for seamless integration</li>
          <li>Batch processing capabilities</li>
          <li>Real-time document processing</li>
          <li>Custom workflow automation</li>
          <li>Multi-language support</li>
        </ul>
      `,
      techs: ["OpenAI", "LangChain", "FastAPI", "Python", "Docker"],
      status: "LLM",
      statusColor: "tech-purple",
      gradient: "from-tech-purple via-tech-cyan to-tech-emerald",
      iconName: "MessageSquare",
      demoUrl: "/projects/enterprise-llm-solution",
      githubUrl: "https://github.com/khiwniti/enterprise-llm",
      featured: "true"
    }
  ];

  console.log("Seeding projects...");

  try {
    for (const project of projects) {
      // Check if project already exists
      const existing = await storage.getProjectBySlug(project.slug);
      if (!existing) {
        await storage.createProject(project);
        console.log(`Created project: ${project.title}`);
      } else {
        console.log(`Project already exists: ${project.title}`);
      }
    }
    console.log("Project seeding completed successfully!");
  } catch (error) {
    console.error("Error seeding projects:", error);
  }
}
