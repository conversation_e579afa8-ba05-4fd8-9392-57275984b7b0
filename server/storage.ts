import { 
  type User, type InsertUser,
  type Contact, type InsertContact,
  type BlogPost, type InsertBlogPost,
  type Project, type InsertProject,
  type AIPlaygroundItem, type InsertAIPlaygroundItem,
  type ConsultingService, type InsertConsultingService
} from "@shared/schema";
import { randomUUID } from "crypto";

// modify the interface with any CRUD methods
// you might need

export interface IStorage {
  // User methods
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Contact methods
  getContacts(): Promise<Contact[]>;
  createContact(contact: InsertContact): Promise<Contact>;
  
  // Blog post methods
  getBlogPosts(): Promise<BlogPost[]>;
  getBlogPost(id: string): Promise<BlogPost | undefined>;
  getBlogPostBySlug(slug: string): Promise<BlogPost | undefined>;
  createBlogPost(post: InsertBlogPost): Promise<BlogPost>;
  updateBlogPost(id: string, post: Partial<InsertBlogPost>): Promise<BlogPost | undefined>;
  deleteBlogPost(id: string): Promise<boolean>;
  
  // Project methods
  getProjects(): Promise<Project[]>;
  getProject(id: string): Promise<Project | undefined>;
  getProjectBySlug(slug: string): Promise<Project | undefined>;
  createProject(project: InsertProject): Promise<Project>;
  updateProject(id: string, project: Partial<InsertProject>): Promise<Project | undefined>;
  deleteProject(id: string): Promise<boolean>;
  
  // AI Playground methods
  getAIPlaygroundItems(): Promise<AIPlaygroundItem[]>;
  getAIPlaygroundItem(id: string): Promise<AIPlaygroundItem | undefined>;
  getAIPlaygroundItemBySlug(slug: string): Promise<AIPlaygroundItem | undefined>;
  createAIPlaygroundItem(item: InsertAIPlaygroundItem): Promise<AIPlaygroundItem>;
  updateAIPlaygroundItem(id: string, item: Partial<InsertAIPlaygroundItem>): Promise<AIPlaygroundItem | undefined>;
  deleteAIPlaygroundItem(id: string): Promise<boolean>;
  
  // Consulting Service methods
  getConsultingServices(): Promise<ConsultingService[]>;
  getConsultingService(id: string): Promise<ConsultingService | undefined>;
  getConsultingServiceBySlug(slug: string): Promise<ConsultingService | undefined>;
  createConsultingService(service: InsertConsultingService): Promise<ConsultingService>;
  updateConsultingService(id: string, service: Partial<InsertConsultingService>): Promise<ConsultingService | undefined>;
  deleteConsultingService(id: string): Promise<boolean>;
}

export class MemStorage implements IStorage {
  private users: Map<string, User>;
  private contacts: Map<string, Contact>;
  private blogPosts: Map<string, BlogPost>;
  private projects: Map<string, Project>;
  private aiPlaygroundItems: Map<string, AIPlaygroundItem>;
  private consultingServices: Map<string, ConsultingService>;

  constructor() {
    this.users = new Map();
    this.contacts = new Map();
    this.blogPosts = new Map();
    this.projects = new Map();
    this.aiPlaygroundItems = new Map();
    this.consultingServices = new Map();
  }

  async getUser(id: string): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = randomUUID();
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // Contact methods
  async getContacts(): Promise<Contact[]> {
    return Array.from(this.contacts.values());
  }

  async createContact(insertContact: InsertContact): Promise<Contact> {
    const id = randomUUID();
    const contact: Contact = {
      ...insertContact,
      id,
      company: insertContact.company || null,
      budget: insertContact.budget || null,
      timeline: insertContact.timeline || null,
      createdAt: new Date()
    };
    this.contacts.set(id, contact);
    return contact;
  }

  // Blog post methods
  async getBlogPosts(): Promise<BlogPost[]> {
    return Array.from(this.blogPosts.values());
  }

  async getBlogPost(id: string): Promise<BlogPost | undefined> {
    return this.blogPosts.get(id);
  }

  async getBlogPostBySlug(slug: string): Promise<BlogPost | undefined> {
    return Array.from(this.blogPosts.values()).find(post => post.slug === slug);
  }

  async createBlogPost(insertPost: InsertBlogPost): Promise<BlogPost> {
    const id = randomUUID();
    const now = new Date();
    const post: BlogPost = {
      ...insertPost,
      id,
      imageUrl: insertPost.imageUrl || null,
      published: insertPost.published || "false",
      createdAt: now,
      updatedAt: now
    };
    this.blogPosts.set(id, post);
    return post;
  }

  async updateBlogPost(id: string, updateData: Partial<InsertBlogPost>): Promise<BlogPost | undefined> {
    const existing = this.blogPosts.get(id);
    if (!existing) return undefined;
    const updated: BlogPost = { ...existing, ...updateData, updatedAt: new Date() };
    this.blogPosts.set(id, updated);
    return updated;
  }

  async deleteBlogPost(id: string): Promise<boolean> {
    return this.blogPosts.delete(id);
  }

  // Project methods
  async getProjects(): Promise<Project[]> {
    return Array.from(this.projects.values());
  }

  async getProject(id: string): Promise<Project | undefined> {
    return this.projects.get(id);
  }

  async getProjectBySlug(slug: string): Promise<Project | undefined> {
    return Array.from(this.projects.values()).find(project => project.slug === slug);
  }

  async createProject(insertProject: InsertProject): Promise<Project> {
    const id = randomUUID();
    const now = new Date();
    const project: Project = {
      ...insertProject,
      id,
      demoUrl: insertProject.demoUrl || null,
      githubUrl: insertProject.githubUrl || null,
      imageUrl: insertProject.imageUrl || null,
      featured: insertProject.featured || "false",
      createdAt: now,
      updatedAt: now
    };
    this.projects.set(id, project);
    return project;
  }

  async updateProject(id: string, updateData: Partial<InsertProject>): Promise<Project | undefined> {
    const existing = this.projects.get(id);
    if (!existing) return undefined;
    const updated: Project = { ...existing, ...updateData, updatedAt: new Date() };
    this.projects.set(id, updated);
    return updated;
  }

  async deleteProject(id: string): Promise<boolean> {
    return this.projects.delete(id);
  }

  // AI Playground methods
  async getAIPlaygroundItems(): Promise<AIPlaygroundItem[]> {
    return Array.from(this.aiPlaygroundItems.values());
  }

  async getAIPlaygroundItem(id: string): Promise<AIPlaygroundItem | undefined> {
    return this.aiPlaygroundItems.get(id);
  }

  async getAIPlaygroundItemBySlug(slug: string): Promise<AIPlaygroundItem | undefined> {
    return Array.from(this.aiPlaygroundItems.values()).find(item => item.slug === slug);
  }

  async createAIPlaygroundItem(insertItem: InsertAIPlaygroundItem): Promise<AIPlaygroundItem> {
    const id = randomUUID();
    const now = new Date();
    const item: AIPlaygroundItem = {
      ...insertItem,
      id,
      apiEndpoint: insertItem.apiEndpoint || null,
      configData: insertItem.configData || null,
      active: insertItem.active || "true",
      createdAt: now,
      updatedAt: now
    };
    this.aiPlaygroundItems.set(id, item);
    return item;
  }

  async updateAIPlaygroundItem(id: string, updateData: Partial<InsertAIPlaygroundItem>): Promise<AIPlaygroundItem | undefined> {
    const existing = this.aiPlaygroundItems.get(id);
    if (!existing) return undefined;
    const updated: AIPlaygroundItem = { ...existing, ...updateData, updatedAt: new Date() };
    this.aiPlaygroundItems.set(id, updated);
    return updated;
  }

  async deleteAIPlaygroundItem(id: string): Promise<boolean> {
    return this.aiPlaygroundItems.delete(id);
  }

  // Consulting Service methods
  async getConsultingServices(): Promise<ConsultingService[]> {
    return Array.from(this.consultingServices.values());
  }

  async getConsultingService(id: string): Promise<ConsultingService | undefined> {
    return this.consultingServices.get(id);
  }

  async getConsultingServiceBySlug(slug: string): Promise<ConsultingService | undefined> {
    return Array.from(this.consultingServices.values()).find(service => service.slug === slug);
  }

  async createConsultingService(insertService: InsertConsultingService): Promise<ConsultingService> {
    const id = randomUUID();
    const now = new Date();
    const service: ConsultingService = {
      ...insertService,
      id,
      pricing: insertService.pricing || null,
      active: insertService.active || "true",
      featured: insertService.featured || "false",
      createdAt: now,
      updatedAt: now
    };
    this.consultingServices.set(id, service);
    return service;
  }

  async updateConsultingService(id: string, updateData: Partial<InsertConsultingService>): Promise<ConsultingService | undefined> {
    const existing = this.consultingServices.get(id);
    if (!existing) return undefined;
    const updated: ConsultingService = { ...existing, ...updateData, updatedAt: new Date() };
    this.consultingServices.set(id, updated);
    return updated;
  }

  async deleteConsultingService(id: string): Promise<boolean> {
    return this.consultingServices.delete(id);
  }
}

export const storage = new MemStorage();
