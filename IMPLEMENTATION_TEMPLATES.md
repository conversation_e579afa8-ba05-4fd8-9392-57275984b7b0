# Implementation Templates and Configuration Guide

This document contains all the essential templates and configurations needed to implement the 6 professional-grade projects.

## GitHub Actions Workflow Template

### `.github/workflows/deploy.yml`
```yaml
name: Deploy to Cloudflare

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Build application
        run: npm run build

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'pull_request'
    environment: staging
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Deploy to Cloudflare Pages (Staging)
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: pages deploy dist --project-name=${{ vars.PROJECT_NAME }}-staging

      - name: Deploy Workers (Staging)
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy --env staging

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: pages deploy dist --project-name=${{ vars.PROJECT_NAME }}

      - name: Deploy Workers
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy

      - name: Run D1 Migrations
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: d1 migrations apply ${{ vars.D1_DATABASE_NAME }} --remote
```

## Cloudflare Workers Template (Hono Framework)

### `src/worker.ts`
```typescript
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';

// Types
type Bindings = {
  DB: D1Database;
  BUCKET: R2Bucket;
  KV: KVNamespace;
  AI: any;
};

type Variables = {
  userId?: string;
};

// Initialize Hono app
const app = new Hono<{ Bindings: Bindings; Variables: Variables }>();

// Middleware
app.use('*', logger());
app.use('*', prettyJSON());
app.use('*', secureHeaders());
app.use('/api/*', cors({
  origin: ['https://your-domain.dev', 'http://localhost:5173'],
  allowHeaders: ['Content-Type', 'Authorization'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE'],
}));

// Health check
app.get('/api/health', async (c) => {
  return c.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Database operations
app.get('/api/data', async (c) => {
  try {
    const { results } = await c.env.DB.prepare(
      'SELECT * FROM your_table ORDER BY created_at DESC'
    ).all();
    
    return c.json({ data: results });
  } catch (error) {
    console.error('Database error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

app.post('/api/data', async (c) => {
  try {
    const body = await c.req.json();
    
    // Validate input
    if (!body.name || !body.value) {
      return c.json({ error: 'Missing required fields' }, 400);
    }
    
    const { success } = await c.env.DB.prepare(
      'INSERT INTO your_table (name, value, created_at) VALUES (?, ?, ?)'
    ).bind(body.name, body.value, new Date().toISOString()).run();
    
    if (success) {
      return c.json({ message: 'Data created successfully' }, 201);
    } else {
      return c.json({ error: 'Failed to create data' }, 500);
    }
  } catch (error) {
    console.error('API error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// File upload to R2
app.post('/api/upload', async (c) => {
  try {
    const formData = await c.req.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return c.json({ error: 'No file provided' }, 400);
    }
    
    const key = `uploads/${Date.now()}-${file.name}`;
    await c.env.BUCKET.put(key, file.stream());
    
    return c.json({ 
      message: 'File uploaded successfully',
      key,
      url: `https://your-bucket.your-account.r2.cloudflarestorage.com/${key}`
    });
  } catch (error) {
    console.error('Upload error:', error);
    return c.json({ error: 'Upload failed' }, 500);
  }
});

// KV operations
app.get('/api/cache/:key', async (c) => {
  const key = c.req.param('key');
  const value = await c.env.KV.get(key);
  
  if (value === null) {
    return c.json({ error: 'Key not found' }, 404);
  }
  
  return c.json({ key, value: JSON.parse(value) });
});

app.put('/api/cache/:key', async (c) => {
  const key = c.req.param('key');
  const body = await c.req.json();
  
  await c.env.KV.put(key, JSON.stringify(body), {
    expirationTtl: 3600 // 1 hour
  });
  
  return c.json({ message: 'Cache updated successfully' });
});

// Error handling
app.onError((err, c) => {
  console.error('Unhandled error:', err);
  return c.json({ error: 'Internal server error' }, 500);
});

// 404 handler
app.notFound((c) => {
  return c.json({ error: 'Route not found' }, 404);
});

export default app;
```

## Wrangler Configuration Template

### `wrangler.toml`
```toml
name = "project-name"
main = "src/worker.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[env.staging]
name = "project-name-staging"
vars = { ENVIRONMENT = "staging" }

[env.production]
name = "project-name"
vars = { ENVIRONMENT = "production" }

# D1 Database
[[d1_databases]]
binding = "DB"
database_name = "project-name-db"
database_id = "your-database-id"
migrations_dir = "migrations"

# R2 Storage
[[r2_buckets]]
binding = "BUCKET"
bucket_name = "project-name-storage"

# KV Namespace
[[kv_namespaces]]
binding = "KV"
id = "your-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"

# AI Workers (if needed)
[ai]
binding = "AI"

# Analytics Engine (if needed)
[[analytics_engine_datasets]]
binding = "ANALYTICS"
dataset = "project-name-analytics"
```

## D1 Database Schema Templates

### AI Portfolio Platform
```sql
-- migrations/0001_initial.sql
CREATE TABLE IF NOT EXISTS portfolios (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  template_id TEXT,
  settings JSON,
  is_public BOOLEAN DEFAULT false,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS portfolio_sections (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  portfolio_id INTEGER NOT NULL,
  section_type TEXT NOT NULL,
  title TEXT,
  content JSON,
  order_index INTEGER DEFAULT 0,
  is_visible BOOLEAN DEFAULT true,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (portfolio_id) REFERENCES portfolios (id) ON DELETE CASCADE
);

CREATE INDEX idx_portfolios_user_id ON portfolios(user_id);
CREATE INDEX idx_portfolio_sections_portfolio_id ON portfolio_sections(portfolio_id);
```

### Enterprise Data Pipeline
```sql
-- migrations/0001_initial.sql
CREATE TABLE IF NOT EXISTS pipelines (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  source_config JSON,
  transformation_config JSON,
  destination_config JSON,
  schedule_config JSON,
  status TEXT DEFAULT 'inactive',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS pipeline_runs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  pipeline_id INTEGER NOT NULL,
  status TEXT DEFAULT 'running',
  started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  completed_at DATETIME,
  records_processed INTEGER DEFAULT 0,
  errors_count INTEGER DEFAULT 0,
  metrics JSON,
  FOREIGN KEY (pipeline_id) REFERENCES pipelines (id) ON DELETE CASCADE
);

CREATE INDEX idx_pipeline_runs_pipeline_id ON pipeline_runs(pipeline_id);
CREATE INDEX idx_pipeline_runs_status ON pipeline_runs(status);
```

### Predictive Analytics Platform
```sql
-- migrations/0001_initial.sql
CREATE TABLE IF NOT EXISTS models (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  algorithm TEXT,
  parameters JSON,
  training_data_hash TEXT,
  accuracy_score REAL,
  status TEXT DEFAULT 'draft',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS predictions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  model_id INTEGER NOT NULL,
  input_data JSON,
  prediction_result JSON,
  confidence_score REAL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (model_id) REFERENCES models (id) ON DELETE CASCADE
);

CREATE INDEX idx_predictions_model_id ON predictions(model_id);
CREATE INDEX idx_predictions_created_at ON predictions(created_at);
```

## Package.json Template

### `package.json`
```json
{
  "name": "project-name",
  "version": "1.0.0",
  "description": "Professional project description",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "dev:worker": "wrangler dev",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "vitest",
    "test:coverage": "vitest --coverage",
    "test:e2e": "playwright test",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext ts,tsx --fix",
    "type-check": "tsc --noEmit",
    "deploy": "wrangler deploy",
    "db:generate": "wrangler d1 migrations create",
    "db:migrate": "wrangler d1 migrations apply",
    "db:studio": "wrangler d1 migrations list"
  },
  "dependencies": {
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "hono": "^4.6.3",
    "@cloudflare/workers-types": "^4.20241218.0",
    "tailwindcss": "^3.4.17",
    "framer-motion": "^11.13.1",
    "lucide-react": "^0.453.0",
    "@radix-ui/react-slot": "^1.2.0",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "tailwind-merge": "^2.6.0"
  },
  "devDependencies": {
    "@types/react": "^18.3.11",
    "@types/react-dom": "^18.3.1",
    "@vitejs/plugin-react": "^4.3.2",
    "typescript": "^5.6.3",
    "vite": "^5.4.19",
    "wrangler": "^3.84.0",
    "vitest": "^2.1.8",
    "@playwright/test": "^1.49.1",
    "eslint": "^9.15.0",
    "@typescript-eslint/eslint-plugin": "^8.15.0",
    "@typescript-eslint/parser": "^8.15.0"
  }
}
```

## Environment Configuration Template

### `.env.example`
```env
# Cloudflare Configuration
CLOUDFLARE_API_TOKEN=your_api_token_here
CLOUDFLARE_ACCOUNT_ID=your_account_id_here

# Database
DATABASE_URL=your_d1_database_url_here

# External APIs
OPENAI_API_KEY=your_openai_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# Application Settings
NODE_ENV=development
APP_URL=http://localhost:5173
API_URL=http://localhost:8787

# Analytics
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
POSTHOG_API_KEY=your_posthog_key_here

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_ANALYTICS=true
ENABLE_MONITORING=true
```

## TypeScript Configuration Template

### `tsconfig.json`
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/types/*": ["./src/types/*"]
    },
    "types": ["@cloudflare/workers-types", "vite/client"]
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

## Vite Configuration Template

### `vite.config.ts`
```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-slot', 'lucide-react'],
        },
      },
    },
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8787',
        changeOrigin: true,
      },
    },
  },
});
```

## Project-Specific Configuration Examples

### AI Portfolio Platform - Additional Dependencies
```json
{
  "dependencies": {
    "@ai-sdk/openai": "^0.0.66",
    "ai": "^4.0.25",
    "react-hook-form": "^7.55.0",
    "@hookform/resolvers": "^3.10.0",
    "zod": "^3.24.2"
  }
}
```

### Enterprise Data Pipeline - Worker Cron Jobs
```toml
# Additional wrangler.toml configuration
[[triggers]]
crons = ["0 */6 * * *"]  # Run every 6 hours

[env.production.triggers]
crons = ["0 2 * * *"]    # Run daily at 2 AM in production
```

### CFD Analysis Platform - WebAssembly Support
```typescript
// vite.config.ts additions
export default defineConfig({
  // ... other config
  optimizeDeps: {
    exclude: ['@tensorflow/tfjs-node'],
  },
  worker: {
    format: 'es',
  },
});
```

## Security Configuration Template

### Content Security Policy (CSP)
```javascript
// In worker.ts
app.use('*', secureHeaders({
  contentSecurityPolicy: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
    styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
    fontSrc: ["'self'", "https://fonts.gstatic.com"],
    imgSrc: ["'self'", "data:", "https:"],
    connectSrc: ["'self'", "https://api.openai.com"],
    frameSrc: ["'none'"],
    objectSrc: ["'none'"],
    upgradeInsecureRequests: true,
  },
}));
```

This comprehensive template collection provides everything needed to implement the 6 professional-grade projects with consistent architecture, security, and deployment strategies.