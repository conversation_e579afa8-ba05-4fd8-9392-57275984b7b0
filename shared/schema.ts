import { sql } from "drizzle-orm";
import { pgTable, text, varchar, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const contacts = pgTable("contacts", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  name: text("name").notNull(),
  email: text("email").notNull(),
  company: text("company"),
  projectType: text("project_type").notNull(),
  budget: text("budget"),
  description: text("description").notNull(),
  timeline: text("timeline"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const blogPosts = pgTable("blog_posts", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  title: text("title").notNull(),
  slug: text("slug").notNull().unique(),
  description: text("description").notNull(),
  content: text("content").notNull(),
  author: text("author").notNull(),
  readTime: text("read_time").notNull(),
  tags: text("tags").array().notNull(),
  category: text("category").notNull(),
  imageUrl: text("image_url"),
  published: text("published").notNull().default("false"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const projects = pgTable("projects", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  title: text("title").notNull(),
  slug: text("slug").notNull().unique(),
  description: text("description").notNull(),
  content: text("content").notNull(),
  techs: text("techs").array().notNull(),
  status: text("status").notNull(),
  statusColor: text("status_color").notNull(),
  gradient: text("gradient").notNull(),
  iconName: text("icon_name").notNull(),
  demoUrl: text("demo_url"),
  githubUrl: text("github_url"),
  imageUrl: text("image_url"),
  featured: text("featured").notNull().default("false"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const aiPlaygroundItems = pgTable("ai_playground_items", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  title: text("title").notNull(),
  slug: text("slug").notNull().unique(),
  description: text("description").notNull(),
  content: text("content").notNull(),
  iconName: text("icon_name").notNull(),
  color: text("color").notNull(),
  demoType: text("demo_type").notNull(),
  apiEndpoint: text("api_endpoint"),
  configData: text("config_data"),
  active: text("active").notNull().default("true"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const consultingServices = pgTable("consulting_services", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  title: text("title").notNull(),
  slug: text("slug").notNull().unique(),
  description: text("description").notNull(),
  content: text("content").notNull(),
  category: text("category").notNull(),
  iconName: text("icon_name").notNull(),
  color: text("color").notNull(),
  pricing: text("pricing"),
  features: text("features").array().notNull(),
  technologies: text("technologies").array().notNull(),
  active: text("active").notNull().default("true"),
  featured: text("featured").notNull().default("false"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
});

export const insertContactSchema = createInsertSchema(contacts).omit({
  id: true,
  createdAt: true,
});

export const insertBlogPostSchema = createInsertSchema(blogPosts).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertProjectSchema = createInsertSchema(projects).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertAIPlaygroundItemSchema = createInsertSchema(aiPlaygroundItems).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertConsultingServiceSchema = createInsertSchema(consultingServices).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertContact = z.infer<typeof insertContactSchema>;
export type Contact = typeof contacts.$inferSelect;
export type InsertBlogPost = z.infer<typeof insertBlogPostSchema>;
export type BlogPost = typeof blogPosts.$inferSelect;
export type InsertProject = z.infer<typeof insertProjectSchema>;
export type Project = typeof projects.$inferSelect;
export type InsertAIPlaygroundItem = z.infer<typeof insertAIPlaygroundItemSchema>;
export type AIPlaygroundItem = typeof aiPlaygroundItems.$inferSelect;
export type InsertConsultingService = z.infer<typeof insertConsultingServiceSchema>;
export type ConsultingService = typeof consultingServices.$inferSelect;
