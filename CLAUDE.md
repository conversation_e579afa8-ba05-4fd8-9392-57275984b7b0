# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Portfolio Website Project

This is a professional portfolio website for Khiw (Ikkyu) Nitithadachot - a modern React/TypeScript application showcasing AI, data engineering, and full-stack development expertise.

## 🚀 Development Commands

| Task | Command | Notes |
|------|---------|-------|
| **Development** | `npm run dev` | Start dev server with hot reload |
| **Build** | `npm run build` | Build for production |
| **Start Production** | `npm run start` | Run production server |
| **Type Check** | `npm run check` | Run TypeScript compiler |
| **Database Push** | `npm run db:push` | Push schema changes to database |

## 🏗️ Architecture Overview

### Full-Stack Structure
- **Frontend**: React 18 + TypeScript + Vite
- **Backend**: Express.js with TypeScript (ES modules)
- **Database**: PostgreSQL with Drizzle ORM
- **UI Framework**: Tailwind CSS + Radix UI + shadcn/ui
- **State Management**: TanStack React Query
- **Routing**: Wouter (lightweight client-side routing)

### Project Structure
```
client/                 # Frontend React application
├── src/
│   ├── components/     # React components
│   ├── pages/         # Route components
│   ├── hooks/         # Custom React hooks
│   └── lib/           # Utilities and config
server/                # Backend Express application
├── index.ts           # Server entry point
├── routes.ts          # API route definitions
├── storage.ts         # Database operations
└── vite.ts           # Vite dev middleware
shared/                # Shared TypeScript types
└── schema.ts          # Database schema + Zod validations
```

### Development vs Production
- **Development**: Vite serves frontend with HMR, Express handles API
- **Production**: Express serves built static files + API from single server
- **Port**: Always uses `PORT` environment variable (default 5000)

## 📊 Database Architecture

### Core Entities
- **users**: Admin authentication
- **contacts**: Contact form submissions
- **blogPosts**: Blog content with publish status
- **projects**: Portfolio projects with featured flag
- **aiPlaygroundItems**: Interactive AI demos
- **consultingServices**: Service offerings

### Database Patterns
- All tables use UUID primary keys with `gen_random_uuid()`
- Timestamps with `defaultNow()` for creation/update tracking
- Text arrays for tags, technologies, and features
- String-based flags for boolean-like fields (published, featured, active)

### Schema Management
- **Schema Definition**: `shared/schema.ts` contains Drizzle schema + Zod validation
- **Migrations**: Use `npm run db:push` to sync schema changes
- **Type Safety**: Shared types between frontend and backend via `@shared` alias

## 🔧 Key Development Patterns

### API Design
- RESTful endpoints in `server/routes.ts`
- Zod validation for all input data
- Consistent error handling with proper HTTP status codes
- Separate admin endpoints for management functionality

### Component Architecture
- **shadcn/ui**: Pre-built accessible components in `components/ui/`
- **Feature Components**: Main page sections in `components/`
- **Pages**: Route-level components in `pages/`
- **Theme System**: Custom provider with light/dark mode support

### State Management
- **Server State**: TanStack React Query for API data
- **Client State**: React hooks and context for UI state
- **Forms**: React Hook Form with Zod validation

### Styling Approach
- **Tailwind CSS**: Utility-first styling with custom configuration
- **Design System**: Consistent spacing, colors, and typography
- **Responsive**: Mobile-first design with Tailwind breakpoints
- **Dark Mode**: Theme-aware components with CSS custom properties

## 🛠️ File Path Aliases

```typescript
"@/*": ["./client/src/*"]      # Frontend source files
"@shared/*": ["./shared/*"]    # Shared types and schemas
"@assets/*": ["./attached_assets/*"] # Static assets
```

## 🔍 Common Development Tasks

### Adding New Content Types
1. Define schema in `shared/schema.ts`
2. Add CRUD operations in `server/storage.ts`
3. Create API routes in `server/routes.ts`
4. Build frontend components and queries

### Database Schema Changes
1. Modify schema in `shared/schema.ts`
2. Update Zod validation schemas
3. Run `npm run db:push` to sync database
4. Update TypeScript types are auto-generated

### Adding New Pages
1. Create component in `client/src/pages/`
2. Add route in `client/src/App.tsx`
3. Implement navigation if needed

### Environment Setup
- **DATABASE_URL**: Required for database connection
- **NODE_ENV**: Controls development vs production behavior
- **PORT**: Server port (default 5000, required for deployment)

## 🚨 Important Notes

### Deployment Considerations
- Single server handles both API and static files in production
- Must use PORT environment variable for hosting compatibility
- Database URL must be configured for PostgreSQL connection

### Code Quality
- TypeScript strict mode enabled
- Shared validation schemas between client/server
- Consistent error handling patterns
- Mobile-first responsive design

### Security
- Input validation on both client and server
- Proper error messages without sensitive information leakage
- Environment variable protection for database credentials