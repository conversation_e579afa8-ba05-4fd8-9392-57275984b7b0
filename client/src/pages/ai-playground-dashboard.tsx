import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useLocation } from 'wouter';
import {
  Brain,
  Database,
  TrendingUp,
  Wind,
  BarChart,
  Cpu,
  ArrowRight,
  Sparkles,
  Zap,
  Target,
  Globe,
  Users,
  Settings,
  Activity
} from 'lucide-react';

export default function AIPlaygroundDashboard() {
  const [, setLocation] = useLocation();

  const aiProjects = [
    {
      id: 'ai-portfolio-platform',
      title: 'AI Portfolio Platform',
      description: 'Intelligent portfolio optimization using machine learning algorithms for personalized investment strategies',
      icon: <Brain className="w-8 h-8" />,
      color: 'tech-purple',
      bgColor: 'bg-purple-500/10',
      status: 'Active',
      accuracy: '94.2%',
      features: ['Portfolio Optimization', 'Risk Analysis', 'ML Predictions', 'Real-time Alerts'],
      stats: {
        models: 12,
        accuracy: 94.2,
        uptime: '99.8%',
        throughput: '1.2M/day'
      }
    },
    {
      id: 'enterprise-data-pipeline',
      title: 'Enterprise Data Pipeline',
      description: 'Scalable data processing pipeline with automated ML model training and real-time analytics',
      icon: <Database className="w-8 h-8" />,
      color: 'tech-cyan',
      bgColor: 'bg-cyan-500/10',
      status: 'Processing',
      accuracy: '97.8%',
      features: ['Data Ingestion', 'Auto ML Training', 'Stream Processing', 'Data Quality'],
      stats: {
        models: 8,
        accuracy: 97.8,
        uptime: '99.9%',
        throughput: '5.2M/day'
      }
    },
    {
      id: 'predictive-analytics-platform',
      title: 'Predictive Analytics Platform',
      description: 'Advanced forecasting system with ensemble models for business intelligence and strategic planning',
      icon: <TrendingUp className="w-8 h-8" />,
      color: 'tech-emerald',
      bgColor: 'bg-emerald-500/10',
      status: 'Training',
      accuracy: '91.7%',
      features: ['Time Series Forecasting', 'Anomaly Detection', 'Business Intelligence', 'Custom Dashboards'],
      stats: {
        models: 15,
        accuracy: 91.7,
        uptime: '99.5%',
        throughput: '800K/day'
      }
    },
    {
      id: 'cfd-analysis-platform',
      title: 'CFD Analysis Platform',
      description: 'Computational fluid dynamics simulation with AI-accelerated solver and optimization algorithms',
      icon: <Wind className="w-8 h-8" />,
      color: 'tech-orange',
      bgColor: 'bg-orange-500/10',
      status: 'Simulating',
      accuracy: '96.3%',
      features: ['CFD Simulation', 'AI Optimization', 'Mesh Generation', 'Result Visualization'],
      stats: {
        models: 6,
        accuracy: 96.3,
        uptime: '98.7%',
        throughput: '200K/day'
      }
    },
    {
      id: 'realtime-bi-dashboard',
      title: 'Real-time BI Dashboard',
      description: 'Live business intelligence with streaming analytics and automated insight generation',
      icon: <BarChart className="w-8 h-8" />,
      color: 'tech-pink',
      bgColor: 'bg-pink-500/10',
      status: 'Live',
      accuracy: '93.5%',
      features: ['Real-time Analytics', 'Automated Insights', 'Interactive Dashboards', 'Alert System'],
      stats: {
        models: 10,
        accuracy: 93.5,
        uptime: '99.7%',
        throughput: '3.1M/day'
      }
    },
    {
      id: 'enterprise-llm-solution',
      title: 'Enterprise LLM Solution',
      description: 'Large language model deployment with fine-tuning capabilities for enterprise applications',
      icon: <Cpu className="w-8 h-8" />,
      color: 'tech-blue',
      bgColor: 'bg-blue-500/10',
      status: 'Fine-tuning',
      accuracy: '89.4%',
      features: ['Custom LLM Training', 'API Integration', 'Content Generation', 'Knowledge Base'],
      stats: {
        models: 4,
        accuracy: 89.4,
        uptime: '99.2%',
        throughput: '600K/day'
      }
    }
  ];

  const overallStats = {
    totalModels: aiProjects.reduce((sum, project) => sum + project.stats.models, 0),
    avgAccuracy: aiProjects.reduce((sum, project) => sum + project.stats.accuracy, 0) / aiProjects.length,
    totalThroughput: '11.1M/day',
    systemUptime: '99.6%'
  };

  const handleViewProject = (projectId: string) => {
    setLocation(`/ai-playground/${projectId}`);
  };

  const handleLearnMore = (projectId: string) => {
    setLocation(`/ai-playground/${projectId}/explanation`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-500';
      case 'Processing': return 'bg-blue-500';
      case 'Training': return 'bg-yellow-500';
      case 'Simulating': return 'bg-purple-500';
      case 'Live': return 'bg-emerald-500';
      case 'Fine-tuning': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-to-r from-tech-purple via-tech-cyan to-tech-emerald py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="font-display text-5xl sm:text-6xl font-bold text-white mb-6">
              AI Playground Dashboard
            </h1>
            <p className="text-xl text-white/90 max-w-4xl mx-auto">
              Interactive showcase of advanced AI/ML projects featuring real-time analytics,
              machine learning models, and intelligent automation systems
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-8">
        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card className="tech-card">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-tech-purple mb-2">{overallStats.totalModels}</div>
              <div className="text-sm text-muted-foreground">Active Models</div>
              <Cpu className="w-6 h-6 mx-auto mt-2 text-tech-purple" />
            </CardContent>
          </Card>
          <Card className="tech-card">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-tech-cyan mb-2">{overallStats.avgAccuracy.toFixed(1)}%</div>
              <div className="text-sm text-muted-foreground">Avg Accuracy</div>
              <Target className="w-6 h-6 mx-auto mt-2 text-tech-cyan" />
            </CardContent>
          </Card>
          <Card className="tech-card">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-tech-emerald mb-2">{overallStats.totalThroughput}</div>
              <div className="text-sm text-muted-foreground">Total Throughput</div>
              <Activity className="w-6 h-6 mx-auto mt-2 text-tech-emerald" />
            </CardContent>
          </Card>
          <Card className="tech-card">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-tech-orange mb-2">{overallStats.systemUptime}</div>
              <div className="text-sm text-muted-foreground">System Uptime</div>
              <Zap className="w-6 h-6 mx-auto mt-2 text-tech-orange" />
            </CardContent>
          </Card>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">



          {aiProjects.map((project, index) => (
            <Card
              key={project.id}
              className="tech-card group hover:shadow-2xl transition-all duration-300 hover:scale-[1.02]"
            >
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`${project.bgColor} p-3 rounded-xl`}>
                      <div className={`text-${project.color}`}>
                        {project.icon}
                      </div>
                    </div>
                    <div>
                      <CardTitle className="text-xl font-bold">{project.title}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <div
                          className={`w-3 h-3 rounded-full ${getStatusColor(project.status)} animate-pulse`}
                        ></div>
                        <Badge variant="secondary" className="text-xs">
                          {project.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-2xl font-bold text-${project.color}`}>
                      {project.accuracy}
                    </div>
                    <div className="text-xs text-muted-foreground">Accuracy</div>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <p className="text-muted-foreground text-sm leading-relaxed">
                  {project.description}
                </p>

                {/* Features */}
                <div className="space-y-2">
                  <div className="text-sm font-semibold">Key Features:</div>
                  <div className="flex flex-wrap gap-2">
                    {project.features.map((feature, idx) => (
                      <Badge key={idx} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 py-3">
                  <div className="text-center">
                    <div className="text-lg font-bold">{project.stats.models}</div>
                    <div className="text-xs text-muted-foreground">Models</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold">{project.stats.uptime}</div>
                    <div className="text-xs text-muted-foreground">Uptime</div>
                  </div>
                </div>

                {/* Performance Bar */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Performance</span>
                    <span>{project.stats.accuracy}%</span>
                  </div>
                  <Progress value={project.stats.accuracy} className="h-2" />
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2 pt-2">
                  <Button
                    onClick={() => handleViewProject(project.id)}
                    className="flex-1 gradient-tech-primary text-white hover:scale-105 transition-transform"
                  >
                    <Sparkles className="w-4 h-4 mr-2" />
                    Explore Demo
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="px-3"
                    onClick={() => handleLearnMore(project.id)}
                  >
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <Card className="tech-card mb-12">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Build Your AI Solution?
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              These interactive demos showcase real AI/ML capabilities. Contact us to discuss
              custom implementations for your enterprise needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="gradient-tech-primary text-white px-8">
                <Globe className="w-5 h-5 mr-2" />
                Schedule Consultation
              </Button>
              <Button size="lg" variant="outline" className="px-8">
                <Users className="w-5 h-5 mr-2" />
                View Case Studies
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
