import { useQuery } from "@tanstack/react-query";
import { useRoute, useLocation } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Play, Code, Calendar } from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";

export default function AIPlaygroundDetail() {
  const [, params] = useRoute("/ai-playground/:slug");
  const [, setLocation] = useLocation();
  
  const { data: item, isLoading, error } = useQuery({
    queryKey: [`/api/ai-playground/${params?.slug}`],
    enabled: !!params?.slug,
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="pt-24 pb-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="animate-pulse">
              <div className="h-8 bg-muted rounded w-3/4 mb-4"></div>
              <div className="h-4 bg-muted rounded w-1/2 mb-8"></div>
              <div className="space-y-4">
                <div className="h-4 bg-muted rounded"></div>
                <div className="h-4 bg-muted rounded w-5/6"></div>
                <div className="h-4 bg-muted rounded w-4/6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !item) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="pt-24 pb-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-2xl font-bold mb-4">AI Demo Not Found</h1>
            <p className="text-muted-foreground mb-8">
              The AI playground demo you're looking for doesn't exist or has been removed.
            </p>
            <Button onClick={() => setLocation("/ai-playground")}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to AI Playground
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <article className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Back Button */}
          <Button
            variant="ghost"
            onClick={() => setLocation("/ai-playground")}
            className="mb-8"
            data-testid="back-to-ai-playground"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to AI Playground
          </Button>

          {/* Demo Header */}
          <header className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <Badge 
                variant="secondary"
                className={`px-3 py-1 bg-${item.color}/10 text-${item.color}`}
              >
                {item.demoType}
              </Badge>
              {item.active === "true" && (
                <Badge variant="outline">Active Demo</Badge>
              )}
            </div>
            
            <h1 className="font-display text-4xl sm:text-5xl font-bold mb-6">
              {item.title}
            </h1>
            
            <p className="text-xl text-muted-foreground mb-6">
              {item.description}
            </p>
            
            <div className="flex items-center gap-6 text-muted-foreground">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-2" />
                {new Date(item.createdAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
            </div>
          </header>

          {/* Interactive Demo */}
          <Card className="mb-8">
            <CardContent className="p-8">
              <h3 className="font-semibold text-lg mb-6">Interactive Demo</h3>
              
              {/* Demo placeholder - in a real implementation, this would be the actual interactive component */}
              <div className="bg-gradient-to-br from-tech-purple/10 to-tech-cyan/10 rounded-xl p-8 text-center">
                <div className={`w-16 h-16 bg-${item.color}/10 rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                  <Play className={`w-8 h-8 text-${item.color}`} />
                </div>
                <h4 className="font-semibold text-lg mb-2">Live Demo Interface</h4>
                <p className="text-muted-foreground mb-6">
                  Interactive demonstration of {item.title} capabilities
                </p>
                <Button 
                  size="lg"
                  className="gradient-tech-primary text-white hover:scale-105 transition-transform"
                  data-testid="launch-demo"
                >
                  <Play className="w-5 h-5 mr-2" />
                  Launch Interactive Demo
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Technical Details */}
          <Card className="mb-8">
            <CardContent className="p-8">
              <h3 className="font-semibold text-lg mb-6">Technical Details</h3>
              <div 
                className="prose prose-lg max-w-none dark:prose-invert"
                dangerouslySetInnerHTML={{ __html: item.content }}
                data-testid="ai-demo-content"
              />
            </CardContent>
          </Card>

          {/* API Information */}
          {item.apiEndpoint && (
            <Card className="mb-8">
              <CardContent className="p-8">
                <h3 className="font-semibold text-lg mb-4">API Integration</h3>
                <div className="bg-neutral-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                  <div className="text-tech-emerald mb-2"># API Endpoint</div>
                  <div className="text-gray-300">POST {item.apiEndpoint}</div>
                  
                  {item.configData && (
                    <>
                      <div className="text-tech-emerald mt-4 mb-2"># Configuration</div>
                      <div className="text-gray-300">{item.configData}</div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button 
              size="lg"
              className="gradient-tech-primary text-white hover:scale-105 transition-transform"
              data-testid="try-demo"
            >
              <Play className="w-5 h-5 mr-2" />
              Try This Demo
            </Button>
            
            <Button 
              variant="outline" 
              size="lg"
              data-testid="view-documentation"
            >
              <Code className="w-5 h-5 mr-2" />
              View Documentation
            </Button>
          </div>
        </div>
      </article>
      
      <Footer />
    </div>
  );
}