import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  LayoutDashboard, 
  FileText, 
  Briefcase, 
  Brain, 
  Settings, 
  Users,
  PlusCircle,
  Edit,
  Trash2,
  Eye,
  LogOut
} from "lucide-react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";

export default function AdminDashboard() {
  const [, setLocation] = useLocation();

  // Check authentication
  useEffect(() => {
    const isAuthenticated = localStorage.getItem("admin-authenticated");
    if (!isAuthenticated) {
      setLocation("/admin/login");
    }
  }, [setLocation]);

  const handleLogout = () => {
    localStorage.removeItem("admin-authenticated");
    setLocation("/");
  };

  // Fetch data for each section
  const { data: blogPosts = [] } = useQuery({
    queryKey: ["/api/admin/blog-posts"],
  });

  const { data: projects = [] } = useQuery({
    queryKey: ["/api/admin/projects"],
  });

  const { data: aiPlayground = [] } = useQuery({
    queryKey: ["/api/admin/ai-playground"],
  });

  const { data: consultingServices = [] } = useQuery({
    queryKey: ["/api/admin/consulting-services"],
  });

  const { data: contacts = [] } = useQuery({
    queryKey: ["/api/contacts"],
  });

  const stats = [
    { label: "Blog Posts", value: blogPosts.length, icon: <FileText className="w-5 h-5" /> },
    { label: "Projects", value: projects.length, icon: <Briefcase className="w-5 h-5" /> },
    { label: "AI Demos", value: aiPlayground.length, icon: <Brain className="w-5 h-5" /> },
    { label: "Contact Inquiries", value: contacts.length, icon: <Users className="w-5 h-5" /> },
  ];

  const ContentTable = ({ 
    title, 
    items, 
    columns, 
    onEdit, 
    onDelete, 
    onAdd 
  }: {
    title: string;
    items: any[];
    columns: string[];
    onEdit: (item: any) => void;
    onDelete: (id: string) => void;
    onAdd: () => void;
  }) => (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-display text-xl font-bold">{title}</h3>
        <Button 
          onClick={onAdd} 
          className="gradient-tech-primary text-white"
          data-testid={`add-${title.toLowerCase().replace(" ", "-")}`}
        >
          <PlusCircle className="w-4 h-4 mr-2" />
          Add New
        </Button>
      </div>
      
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-border">
                  {columns.map((col) => (
                    <th key={col} className="text-left p-4 font-semibold">
                      {col}
                    </th>
                  ))}
                  <th className="text-left p-4 font-semibold">Actions</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item, index) => (
                  <tr key={item.id || index} className="border-b border-border hover:bg-muted/50">
                    <td className="p-4">
                      <div className="font-medium">{item.title}</div>
                      <div className="text-sm text-muted-foreground">{item.slug}</div>
                    </td>
                    <td className="p-4">
                      <div className="text-sm">{item.description?.substring(0, 50)}...</div>
                    </td>
                    <td className="p-4">
                      {item.published !== undefined && (
                        <Badge variant={item.published === "true" ? "default" : "secondary"}>
                          {item.published === "true" ? "Published" : "Draft"}
                        </Badge>
                      )}
                      {item.active !== undefined && (
                        <Badge variant={item.active === "true" ? "default" : "secondary"}>
                          {item.active === "true" ? "Active" : "Inactive"}
                        </Badge>
                      )}
                      {item.featured !== undefined && item.featured === "true" && (
                        <Badge variant="outline" className="ml-1">Featured</Badge>
                      )}
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEdit(item)}
                          data-testid={`edit-${item.id}`}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-destructive hover:text-destructive"
                          onClick={() => onDelete(item.id)}
                          data-testid={`delete-${item.id}`}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {items.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No items found. Click "Add New" to create your first {title.toLowerCase()}.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <LayoutDashboard className="w-6 h-6 text-tech-cyan mr-2" />
              <h1 className="font-display text-xl font-bold">Admin Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={() => setLocation("/")}
                data-testid="view-site"
              >
                <Eye className="w-4 h-4 mr-2" />
                View Site
              </Button>
              <Button
                variant="ghost"
                onClick={handleLogout}
                data-testid="admin-logout"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold">{stat.value}</div>
                    <div className="text-sm text-muted-foreground">{stat.label}</div>
                  </div>
                  <div className="text-tech-cyan">
                    {stat.icon}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Content Management Tabs */}
        <Tabs defaultValue="blog" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="blog" data-testid="tab-blog">Blog Posts</TabsTrigger>
            <TabsTrigger value="projects" data-testid="tab-projects">Projects</TabsTrigger>
            <TabsTrigger value="ai" data-testid="tab-ai">AI Playground</TabsTrigger>
            <TabsTrigger value="services" data-testid="tab-services">Services</TabsTrigger>
            <TabsTrigger value="contacts" data-testid="tab-contacts">Contacts</TabsTrigger>
          </TabsList>

          <TabsContent value="blog">
            <ContentTable
              title="Blog Posts"
              items={blogPosts}
              columns={["Title", "Description", "Status"]}
              onAdd={() => setLocation("/admin/blog/new")}
              onEdit={(post) => setLocation(`/admin/blog/edit/${post.id}`)}
              onDelete={(id) => console.log("Delete blog post:", id)}
            />
          </TabsContent>

          <TabsContent value="projects">
            <ContentTable
              title="Projects"
              items={projects}
              columns={["Title", "Description", "Status"]}
              onAdd={() => setLocation("/admin/projects/new")}
              onEdit={(project) => setLocation(`/admin/projects/edit/${project.id}`)}
              onDelete={(id) => console.log("Delete project:", id)}
            />
          </TabsContent>

          <TabsContent value="ai">
            <ContentTable
              title="AI Playground Items"
              items={aiPlayground}
              columns={["Title", "Description", "Status"]}
              onAdd={() => setLocation("/admin/ai-playground/new")}
              onEdit={(item) => setLocation(`/admin/ai-playground/edit/${item.id}`)}
              onDelete={(id) => console.log("Delete AI item:", id)}
            />
          </TabsContent>

          <TabsContent value="services">
            <ContentTable
              title="Consulting Services"
              items={consultingServices}
              columns={["Title", "Description", "Status"]}
              onAdd={() => setLocation("/admin/services/new")}
              onEdit={(service) => setLocation(`/admin/services/edit/${service.id}`)}
              onDelete={(id) => console.log("Delete service:", id)}
            />
          </TabsContent>

          <TabsContent value="contacts">
            <Card>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-border">
                        <th className="text-left p-4 font-semibold">Name</th>
                        <th className="text-left p-4 font-semibold">Email</th>
                        <th className="text-left p-4 font-semibold">Project Type</th>
                        <th className="text-left p-4 font-semibold">Budget</th>
                        <th className="text-left p-4 font-semibold">Date</th>
                      </tr>
                    </thead>
                    <tbody>
                      {contacts.map((contact: any, index: number) => (
                        <tr key={contact.id || index} className="border-b border-border hover:bg-muted/50">
                          <td className="p-4">
                            <div className="font-medium">{contact.name}</div>
                            <div className="text-sm text-muted-foreground">{contact.company}</div>
                          </td>
                          <td className="p-4">{contact.email}</td>
                          <td className="p-4">
                            <Badge variant="outline">{contact.projectType}</Badge>
                          </td>
                          <td className="p-4">{contact.budget || "Not specified"}</td>
                          <td className="p-4">
                            {new Date(contact.createdAt).toLocaleDateString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                {contacts.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No contact inquiries yet.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}