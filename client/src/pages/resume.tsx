import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Download, 
  MapPin, 
  Mail, 
  Phone, 
  Github, 
  Linkedin,
  Calendar,
  Award,
  GraduationCap,
  Briefcase,
  Code,
  Brain,
  Database,
  Cloud
} from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import PageTransition, { SectionTransition, CardHover } from "@/components/PageTransition";

export default function Resume() {
  const personalInfo = {
    name: "Khiw (Ikkyu) Nitithadachot",
    title: "Data Engineer & AI/ML Specialist",
    location: "Bangkok, Thailand",
    email: "<EMAIL>",
    phone: "+66 123 456 789",
    github: "github.com/khiw-dev",
    linkedin: "linkedin.com/in/khiw-dev"
  };

  const skills = {
    "Data Engineering": ["Python", "SQL", "Apache Spark", "Kafka", "Airflow", "dbt"],
    "Machine Learning": ["TensorFlow", "PyTorch", "Scikit-learn", "MLflow", "Kubeflow"],
    "Cloud Platforms": ["Azure", "AWS", "GCP", "Docker", "Kubernetes"],
    "Programming": ["Python", "TypeScript", "R", "Scala", "Java"],
    "Databases": ["PostgreSQL", "MongoDB", "Redis", "Snowflake", "BigQuery"],
    "Tools & Frameworks": ["FastAPI", "React", "Next.js", "Terraform", "Git"]
  };

  const experience = [
    {
      title: "Senior Data Engineer",
      company: "TechCorp Solutions",
      location: "Bangkok, Thailand",
      period: "2022 - Present",
      description: "Lead data engineering initiatives for enterprise-scale applications",
      achievements: [
        "Designed and implemented data pipelines processing 10TB+ daily data",
        "Reduced data processing time by 60% through pipeline optimization",
        "Led team of 5 engineers in building ML infrastructure",
        "Implemented real-time streaming analytics using Kafka and Spark"
      ]
    },
    {
      title: "Data Engineer",
      company: "DataFlow Analytics",
      location: "Bangkok, Thailand", 
      period: "2020 - 2022",
      description: "Developed scalable data solutions for fintech applications",
      achievements: [
        "Built ETL pipelines serving 1M+ users with 99.9% uptime",
        "Implemented ML models for fraud detection with 95% accuracy",
        "Migrated legacy systems to cloud-native architecture",
        "Established data governance and quality frameworks"
      ]
    },
    {
      title: "Junior Data Analyst",
      company: "Analytics Plus",
      location: "Bangkok, Thailand",
      period: "2018 - 2020", 
      description: "Analyzed business data and created insights for decision making",
      achievements: [
        "Created automated reporting dashboards reducing manual work by 80%",
        "Performed statistical analysis for business optimization",
        "Collaborated with cross-functional teams on data-driven projects",
        "Developed predictive models for customer behavior analysis"
      ]
    }
  ];

  const education = [
    {
      degree: "Master of Science in Data Science",
      school: "Chulalongkorn University",
      location: "Bangkok, Thailand",
      period: "2016 - 2018",
      gpa: "3.8/4.0",
      thesis: "Deep Learning Applications in Financial Time Series Analysis"
    },
    {
      degree: "Bachelor of Engineering in Computer Engineering", 
      school: "King Mongkut's University of Technology",
      location: "Bangkok, Thailand",
      period: "2012 - 2016",
      gpa: "3.6/4.0",
      honors: "Magna Cum Laude"
    }
  ];

  const certifications = [
    {
      title: "Azure Data Engineer Associate",
      issuer: "Microsoft",
      date: "2023",
      credentialId: "AZ-104-2023"
    },
    {
      title: "AWS Certified Solutions Architect",
      issuer: "Amazon Web Services",
      date: "2022", 
      credentialId: "SAA-C02-2022"
    },
    {
      title: "Google Cloud Professional Data Engineer",
      issuer: "Google Cloud",
      date: "2022",
      credentialId: "GCP-PDE-2022"
    },
    {
      title: "TensorFlow Developer Certificate",
      issuer: "TensorFlow",
      date: "2021",
      credentialId: "TF-DEV-2021"
    }
  ];

  const projects = [
    {
      title: "Real-time Financial Analytics Platform",
      description: "End-to-end ML platform for algorithmic trading with real-time data processing",
      technologies: ["Python", "Kafka", "Spark", "TensorFlow", "Kubernetes"],
      impact: "Achieved 15% improvement in trading performance"
    },
    {
      title: "Customer Behavior Prediction Engine", 
      description: "ML system predicting customer churn with advanced feature engineering",
      technologies: ["Python", "Scikit-learn", "MLflow", "Azure ML", "PostgreSQL"],
      impact: "Reduced customer churn by 25%"
    },
    {
      title: "Automated Data Quality Framework",
      description: "Comprehensive data validation and monitoring system for enterprise data",
      technologies: ["Python", "Apache Airflow", "Great Expectations", "Grafana"],
      impact: "Improved data quality scores by 40%"
    }
  ];

  return (
    <PageTransition className="min-h-screen">
      <Navigation />
      <div className="min-h-screen pt-16 bg-gradient-to-br from-background via-background to-muted/20">
        
        {/* Header Section */}
        <SectionTransition>
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center mb-12">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6 }}
                className="mb-8"
              >
                <div className="w-32 h-32 mx-auto rounded-full gradient-tech-primary p-1 mb-6">
                  <div className="w-full h-full rounded-full bg-background flex items-center justify-center">
                    <span className="text-4xl font-bold text-primary">KN</span>
                  </div>
                </div>
                <h1 className="font-display text-4xl md:text-5xl font-bold mb-4">
                  <span className="bg-gradient-to-r from-primary-800 via-tech-cyan to-tech-purple bg-clip-text text-transparent">
                    {personalInfo.name}
                  </span>
                </h1>
                <p className="text-xl text-muted-foreground mb-6">{personalInfo.title}</p>
                
                <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground mb-8">
                  <div className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    {personalInfo.location}
                  </div>
                  <div className="flex items-center gap-1">
                    <Mail className="w-4 h-4" />
                    {personalInfo.email}
                  </div>
                  <div className="flex items-center gap-1">
                    <Phone className="w-4 h-4" />
                    {personalInfo.phone}
                  </div>
                </div>

                <div className="flex justify-center gap-4 mb-8">
                  <Button variant="outline" size="sm">
                    <Github className="w-4 h-4 mr-2" />
                    GitHub
                  </Button>
                  <Button variant="outline" size="sm">
                    <Linkedin className="w-4 h-4 mr-2" />
                    LinkedIn
                  </Button>
                  <Button className="gradient-tech-primary text-white">
                    <Download className="w-4 h-4 mr-2" />
                    Download PDF
                  </Button>
                </div>
              </motion.div>
            </div>
          </div>
        </SectionTransition>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-12 space-y-12">
          
          {/* Professional Summary */}
          <SectionTransition delay={0.1}>
            <Card>
              <CardContent className="p-8">
                <h2 className="flex items-center text-2xl font-bold mb-6">
                  <Brain className="w-6 h-6 mr-3 text-tech-cyan" />
                  Professional Summary
                </h2>
                <p className="text-muted-foreground leading-relaxed">
                  Experienced Data Engineer and AI/ML Specialist with 6+ years of expertise in designing and implementing 
                  scalable data solutions. Proven track record of leading cross-functional teams, optimizing data pipelines 
                  for enterprise-scale applications, and delivering ML solutions that drive business value. Passionate about 
                  leveraging cutting-edge technologies to solve complex data challenges and enable data-driven decision making.
                </p>
              </CardContent>
            </Card>
          </SectionTransition>

          {/* Skills */}
          <SectionTransition delay={0.2}>
            <Card>
              <CardContent className="p-8">
                <h2 className="flex items-center text-2xl font-bold mb-6">
                  <Code className="w-6 h-6 mr-3 text-tech-cyan" />
                  Technical Skills
                </h2>
                <div className="space-y-6">
                  {Object.entries(skills).map(([category, skillList], index) => (
                    <motion.div
                      key={category}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <h3 className="font-semibold text-lg mb-3 text-primary">{category}</h3>
                      <div className="flex flex-wrap gap-2">
                        {skillList.map((skill) => (
                          <Badge key={skill} variant="secondary" className="hover:bg-tech-cyan hover:text-white transition-colors">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </SectionTransition>

          {/* Experience */}
          <SectionTransition delay={0.3}>
            <Card>
              <CardContent className="p-8">
                <h2 className="flex items-center text-2xl font-bold mb-6">
                  <Briefcase className="w-6 h-6 mr-3 text-tech-cyan" />
                  Professional Experience
                </h2>
                <div className="space-y-8">
                  {experience.map((job, index) => (
                    <CardHover key={index}>
                      <div className="border-l-4 border-tech-cyan pl-6 pb-6">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <h3 className="text-xl font-semibold">{job.title}</h3>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Calendar className="w-4 h-4 mr-1" />
                            {job.period}
                          </div>
                        </div>
                        <div className="flex items-center text-primary font-medium mb-2">
                          <span>{job.company}</span>
                          <span className="mx-2">•</span>
                          <span className="text-muted-foreground">{job.location}</span>
                        </div>
                        <p className="text-muted-foreground mb-4">{job.description}</p>
                        <ul className="space-y-1">
                          {job.achievements.map((achievement, achieveIndex) => (
                            <li key={achieveIndex} className="flex items-start">
                              <span className="w-2 h-2 rounded-full bg-tech-cyan mt-2 mr-3 flex-shrink-0" />
                              <span className="text-sm">{achievement}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardHover>
                  ))}
                </div>
              </CardContent>
            </Card>
          </SectionTransition>

          {/* Education */}
          <SectionTransition delay={0.4}>
            <Card>
              <CardContent className="p-8">
                <h2 className="flex items-center text-2xl font-bold mb-6">
                  <GraduationCap className="w-6 h-6 mr-3 text-tech-cyan" />
                  Education
                </h2>
                <div className="space-y-6">
                  {education.map((edu, index) => (
                    <CardHover key={index}>
                      <div className="border-l-4 border-tech-emerald pl-6">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                          <h3 className="text-lg font-semibold">{edu.degree}</h3>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Calendar className="w-4 h-4 mr-1" />
                            {edu.period}
                          </div>
                        </div>
                        <div className="text-primary font-medium mb-1">{edu.school}</div>
                        <div className="text-sm text-muted-foreground mb-2">{edu.location}</div>
                        <div className="flex gap-4 text-sm">
                          <span>GPA: <strong>{edu.gpa}</strong></span>
                          {edu.honors && <span className="text-tech-emerald font-medium">{edu.honors}</span>}
                        </div>
                        {edu.thesis && (
                          <div className="mt-2 text-sm">
                            <span className="font-medium">Thesis: </span>
                            <span className="text-muted-foreground">{edu.thesis}</span>
                          </div>
                        )}
                      </div>
                    </CardHover>
                  ))}
                </div>
              </CardContent>
            </Card>
          </SectionTransition>

          {/* Certifications */}
          <SectionTransition delay={0.5}>
            <Card>
              <CardContent className="p-8">
                <h2 className="flex items-center text-2xl font-bold mb-6">
                  <Award className="w-6 h-6 mr-3 text-tech-cyan" />
                  Certifications
                </h2>
                <div className="grid md:grid-cols-2 gap-4">
                  {certifications.map((cert, index) => (
                    <CardHover key={index}>
                      <div className="border rounded-lg p-4 hover:border-tech-cyan transition-colors">
                        <h3 className="font-semibold mb-1">{cert.title}</h3>
                        <div className="text-primary font-medium text-sm mb-1">{cert.issuer}</div>
                        <div className="flex justify-between items-center text-xs text-muted-foreground">
                          <span>Issued: {cert.date}</span>
                          <span>ID: {cert.credentialId}</span>
                        </div>
                      </div>
                    </CardHover>
                  ))}
                </div>
              </CardContent>
            </Card>
          </SectionTransition>

          {/* Key Projects */}
          <SectionTransition delay={0.6}>
            <Card>
              <CardContent className="p-8">
                <h2 className="flex items-center text-2xl font-bold mb-6">
                  <Database className="w-6 h-6 mr-3 text-tech-cyan" />
                  Key Projects
                </h2>
                <div className="space-y-6">
                  {projects.map((project, index) => (
                    <CardHover key={index}>
                      <div className="border rounded-lg p-6 hover:border-tech-cyan transition-colors">
                        <h3 className="text-lg font-semibold mb-2">{project.title}</h3>
                        <p className="text-muted-foreground mb-4">{project.description}</p>
                        <div className="flex flex-wrap gap-2 mb-3">
                          {project.technologies.map((tech) => (
                            <Badge key={tech} variant="outline" className="text-xs">
                              {tech}
                            </Badge>
                          ))}
                        </div>
                        <div className="text-sm font-medium text-tech-emerald">
                          Impact: {project.impact}
                        </div>
                      </div>
                    </CardHover>
                  ))}
                </div>
              </CardContent>
            </Card>
          </SectionTransition>

        </div>
      </div>
      <Footer />
    </PageTransition>
  );
}