import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useLocation } from 'wouter';
import {
  Brain,
  ArrowLeft,
  Code,
  Database,
  TrendingUp,
  Shield,
  Zap,
  Users,
  Globe,
  FileText,
  Play,
  Download,
  ExternalLink,
  CheckCircle,
  Target,
  BarChart3,
  LineChart
} from 'lucide-react';

export default function AIPortfolioExplanation() {
  const [, setLocation] = useLocation();
  const [activeCodeTab, setActiveCodeTab] = useState('python');

  const codeExamples = {
    python: `# AI Portfolio Optimization Algorithm
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from scipy.optimize import minimize
import yfinance as yf

class AIPortfolioOptimizer:
    def __init__(self, symbols, lookback_period=252):
        self.symbols = symbols
        self.lookback_period = lookback_period
        self.data = None
        self.returns = None
        self.risk_model = None
        self.return_model = None

    def fetch_data(self):
        """Fetch historical price data for portfolio assets"""
        self.data = yf.download(self.symbols,
                               period=f"{self.lookback_period}d")['Adj Close']
        self.returns = self.data.pct_change().dropna()
        return self.data

    def train_risk_model(self):
        """Train ML model to predict asset volatility"""
        features = self._generate_features()
        target = self.returns.rolling(30).std().shift(-30).dropna()

        self.risk_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.risk_model.fit(features[:-30], target)

    def train_return_model(self):
        """Train ML model to predict expected returns"""
        features = self._generate_features()
        target = self.returns.rolling(30).mean().shift(-30).dropna()

        self.return_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.return_model.fit(features[:-30], target)

    def _generate_features(self):
        """Generate technical and fundamental features"""
        features = pd.DataFrame(index=self.returns.index)

        # Technical indicators
        for symbol in self.symbols:
            price = self.data[symbol]
            returns = self.returns[symbol]

            # Moving averages
            features[f'{symbol}_sma_20'] = price.rolling(20).mean()
            features[f'{symbol}_sma_50'] = price.rolling(50).mean()

            # Volatility measures
            features[f'{symbol}_vol_20'] = returns.rolling(20).std()
            features[f'{symbol}_vol_60'] = returns.rolling(60).std()

            # Momentum indicators
            features[f'{symbol}_momentum_10'] = returns.rolling(10).sum()
            features[f'{symbol}_momentum_30'] = returns.rolling(30).sum()

            # RSI
            features[f'{symbol}_rsi'] = self._calculate_rsi(price)

        return features.dropna()

    def _calculate_rsi(self, prices, period=14):
        """Calculate Relative Strength Index"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def optimize_portfolio(self, target_return=None, risk_tolerance='moderate'):
        """Optimize portfolio allocation using AI predictions"""
        if self.risk_model is None or self.return_model is None:
            raise ValueError("Models must be trained first")

        # Get latest features for prediction
        latest_features = self._generate_features().iloc[-1:].values

        # Predict expected returns and risks
        expected_returns = self.return_model.predict(latest_features)[0]
        expected_risks = self.risk_model.predict(latest_features)[0]

        # Covariance matrix estimation
        cov_matrix = self.returns.cov().values

        # Optimization objective
        def objective(weights):
            portfolio_return = np.sum(weights * expected_returns)
            portfolio_risk = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))

            # Risk-adjusted return (Sharpe ratio maximization)
            risk_free_rate = 0.02  # 2% risk-free rate
            sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_risk

            return -sharpe_ratio  # Minimize negative Sharpe ratio

        # Constraints
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # Weights sum to 1
        ]

        # Bounds (0-40% per asset based on risk tolerance)
        risk_limits = {
            'conservative': 0.25,
            'moderate': 0.35,
            'aggressive': 0.50
        }
        max_weight = risk_limits.get(risk_tolerance, 0.35)
        bounds = tuple((0, max_weight) for _ in range(len(self.symbols)))

        # Initial guess (equal weights)
        x0 = np.array([1/len(self.symbols)] * len(self.symbols))

        # Optimize
        result = minimize(objective, x0, method='SLSQP',
                         bounds=bounds, constraints=constraints)

        return {
            'weights': dict(zip(self.symbols, result.x)),
            'expected_return': np.sum(result.x * expected_returns),
            'expected_risk': np.sqrt(np.dot(result.x.T, np.dot(cov_matrix, result.x))),
            'sharpe_ratio': -result.fun
        }

    def generate_insights(self, portfolio_weights):
        """Generate AI-powered investment insights"""
        insights = []

        # Concentration analysis
        max_weight = max(portfolio_weights.values())
        if max_weight > 0.4:
            insights.append({
                'type': 'warning',
                'message': f'High concentration risk detected. Consider diversifying.',
                'confidence': 0.85
            })

        # Sector analysis (simplified)
        tech_weight = sum(w for s, w in portfolio_weights.items()
                         if s in ['AAPL', 'GOOGL', 'MSFT', 'NVDA'])
        if tech_weight > 0.6:
            insights.append({
                'type': 'info',
                'message': f'High tech sector exposure ({tech_weight:.1%}). Monitor sector correlation.',
                'confidence': 0.92
            })

        # Momentum analysis
        latest_returns = self.returns.iloc[-30:].mean()
        for symbol, weight in portfolio_weights.items():
            if weight > 0.15 and latest_returns[symbol] < -0.02:
                insights.append({
                    'type': 'warning',
                    'message': f'{symbol} showing negative momentum but high allocation.',
                    'confidence': 0.78
                })

        return insights

# Usage Example
if __name__ == "__main__":
    # Initialize optimizer
    symbols = ['AAPL', 'GOOGL', 'MSFT', 'NVDA', 'TSLA', 'META']
    optimizer = AIPortfolioOptimizer(symbols)

    # Fetch data and train models
    data = optimizer.fetch_data()
    optimizer.train_risk_model()
    optimizer.train_return_model()

    # Optimize portfolio
    result = optimizer.optimize_portfolio(risk_tolerance='moderate')

    print("Optimized Portfolio Allocation:")
    for symbol, weight in result['weights'].items():
        print(f"{symbol}: {weight:.2%}")

    print(f"\\nExpected Annual Return: {result['expected_return']*252:.2%}")
    print(f"Expected Annual Risk: {result['expected_risk']*np.sqrt(252):.2%}")
    print(f"Sharpe Ratio: {result['sharpe_ratio']:.3f}")

    # Generate insights
    insights = optimizer.generate_insights(result['weights'])
    print("\\nAI Insights:")
    for insight in insights:

        print(f"- {insight['message']} (Confidence: {insight['confidence']:.0%})")`,

    typescript: `// TypeScript Interface for Portfolio Management
interface Portfolio {
  id: string;
  name: string;

  assets: Asset[];
  allocation: Record<string, number>;
  metrics: PortfolioMetrics;
  riskProfile: RiskProfile;
}

interface Asset {
  symbol: string;
  name: string;
  sector: string;
  price: number;
  quantity: number;
  weight: number;
  expectedReturn: number;
  volatility: number;
  beta: number;
}

interface PortfolioMetrics {
  totalValue: number;
  dailyReturn: number;
  weeklyReturn: number;
  monthlyReturn: number;
  sharpeRatio: number;
  sortino: number;
  maxDrawdown: number;
  volatility: number;
  beta: number;
  alpha: number;
}

interface RiskProfile {
  tolerance: 'conservative' | 'moderate' | 'aggressive';
  timeHorizon: number; // years
  liquidityNeeds: number; // percentage
  constraints: {
    maxSingleAsset: number;
    maxSectorExposure: number;
    minDiversification: number;
  };
}

interface AIInsight {
  type: 'opportunity' | 'risk' | 'rebalance' | 'market';
  message: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high';
  recommendation?: string;
  timeframe?: string;
}

class AIPortfolioManager {
  private portfolio: Portfolio;
  private marketData: MarketData;
  private mlModels: MLModels;

  constructor(portfolio: Portfolio) {
    this.portfolio = portfolio;
    this.marketData = new MarketData();
    this.mlModels = new MLModels();
  }

  async optimizeAllocation(): Promise<{
    newAllocation: Record<string, number>;
    expectedImprovement: number;
    confidence: number;
  }> {
    // Fetch latest market data
    const data = await this.marketData.getLatestData(
      this.portfolio.assets.map(a => a.symbol)
    );

    // Generate features for ML models
    const features = this.generateFeatures(data);

    // Predict returns and risks
    const predictions = await this.mlModels.predict(features);

    // Run optimization algorithm
    const optimization = this.runOptimization(predictions);

    return optimization;
  }

  generateInsights(): AIInsight[] {
    const insights: AIInsight[] = [];

    // Risk concentration analysis
    const maxWeight = Math.max(...Object.values(this.portfolio.allocation));
    if (maxWeight > 0.3) {
      insights.push({
        type: 'risk',
        message: \`High concentration in single asset (\${(maxWeight * 100).toFixed(1)}%)\`,
        confidence: 0.92,
        impact: 'high',
        recommendation: 'Consider reducing position size and diversifying'
      });
    }

    // Performance analysis
    if (this.portfolio.metrics.sharpeRatio < 0.5) {
      insights.push({
        type: 'opportunity',
        message: 'Portfolio showing suboptimal risk-adjusted returns',
        confidence: 0.87,
        impact: 'medium',
        recommendation: 'Review asset allocation and consider rebalancing'
      });
    }

    // Market sentiment analysis
    insights.push({
      type: 'market',
      message: 'AI models detect potential market volatility increase',
      confidence: 0.76,

      impact: 'medium',

      timeframe: 'Next 2-4 weeks'

    });



    return insights;



  }



  private generateFeatures(data: MarketData): number[][] {

    // Feature engineering for ML models

    return data.assets.map(asset => [

      asset.returns.mean(),

      asset.returns.std(),

      asset.momentum.short,

      asset.momentum.long,

      asset.technicals.rsi,

      asset.technicals.macd,

      asset.fundamentals.pe,

      asset.fundamentals.pbv,

      asset.sentiment.score

    ]);

  }



  private async runOptimization(

    predictions: ModelPredictions

  ): Promise<OptimizationResult> {

    // Modern Portfolio Theory with AI enhancements

    const constraints = this.buildConstraints();

    const objective = this.buildObjective(predictions);



    // Use quadratic programming for optimization

    const result = await this.optimize(objective, constraints);



    return result;

  }

}



// React Hook for Portfolio Management

export const useAIPortfolio = (portfolioId: string) => {

  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);

  const [insights, setInsights] = useState<AIInsight[]>([]);

  const [isOptimizing, setIsOptimizing] = useState(false);



  const optimizePortfolio = async () => {

    if (!portfolio) return;



    setIsOptimizing(true);

    try {

      const manager = new AIPortfolioManager(portfolio);

      const result = await manager.optimizeAllocation();



      // Update portfolio with new allocation

      setPortfolio(prev => ({

        ...prev!,

        allocation: result.newAllocation

      }));



      // Generate new insights

      const newInsights = manager.generateInsights();

      setInsights(newInsights);



    } catch (error) {

      console.error('Portfolio optimization failed:', error);

    } finally {

      setIsOptimizing(false);

    }

  };



  return {

    portfolio,

    insights,

    isOptimizing,

    optimizePortfolio

  };

};`,



    sql: `-- AI Portfolio Database Schema

-- Advanced database design for portfolio management with ML integration



-- Main portfolio table

CREATE TABLE portfolios (

    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    user_id UUID NOT NULL REFERENCES users(id),

    name VARCHAR(255) NOT NULL,

    description TEXT,

    risk_profile risk_profile_enum NOT NULL,

    target_return DECIMAL(5,4),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),



    -- AI-specific fields

    ai_model_version VARCHAR(50),

    last_optimization_at TIMESTAMP WITH TIME ZONE,

    optimization_frequency INTERVAL DEFAULT '1 day',



    auto_rebalance BOOLEAN DEFAULT false

);



-- Portfolio assets and allocations

CREATE TABLE portfolio_allocations (

    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    portfolio_id UUID NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,

    asset_symbol VARCHAR(20) NOT NULL,

    target_weight DECIMAL(8,6) NOT NULL CHECK (target_weight >= 0 AND target_weight <= 1),

    current_weight DECIMAL(8,6),

    shares DECIMAL(15,8),

    cost_basis DECIMAL(15,4),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),



    UNIQUE(portfolio_id, asset_symbol)

);



-- Historical performance tracking

CREATE TABLE portfolio_performance (

    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    portfolio_id UUID NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,

    date DATE NOT NULL,

    total_value DECIMAL(15,4) NOT NULL,

    daily_return DECIMAL(8,6),

    benchmark_return DECIMAL(8,6),



    -- Risk metrics

    volatility DECIMAL(8,6),

    sharpe_ratio DECIMAL(8,6),

    sortino_ratio DECIMAL(8,6),
    max_drawdown DECIMAL(8,6),
    beta DECIMAL(8,6),
    alpha DECIMAL(8,6),

    -- AI-generated metrics
    ai_confidence_score DECIMAL(5,4),
    predicted_return DECIMAL(8,6),
    risk_score DECIMAL(5,4),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(portfolio_id, date)
);

-- AI insights and recommendations
CREATE TABLE ai_insights (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    portfolio_id UUID NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,
    insight_type insight_type_enum NOT NULL,
    message TEXT NOT NULL,
    confidence DECIMAL(5,4) NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
    impact_level impact_level_enum NOT NULL,
    recommendation TEXT,
    timeframe VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- ML model metadata
    model_name VARCHAR(100),
    model_version VARCHAR(50),
    feature_importance JSONB
);

-- Market data for ML training
CREATE TABLE market_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    symbol VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    open_price DECIMAL(15,4),
    high_price DECIMAL(15,4),
    low_price DECIMAL(15,4),
    close_price DECIMAL(15,4),
    volume BIGINT,
    adjusted_close DECIMAL(15,4),

    -- Technical indicators
    sma_20 DECIMAL(15,4),
    sma_50 DECIMAL(15,4),
    sma_200 DECIMAL(15,4),
    rsi DECIMAL(8,4),
    macd DECIMAL(15,8),
    bollinger_upper DECIMAL(15,4),
    bollinger_lower DECIMAL(15,4),

    -- Fundamental data
    pe_ratio DECIMAL(8,4),
    pb_ratio DECIMAL(8,4),
    market_cap BIGINT,
    dividend_yield DECIMAL(8,6),

    -- Sentiment and alternative data
    news_sentiment DECIMAL(5,4),
    social_sentiment DECIMAL(5,4),
    insider_activity DECIMAL(5,4),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(symbol, date)
);

-- ML model tracking
CREATE TABLE ml_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    version VARCHAR(50) NOT NULL,
    model_type VARCHAR(50) NOT NULL, -- 'return_prediction', 'risk_assessment', 'sentiment'
    hyperparameters JSONB,
    training_data_period DATERANGE,
    performance_metrics JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(name, version)
);

-- Model predictions for backtesting
CREATE TABLE model_predictions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_id UUID NOT NULL REFERENCES ml_models(id),
    symbol VARCHAR(20) NOT NULL,
    prediction_date DATE NOT NULL,
    prediction_horizon INTERVAL NOT NULL, -- '1 day', '1 week', '1 month'
    predicted_return DECIMAL(8,6),
    predicted_volatility DECIMAL(8,6),
    confidence DECIMAL(5,4),
    actual_return DECIMAL(8,6), -- Filled after horizon passes
    prediction_error DECIMAL(8,6),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()

);



-- Enums

CREATE TYPE risk_profile_enum AS ENUM ('conservative', 'moderate', 'aggressive');

CREATE TYPE insight_type_enum AS ENUM ('opportunity', 'risk', 'rebalance', 'market');

CREATE TYPE impact_level_enum AS ENUM ('low', 'medium', 'high');


-- Indexes for performance
CREATE INDEX idx_portfolio_performance_date ON portfolio_performance(portfolio_id, date DESC);

CREATE INDEX idx_market_data_symbol_date ON market_data(symbol, date DESC);

CREATE INDEX idx_ai_insights_portfolio_active ON ai_insights(portfolio_id, is_active, created_at DESC);

CREATE INDEX idx_model_predictions_symbol_date ON model_predictions(symbol, prediction_date DESC);



-- Advanced queries for portfolio analysis

-- 1. Get portfolio performance with rolling metrics

CREATE OR REPLACE FUNCTION get_portfolio_performance(

    p_portfolio_id UUID,

    p_days INTEGER DEFAULT 30

) RETURNS TABLE (

    date DATE,

    total_value DECIMAL,

    daily_return DECIMAL,

    rolling_volatility DECIMAL,

    rolling_sharpe DECIMAL

) AS $$

BEGIN

    RETURN QUERY

    SELECT

        pp.date,

        pp.total_value,

        pp.daily_return,

        STDDEV(pp.daily_return) OVER (

            ORDER BY pp.date

            ROWS BETWEEN p_days PRECEDING AND CURRENT ROW

        ) as rolling_volatility,

        AVG(pp.daily_return) OVER (

            ORDER BY pp.date
            ROWS BETWEEN p_days PRECEDING AND CURRENT ROW
        ) / NULLIF(STDDEV(pp.daily_return) OVER (
            ORDER BY pp.date
            ROWS BETWEEN p_days PRECEDING AND CURRENT ROW
        ), 0) as rolling_sharpe
    FROM portfolio_performance pp
    WHERE pp.portfolio_id = p_portfolio_id
    ORDER BY pp.date DESC;
END;
$$ LANGUAGE plpgsql;

-- 2. Get AI insights with impact analysis
CREATE OR REPLACE VIEW active_portfolio_insights AS
SELECT
    ai.portfolio_id,
    ai.insight_type,
    ai.message,
    ai.confidence,
    ai.impact_level,
    ai.recommendation,
    ai.created_at,
    EXTRACT(days FROM NOW() - ai.created_at) as days_old,
    CASE
        WHEN ai.impact_level = 'high' AND ai.confidence > 0.8 THEN 5
        WHEN ai.impact_level = 'high' AND ai.confidence > 0.6 THEN 4
        WHEN ai.impact_level = 'medium' AND ai.confidence > 0.8 THEN 3
        WHEN ai.impact_level = 'medium' AND ai.confidence > 0.6 THEN 2
        ELSE 1
    END as priority_score
FROM ai_insights ai
WHERE ai.is_active = true
  AND (ai.expires_at IS NULL OR ai.expires_at > NOW())
ORDER BY priority_score DESC, ai.created_at DESC;

-- 3. Portfolio optimization data preparation
CREATE OR REPLACE FUNCTION prepare_optimization_data(
    p_portfolio_id UUID,
    p_lookback_days INTEGER DEFAULT 252
) RETURNS TABLE (
    symbol VARCHAR,
    current_weight DECIMAL,
    expected_return DECIMAL,
    volatility DECIMAL,
    correlation_matrix JSONB
) AS $$
DECLARE
    symbols TEXT[];
    correlation_data JSONB;
BEGIN
    -- Get portfolio symbols
    SELECT ARRAY_AGG(asset_symbol) INTO symbols
    FROM portfolio_allocations
    WHERE portfolio_id = p_portfolio_id;

    -- Calculate correlation matrix
    WITH daily_returns AS (
        SELECT
            symbol,
            date,
            (close_price - LAG(close_price) OVER (PARTITION BY symbol ORDER BY date))
            / LAG(close_price) OVER (PARTITION BY symbol ORDER BY date) as daily_return
        FROM market_data
        WHERE symbol = ANY(symbols)
          AND date >= CURRENT_DATE - INTERVAL '1 day' * p_lookback_days
    ),
    correlation_calc AS (
        SELECT
            a.symbol as symbol_a,
            b.symbol as symbol_b,
            CORR(a.daily_return, b.daily_return) as correlation
        FROM daily_returns a
        JOIN daily_returns b ON a.date = b.date
        WHERE a.symbol <= b.symbol
        GROUP BY a.symbol, b.symbol
    )
    SELECT json_object_agg(
        symbol_a || '_' || symbol_b,
        correlation
    ) INTO correlation_data
    FROM correlation_calc;

    -- Return optimization data
    RETURN QUERY
    SELECT
        pa.asset_symbol as symbol,
        pa.current_weight,
        AVG(md.daily_return) * 252 as expected_return, -- Annualized
        STDDEV(md.daily_return) * SQRT(252) as volatility, -- Annualized
        correlation_data as correlation_matrix
    FROM portfolio_allocations pa
    JOIN (
        SELECT
            symbol,
            (close_price - LAG(close_price) OVER (PARTITION BY symbol ORDER BY date))
            / LAG(close_price) OVER (PARTITION BY symbol ORDER BY date) as daily_return
        FROM market_data
        WHERE date >= CURRENT_DATE - INTERVAL '1 day' * p_lookback_days
    ) md ON pa.asset_symbol = md.symbol
    WHERE pa.portfolio_id = p_portfolio_id
    GROUP BY pa.asset_symbol, pa.current_weight;
END;
$$ LANGUAGE plpgsql;`
  };

  const features = [
    {
      title: "Machine Learning Portfolio Optimization",
      description: "Advanced algorithms that analyze market patterns, risk factors, and return predictions to optimize asset allocation",
      icon: <Brain className="w-6 h-6" />,
      details: [
        "Random Forest and Gradient Boosting models for return prediction",
        "LSTM neural networks for time series analysis",
        "Risk parity and Black-Litterman optimization",
        "Real-time model retraining and backtesting"
      ]
    },
    {
      title: "Real-time Risk Analysis",
      description: "Continuous monitoring of portfolio risk with dynamic adjustment recommendations",
      icon: <Shield className="w-6 h-6" />,
      details: [
        "VaR (Value at Risk) and CVaR calculations",
        "Monte Carlo simulations for stress testing",
        "Correlation and concentration risk monitoring",
        "Dynamic hedging recommendations"
      ]
    },
    {
      title: "Automated Rebalancing",
      description: "Smart rebalancing triggered by market conditions and portfolio drift analysis",
      icon: <Zap className="w-6 h-6" />,
      details: [
        "Threshold-based and calendar-based rebalancing",
        "Tax-loss harvesting optimization",
        "Transaction cost analysis",
        "Liquidity-aware execution strategies"
      ]
    },
    {
      title: "AI-Powered Insights",
      description: "Natural language insights generated from complex market analysis and portfolio metrics",
      icon: <Target className="w-6 h-6" />,
      details: [
        "Market sentiment analysis integration",
        "Anomaly detection in portfolio performance",
        "Sector rotation recommendations",
        "Economic indicator impact analysis"
      ]
    }
  ];

  const useCases = [
    {
      title: "Individual Investors",
      description: "Personal wealth management with customized risk profiles and investment goals",
      metrics: ["$50K - $10M+ portfolios", "3-7% annual alpha generation", "15-25% risk reduction"]
    },
    {
      title: "Financial Advisors",
      description: "Professional tools for managing multiple client portfolios with institutional-grade analytics",
      metrics: ["500+ client capacity", "80% time savings", "99.9% uptime SLA"]
    },
    {
      title: "Hedge Funds",
      description: "Quantitative strategies and risk management for alternative investment strategies",
      metrics: ["Multi-strategy support", "Sub-second execution", "Regulatory compliance"]
    },
    {
      title: "Robo-Advisors",
      description: "White-label solution for fintech companies building automated investment platforms",
      metrics: ["API-first architecture", "Scalable to millions", "SOC 2 certified"]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-800 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLocation('/ai-playground')}
            className="text-white hover:bg-white/10 mb-6"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to AI Playground
          </Button>

          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="bg-white/10 p-4 rounded-2xl">
                <Brain className="w-16 h-16 text-white" />
              </div>
            </div>
            <h1 className="text-5xl font-bold text-white mb-6">
              AI Portfolio Platform
            </h1>
            <p className="text-xl text-purple-100 max-w-4xl mx-auto">
              Intelligent portfolio optimization using machine learning algorithms for personalized investment strategies.
              Advanced risk management, automated rebalancing, and AI-powered market insights.
            </p>

            <div className="flex justify-center space-x-4 mt-8">
              <Button
                onClick={() => setLocation('/ai-playground/ai-portfolio-platform')}

                className="bg-white text-purple-700 hover:bg-purple-50 px-8 py-3"
              >
                <Play className="w-5 h-5 mr-2" />
                Live Demo
              </Button>
              <Button variant="outline" className="border-white text-white hover:bg-white/10 px-8 py-3">
                <Download className="w-5 h-5 mr-2" />
                Documentation
              </Button>

            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>

            <TabsTrigger value="features">Features</TabsTrigger>
            <TabsTrigger value="technical">Technical</TabsTrigger>
            <TabsTrigger value="code">Code Examples</TabsTrigger>
            <TabsTrigger value="deployment">Deployment</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            {/* Key Features */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {features.map((feature, index) => (
                <Card key={index} className="tech-card">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className="bg-purple-500/10 p-2 rounded-lg">
                        <div className="text-purple-500">{feature.icon}</div>
                      </div>
                      <span>{feature.title}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4">{feature.description}</p>
                    <ul className="space-y-2">
                      {feature.details.map((detail, idx) => (
                        <li key={idx} className="flex items-start space-x-2 text-sm">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span>{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Architecture Diagram */}
            <Card className="tech-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="w-6 h-6 text-purple-500" />
                  <span>System Architecture</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 p-8 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="bg-purple-100 dark:bg-purple-800/30 p-4 rounded-lg mb-3">
                        <Database className="w-8 h-8 text-purple-600 mx-auto" />
                      </div>
                      <h3 className="font-semibold mb-2">Data Layer</h3>
                      <p className="text-sm text-muted-foreground">Market data ingestion, feature engineering, and model training pipeline</p>
                    </div>
                    <div className="text-center">
                      <div className="bg-blue-100 dark:bg-blue-800/30 p-4 rounded-lg mb-3">
                        <Brain className="w-8 h-8 text-blue-600 mx-auto" />
                      </div>
                      <h3 className="font-semibold mb-2">AI Engine</h3>
                      <p className="text-sm text-muted-foreground">ML models for prediction, optimization algorithms, and risk analysis</p>
                    </div>
                    <div className="text-center">
                      <div className="bg-green-100 dark:bg-green-800/30 p-4 rounded-lg mb-3">
                        <LineChart className="w-8 h-8 text-green-600 mx-auto" />
                      </div>
                      <h3 className="font-semibold mb-2">Application Layer</h3>
                      <p className="text-sm text-muted-foreground">Portfolio management interface, real-time analytics, and client APIs</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Use Cases */}
            <Card className="tech-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="w-6 h-6 text-purple-500" />
                  <span>Use Cases & Applications</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {useCases.map((useCase, index) => (
                    <div key={index} className="p-4 border border-border rounded-lg">
                      <h3 className="font-semibold mb-2">{useCase.title}</h3>
                      <p className="text-sm text-muted-foreground mb-3">{useCase.description}</p>
                      <div className="space-y-1">
                        {useCase.metrics.map((metric, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs mr-2">
                            {metric}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>

              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="features" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card className="tech-card">
                <CardHeader>
                  <CardTitle>Machine Learning Models</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Return Prediction Model</span>
                      <Badge className="bg-green-500 text-white">Active</Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">Random Forest with 500 trees, R² = 0.847</div>

                    <div className="flex justify-between items-center">
                      <span className="text-sm">Risk Assessment Model</span>
                      <Badge className="bg-green-500 text-white">Active</Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">LSTM Neural Network, RMSE = 0.023</div>

                    <div className="flex justify-between items-center">
                      <span className="text-sm">Sentiment Analysis Model</span>
                      <Badge className="bg-blue-500 text-white">Training</Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">BERT-based NLP, Accuracy = 94.2%</div>
                  </div>
                </CardContent>
              </Card>

              <Card className="tech-card">
                <CardHeader>
                  <CardTitle>Optimization Algorithms</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <div className="font-medium text-sm">Mean-Variance Optimization</div>
                      <div className="text-xs text-muted-foreground">Classic Markowitz with ML return estimates</div>
                    </div>
                    <div>
                      <div className="font-medium text-sm">Black-Litterman Model</div>
                      <div className="text-xs text-muted-foreground">Bayesian approach with market views</div>
                    </div>
                    <div>
                      <div className="font-medium text-sm">Risk Parity</div>
                      <div className="text-xs text-muted-foreground">Equal risk contribution weighting</div>
                    </div>
                    <div>
                      <div className="font-medium text-sm">Kelly Criterion</div>
                      <div className="text-xs text-muted-foreground">Optimal position sizing for growth</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="technical" className="space-y-6">
            <Card className="tech-card">
              <CardHeader>
                <CardTitle>Technical Specifications</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="font-semibold mb-4">Infrastructure</h3>
                    <ul className="space-y-2 text-sm">
                      <li>• Cloud-native architecture (AWS/Azure)</li>
                      <li>• Kubernetes orchestration</li>
                      <li>• Real-time data streaming (Kafka)</li>
                      <li>• PostgreSQL with TimescaleDB</li>
                      <li>• Redis for caching</li>
                      <li>• Elasticsearch for search</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-4">Performance</h3>
                    <ul className="space-y-2 text-sm">
                      <li>• 99.9% uptime SLA</li>
                      <li>• &lt;100ms API response time</li>
                      <li>• 10,000+ portfolios per instance</li>
                      <li>• Real-time market data processing</li>
                      <li>• Auto-scaling based on load</li>
                      <li>• Disaster recovery</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="tech-card">
              <CardHeader>
                <CardTitle>API Endpoints</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-muted/50 rounded-lg font-mono text-sm">
                    <div className="text-green-600">GET</div>
                    <div>/api/v1/portfolios/{id}/optimization</div>
                    <div className="text-xs text-muted-foreground mt-1">Get current portfolio optimization recommendations</div>
                  </div>
                  <div className="p-4 bg-muted/50 rounded-lg font-mono text-sm">
                    <div className="text-blue-600">POST</div>
                    <div>/api/v1/portfolios/{id}/rebalance</div>
                    <div className="text-xs text-muted-foreground mt-1">Execute portfolio rebalancing with specified parameters</div>
                  </div>
                  <div className="p-4 bg-muted/50 rounded-lg font-mono text-sm">
                    <div className="text-purple-600">WebSocket</div>
                    <div>/ws/portfolios/{id}/realtime</div>
                    <div className="text-xs text-muted-foreground mt-1">Real-time portfolio updates and market data</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="code" className="space-y-6">
            <Card className="tech-card">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Code Examples</span>
                  <div className="flex space-x-2">
                    <Button
                      variant={activeCodeTab === 'python' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveCodeTab('python')}
                    >
                      Python
                    </Button>
                    <Button
                      variant={activeCodeTab === 'typescript' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveCodeTab('typescript')}
                    >
                      TypeScript
                    </Button>
                    <Button
                      variant={activeCodeTab === 'sql' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveCodeTab('sql')}
                    >
                      SQL
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-900 text-gray-100 p-6 rounded-lg overflow-x-auto">
                  <pre className="text-sm">
                    <code>{codeExamples[activeCodeTab as keyof typeof codeExamples]}</code>
                  </pre>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="deployment" className="space-y-6">
            <Card className="tech-card">
              <CardHeader>
                <CardTitle>Deployment Options</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-6 border border-border rounded-lg">
                    <Globe className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">Cloud SaaS</h3>
                    <p className="text-sm text-muted-foreground mb-4">Fully managed cloud solution with automatic updates and scaling</p>
                    <Button className="w-full">Get Started</Button>
                  </div>
                  <div className="text-center p-6 border border-border rounded-lg">
                    <Cpu className="w-12 h-12 text-green-500 mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">On-Premise</h3>
                    <p className="text-sm text-muted-foreground mb-4">Full control with private deployment in your infrastructure</p>
                    <Button variant="outline" className="w-full">Contact Sales</Button>
                  </div>
                  <div className="text-center p-6 border border-border rounded-lg">
                    <Code className="w-12 h-12 text-purple-500 mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">API Integration</h3>
                    <p className="text-sm text-muted-foreground mb-4">Integrate AI capabilities into your existing platform</p>
                    <Button variant="outline" className="w-full">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Documentation
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="tech-card">
              <CardHeader>
                <CardTitle>Pricing Tiers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="p-6 border border-border rounded-lg">
                    <h3 className="font-semibold text-lg mb-2">Starter</h3>
                    <div className="text-3xl font-bold mb-4">$99<span className="text-sm font-normal">/month</span></div>
                    <ul className="space-y-2 text-sm mb-6">
                      <li>• Up to 5 portfolios</li>
                      <li>• Basic optimization</li>
                      <li>• Email support</li>
                      <li>• Standard SLA</li>
                    </ul>
                    <Button variant="outline" className="w-full">Start Trial</Button>
                  </div>
                  <div className="p-6 border-2 border-purple-500 rounded-lg relative">
                    <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-purple-500 text-white">Popular</Badge>
                    <h3 className="font-semibold text-lg mb-2">Professional</h3>
                    <div className="text-3xl font-bold mb-4">$299<span className="text-sm font-normal">/month</span></div>
                    <ul className="space-y-2 text-sm mb-6">
                      <li>• Up to 50 portfolios</li>
                      <li>• Advanced AI features</li>
                      <li>• Priority support</li>
                      <li>• 99.9% SLA</li>
                    </ul>
                    <Button className="w-full bg-purple-500 text-white">Get Started</Button>
                  </div>
                  <div className="p-6 border border-border rounded-lg">
                    <h3 className="font-semibold text-lg mb-2">Enterprise</h3>
                    <div className="text-3xl font-bold mb-4">Custom</div>
                    <ul className="space-y-2 text-sm mb-6">
                      <li>• Unlimited portfolios</li>
                      <li>• Custom models</li>
                      <li>• Dedicated support</li>
                      <li>• Custom SLA</li>
                    </ul>
                    <Button variant="outline" className="w-full">Contact Sales</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
