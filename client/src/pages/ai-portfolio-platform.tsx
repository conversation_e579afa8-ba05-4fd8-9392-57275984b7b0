import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLocation } from 'wouter';
import {
  Brain,
  TrendingUp,
  AlertTriangle,
  DollarSign,
  BarChart3,
  PieChart,
  Activity,
  ArrowLeft,
  Play,
  Pause,
  Settings,
  Download,
  RefreshCw,
  Target,
  Shield,
  Zap
} from 'lucide-react';

export default function AIPortfolioPlatform() {
  const [, setLocation] = useLocation();
  const [isRunning, setIsRunning] = useState(true);
  const [currentMetrics, setCurrentMetrics] = useState({
    portfolioValue: 2847692,
    dailyReturn: 2.34,
    weeklyReturn: 8.47,
    monthlyReturn: 12.83,
    sharpeRatio: 1.94,
    volatility: 14.2,
    maxDrawdown: -8.7,
    winRate: 73.2
  });

  const [portfolioData, setPortfolioData] = useState([
    { name: 'AAPL', allocation: 22.5, performance: 15.3, risk: 'Low' },
    { name: 'NVDA', allocation: 18.7, performance: 28.9, risk: 'High' },
    { name: 'GOOGL', allocation: 16.2, performance: 12.1, risk: 'Medium' },
    { name: 'MSFT', allocation: 15.8, performance: 18.7, risk: 'Low' },
    { name: 'TSLA', allocation: 12.3, performance: 22.4, risk: 'High' },
    { name: 'META', allocation: 8.9, performance: 9.8, risk: 'Medium' },
    { name: 'Cash', allocation: 5.6, performance: 0.5, risk: 'None' }
  ]);

  const [aiInsights, setAiInsights] = useState([
    {
      type: 'opportunity',
      message: 'AI model suggests increasing NVDA allocation by 3.2% based on market sentiment analysis',
      confidence: 87,
      impact: 'High'
    },
    {
      type: 'risk',
      message: 'Elevated correlation detected between tech holdings - consider diversification',
      confidence: 92,
      impact: 'Medium'
    },
    {
      type: 'rebalance',
      message: 'Portfolio drift detected - optimal rebalancing recommended within 24 hours',
      confidence: 78,
      impact: 'Low'
    }
  ]);

  // Simulate real-time updates
  useEffect(() => {
    if (!isRunning) return;

    const interval = setInterval(() => {
      setCurrentMetrics(prev => ({
        ...prev,
        portfolioValue: prev.portfolioValue + (Math.random() - 0.5) * 1000,
        dailyReturn: prev.dailyReturn + (Math.random() - 0.5) * 0.1,
        weeklyReturn: prev.weeklyReturn + (Math.random() - 0.5) * 0.2,
        sharpeRatio: Math.max(0, prev.sharpeRatio + (Math.random() - 0.5) * 0.05),
        volatility: Math.max(0, prev.volatility + (Math.random() - 0.5) * 0.3)
      }));

      // Update portfolio allocations slightly
      setPortfolioData(prev =>
        prev.map(item => ({
          ...item,
          performance: item.performance + (Math.random() - 0.5) * 0.5
        }))
      );
    }, 2000);

    return () => clearInterval(interval);
  }, [isRunning]);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'High': return 'text-red-500';
      case 'Medium': return 'text-yellow-500';
      case 'Low': return 'text-green-500';
      default: return 'text-gray-500';
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'risk': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'rebalance': return <RefreshCw className="w-4 h-4 text-blue-500" />;
      default: return <Brain className="w-4 h-4 text-purple-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-800 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLocation('/ai-playground')}
                className="text-white hover:bg-white/10"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsRunning(!isRunning)}
                className="text-white hover:bg-white/10"
              >
                {isRunning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                {isRunning ? 'Pause' : 'Resume'}
              </Button>
            </div>
          </div>

          <div className="mt-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-4xl font-bold text-white">AI Portfolio Platform</h1>
                <p className="text-purple-100">Intelligent portfolio optimization with machine learning</p>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white/10 rounded-lg p-4">
                <div className="text-2xl font-bold text-white">
                  ${currentMetrics.portfolioValue.toLocaleString()}
                </div>
                <div className="text-purple-100 text-sm">Portfolio Value</div>
              </div>
              <div className="bg-white/10 rounded-lg p-4">
                <div className={`text-2xl font-bold ${currentMetrics.dailyReturn >= 0 ? 'text-green-300' : 'text-red-300'}`}>
                  {currentMetrics.dailyReturn >= 0 ? '+' : ''}{currentMetrics.dailyReturn.toFixed(2)}%
                </div>
                <div className="text-purple-100 text-sm">Daily Return</div>
              </div>
              <div className="bg-white/10 rounded-lg p-4">
                <div className="text-2xl font-bold text-white">{currentMetrics.sharpeRatio.toFixed(2)}</div>
                <div className="text-purple-100 text-sm">Sharpe Ratio</div>
              </div>
              <div className="bg-white/10 rounded-lg p-4">
                <div className="text-2xl font-bold text-white">{currentMetrics.winRate}%</div>
                <div className="text-purple-100 text-sm">Win Rate</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="portfolio">Portfolio</TabsTrigger>
            <TabsTrigger value="insights">AI Insights</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Performance Metrics */}
              <Card className="tech-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="w-5 h-5 text-green-500" />
                    <span>Performance</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm">Daily Return</span>
                    <span className={`font-bold ${currentMetrics.dailyReturn >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {currentMetrics.dailyReturn >= 0 ? '+' : ''}{currentMetrics.dailyReturn.toFixed(2)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Weekly Return</span>
                    <span className={`font-bold ${currentMetrics.weeklyReturn >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {currentMetrics.weeklyReturn >= 0 ? '+' : ''}{currentMetrics.weeklyReturn.toFixed(2)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Monthly Return</span>
                    <span className={`font-bold ${currentMetrics.monthlyReturn >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {currentMetrics.monthlyReturn >= 0 ? '+' : ''}{currentMetrics.monthlyReturn.toFixed(2)}%
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Risk Metrics */}
              <Card className="tech-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="w-5 h-5 text-yellow-500" />
                    <span>Risk Analysis</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm">Volatility</span>
                    <span className="font-bold">{currentMetrics.volatility.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Max Drawdown</span>
                    <span className="font-bold text-red-500">{currentMetrics.maxDrawdown}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Sharpe Ratio</span>
                    <span className="font-bold text-green-500">{currentMetrics.sharpeRatio.toFixed(2)}</span>
                  </div>
                </CardContent>
              </Card>

              {/* AI Status */}
              <Card className="tech-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Brain className="w-5 h-5 text-purple-500" />
                    <span>AI Engine Status</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Model Performance</span>
                    <Badge className="bg-green-500 text-white">Optimal</Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Prediction Accuracy</span>
                      <span>94.2%</span>
                    </div>
                    <Progress value={94.2} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Data Processing</span>
                      <span>87%</span>
                    </div>
                    <Progress value={87} className="h-2" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="portfolio" className="space-y-6">
            <Card className="tech-card">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <PieChart className="w-5 h-5" />
                    <span>Portfolio Allocation</span>
                  </div>
                  <Button size="sm" variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    Export
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {portfolioData.map((asset, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 text-center">
                          <div className="font-bold">{asset.name}</div>
                        </div>
                        <div>
                          <div className="text-sm text-muted-foreground">Allocation</div>
                          <div className="font-semibold">{asset.allocation}%</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-6">
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">Performance</div>
                          <div className={`font-semibold ${asset.performance >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                            {asset.performance >= 0 ? '+' : ''}{asset.performance.toFixed(1)}%
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">Risk</div>
                          <div className={`font-semibold ${getRiskColor(asset.risk)}`}>
                            {asset.risk}
                          </div>
                        </div>
                        <div className="w-32">
                          <Progress value={asset.allocation * 3} className="h-2" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="insights" className="space-y-6">
            <Card className="tech-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-yellow-500" />
                  <span>AI-Generated Insights</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {aiInsights.map((insight, index) => (
                  <div key={index} className="p-4 border border-border rounded-lg bg-muted/30">
                    <div className="flex items-start space-x-3">
                      {getInsightIcon(insight.type)}
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="outline" className="text-xs">
                            {insight.type.charAt(0).toUpperCase() + insight.type.slice(1)}
                          </Badge>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-muted-foreground">
                              Confidence: {insight.confidence}%
                            </span>
                            <Badge className={`text-xs ${
                              insight.impact === 'High' ? 'bg-red-500' :
                              insight.impact === 'Medium' ? 'bg-yellow-500' : 'bg-green-500'
                            } text-white`}>
                              {insight.impact} Impact
                            </Badge>
                          </div>
                        </div>
                        <p className="text-sm">{insight.message}</p>
                        <div className="mt-2">
                          <Progress value={insight.confidence} className="h-1" />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="tech-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="w-5 h-5" />
                    <span>Model Performance</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-48 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Activity className="w-12 h-12 mx-auto text-purple-500 mb-2" />
                      <div className="text-sm text-muted-foreground">Performance Chart</div>
                      <div className="text-xs text-muted-foreground mt-1">Real-time model metrics</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="tech-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Target className="w-5 h-5" />
                    <span>Prediction Accuracy</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-48 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-4xl font-bold text-green-500 mb-2">94.2%</div>
                      <div className="text-sm text-muted-foreground">Accuracy Rate</div>
                      <div className="text-xs text-muted-foreground mt-1">Last 30 days</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
