import { useRoute } from "wouter";
import AIPortfolioPlatformDemo from "./demos/ai-portfolio-platform";
import EnterpriseDataPipelineDemo from "./demos/enterprise-data-pipeline";
import PredictiveAnalyticsPlatformDemo from "./demos/predictive-analytics-platform";
import CFDAnalysisPlatformDemo from "./demos/cfd-analysis-platform";
import RealtimeBIDashboardDemo from "./demos/realtime-bi-dashboard";
import EnterpriseLLMSolutionDemo from "./demos/enterprise-llm-solution";
import NotFound from "./not-found";

export default function ProjectDetail() {
  const [, params] = useRoute("/projects/:slug");

  if (!params?.slug) {
    return <NotFound />;
  }

  // Route to specific demo pages based on slug
  switch (params.slug) {
    case "ai-portfolio-platform":
      return <AIPortfolioPlatformDemo />;
    case "enterprise-data-pipeline":
      return <EnterpriseDataPipelineDemo />;
    case "predictive-analytics-platform":
      return <PredictiveAnalyticsPlatformDemo />;
    case "cfd-analysis-platform":
      return <CFDAnalysisPlatformDemo />;
    case "realtime-bi-dashboard":
      return <RealtimeBIDashboardDemo />;
    case "enterprise-llm-solution":
      return <EnterpriseLLMSolutionDemo />;
    default:
      return <NotFound />;
  }
}
