import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useLocation } from 'wouter';
import {
  Database,
  Activity,
  Settings,
  AlertCircle,
  CheckCircle,
  TrendingUp,
  BarChart3,
  ArrowLeft,
  Play,
  Pause,
  RefreshCw,
  Upload,
  Download,
  Cpu,
  Zap,
  Shield
} from 'lucide-react';

export default function EnterpriseDataPipelineAI() {
  const [, setLocation] = useLocation();
  const [isRunning, setIsRunning] = useState(true);
  const [pipelineStats, setPipelineStats] = useState({
    totalRecords: 15847293,
    processedToday: 1247682,
    successRate: 99.87,
    avgProcessingTime: 0.23,
    throughput: 5247,
    queueSize: 1823,
    errorCount: 47,
    modelAccuracy: 97.8
  });

  const [pipelineStages] = useState([
    { name: 'Data Ingestion', status: 'active', progress: 94, throughput: '1.2M/hr' },
    { name: 'Data Validation', status: 'active', progress: 89, throughput: '980K/hr' },
    { name: 'Feature Engineering', status: 'active', progress: 76, throughput: '840K/hr' },
    { name: 'ML Training', status: 'training', progress: 45, throughput: '120K/hr' },
    { name: 'Model Deployment', status: 'ready', progress: 100, throughput: '650K/hr' },
    { name: 'Output Generation', status: 'active', progress: 91, throughput: '720K/hr' }
  ]);

  const [alerts] = useState([
    {
      type: 'warning',
      message: 'Data quality score dropped to 94.2% for batch #47821',
      timestamp: '2 minutes ago',
      severity: 'medium'
    },
    {
      type: 'info',
      message: 'New ML model version deployed successfully',
      timestamp: '15 minutes ago',
      severity: 'low'
    },
    {
      type: 'success',
      message: 'Pipeline throughput increased by 12% after optimization',
      timestamp: '1 hour ago',
      severity: 'low'
    }
  ]);

  // Simulate real-time updates
  useEffect(() => {
    if (!isRunning) return;

    const interval = setInterval(() => {
      setPipelineStats(prev => ({
        ...prev,
        totalRecords: prev.totalRecords + Math.floor(Math.random() * 1000),
        processedToday: prev.processedToday + Math.floor(Math.random() * 100),
        throughput: prev.throughput + Math.floor((Math.random() - 0.5) * 200),
        queueSize: Math.max(0, prev.queueSize + Math.floor((Math.random() - 0.5) * 50)),
        avgProcessingTime: Math.max(0.1, prev.avgProcessingTime + (Math.random() - 0.5) * 0.05)
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, [isRunning]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-500';
      case 'training': return 'text-blue-500';
      case 'ready': return 'text-cyan-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'training': return 'bg-blue-500';
      case 'ready': return 'bg-cyan-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'warning': return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'info': return <Database className="w-4 h-4 text-blue-500" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-to-r from-cyan-600 via-blue-700 to-indigo-800 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLocation('/ai-playground')}
                className="text-white hover:bg-white/10"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsRunning(!isRunning)}
                className="text-white hover:bg-white/10"
              >
                {isRunning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                {isRunning ? 'Pause' : 'Resume'}
              </Button>
            </div>
          </div>

          <div className="mt-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="bg-white/10 p-3 rounded-xl">
                <Database className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-4xl font-bold text-white">Enterprise Data Pipeline</h1>
                <p className="text-cyan-100">Scalable ML data processing with automated training</p>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white/10 rounded-lg p-4">
                <div className="text-2xl font-bold text-white">
                  {pipelineStats.totalRecords.toLocaleString()}
                </div>
                <div className="text-cyan-100 text-sm">Total Records</div>
              </div>
              <div className="bg-white/10 rounded-lg p-4">
                <div className="text-2xl font-bold text-white">
                  {pipelineStats.throughput.toLocaleString()}/hr
                </div>
                <div className="text-cyan-100 text-sm">Throughput</div>
              </div>
              <div className="bg-white/10 rounded-lg p-4">
                <div className="text-2xl font-bold text-white">{pipelineStats.successRate}%</div>
                <div className="text-cyan-100 text-sm">Success Rate</div>
              </div>
              <div className="bg-white/10 rounded-lg p-4">
                <div className="text-2xl font-bold text-white">{pipelineStats.modelAccuracy}%</div>
                <div className="text-cyan-100 text-sm">Model Accuracy</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="pipeline" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="pipeline">Pipeline Status</TabsTrigger>
            <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
            <TabsTrigger value="models">ML Models</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="pipeline" className="space-y-6">
            {/* Pipeline Stages */}
            <Card className="tech-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="w-5 h-5 text-cyan-500" />
                  <span>Pipeline Stages</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {pipelineStages.map((stage, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className={`w-3 h-3 rounded-full ${getStatusBadge(stage.status)} animate-pulse`}></div>
                      <div>
                        <div className="font-semibold">{stage.name}</div>
                        <div className="text-sm text-muted-foreground">
                          Status: <span className={getStatusColor(stage.status)}>{stage.status}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Throughput</div>
                        <div className="font-semibold">{stage.throughput}</div>
                      </div>
                      <div className="w-32">
                        <div className="flex justify-between text-sm mb-1">
                          <span>Progress</span>
                          <span>{stage.progress}%</span>
                        </div>
                        <Progress value={stage.progress} className="h-2" />
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Real-time Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="tech-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="w-5 h-5 text-green-500" />
                    <span>Processing Stats</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm">Records Processed Today</span>
                    <span className="font-bold text-green-500">
                      {pipelineStats.processedToday.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Avg Processing Time</span>
                    <span className="font-bold">{pipelineStats.avgProcessingTime.toFixed(2)}s</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Queue Size</span>
                    <span className="font-bold text-blue-500">{pipelineStats.queueSize.toLocaleString()}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="tech-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="w-5 h-5 text-yellow-500" />
                    <span>Quality Metrics</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm">Success Rate</span>
                    <span className="font-bold text-green-500">{pipelineStats.successRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Error Count</span>
                    <span className="font-bold text-red-500">{pipelineStats.errorCount}</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Data Quality Score</span>
                      <span>94.2%</span>
                    </div>
                    <Progress value={94.2} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              <Card className="tech-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Cpu className="w-5 h-5 text-purple-500" />
                    <span>System Resources</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>CPU Usage</span>
                        <span>67%</span>
                      </div>
                      <Progress value={67} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Memory Usage</span>
                        <span>82%</span>
                      </div>
                      <Progress value={82} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Disk I/O</span>
                        <span>45%</span>
                      </div>
                      <Progress value={45} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="monitoring" className="space-y-6">
            <Card className="tech-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <AlertCircle className="w-5 h-5 text-yellow-500" />
                  <span>System Alerts</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {alerts.map((alert, index) => (
                  <div key={index} className="flex items-start space-x-3 p-4 border border-border rounded-lg">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <Badge variant="outline" className="text-xs">
                          {alert.type.charAt(0).toUpperCase() + alert.type.slice(1)}
                        </Badge>
                        <span className="text-xs text-muted-foreground">{alert.timestamp}</span>
                      </div>
                      <p className="text-sm">{alert.message}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="models" className="space-y-6">
            <Card className="tech-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-purple-500" />
                  <span>ML Model Performance</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gradient-to-br from-purple-500/10 to-cyan-500/10 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-4xl font-bold text-purple-500 mb-2">97.8%</div>
                    <div className="text-sm text-muted-foreground">Model Accuracy</div>
                    <div className="text-xs text-muted-foreground mt-1">Last 7 days</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="tech-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="w-5 h-5" />
                    <span>Throughput Analytics</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-48 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Activity className="w-12 h-12 mx-auto text-cyan-500 mb-2" />
                      <div className="text-sm text-muted-foreground">Real-time Analytics</div>
                      <div className="text-xs text-muted-foreground mt-1">Processing trends</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="tech-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="w-5 h-5" />
                    <span>Configuration</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Auto Scaling</span>
                    <Badge className="bg-green-500 text-white">Enabled</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Data Retention</span>
                    <span className="text-sm font-semibold">90 days</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Backup Schedule</span>
                    <span className="text-sm font-semibold">Daily</span>
                  </div>
                  <div className="flex space-x-2 pt-2">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Settings className="w-4 h-4 mr-2" />
                      Configure
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
