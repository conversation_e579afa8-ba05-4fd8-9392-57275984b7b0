import { <PERSON><PERSON><PERSON> } from "lucide-react";
import ComingSoon from "@/components/ComingSoon";

export default function RealtimeBIDashboardDemo() {
  return (
    <ComingSoon
      title="Real-time BI Dashboard"
      description="Interactive business intelligence platform with real-time data visualization and alerting"
      icon={<BarChart className="w-12 h-12" />}
      gradient="from-tech-emerald to-tech-cyan"
      technologies={["Power BI", "D3.js", "Real-time Analytics", "JavaScript", "WebSocket", "SQL"]}
      expectedDate="Q1 2025"
      githubUrl="https://github.com/khiwniti/bi-dashboard"
      features={[
        "Real-time data streaming and visualization",
        "Customizable KPI monitoring dashboards",
        "Automated alert system with notifications",
        "Interactive chart and graph components",
        "Multi-source data integration capabilities",
        "Export and reporting functionality"
      ]}
    />
  );
}

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="pt-24 pb-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={() => setLocation("/")}
              className="mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Portfolio
            </Button>
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-br from-tech-emerald to-tech-cyan text-white">
                <BarChart3 className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">Real-time BI Dashboard</h1>
                <p className="text-muted-foreground">Interactive business intelligence platform with real-time data visualization and alerting</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Badge variant="secondary" className="bg-tech-emerald text-white">Dashboard</Badge>
              <Badge variant="outline">Power BI</Badge>
              <Badge variant="outline">Real-time</Badge>
              <Badge variant="outline">Analytics</Badge>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid lg:grid-cols-4 gap-4 mb-8">
            <Card>
              <CardContent className="p-6 text-center">
                <DollarSign className="w-6 h-6 mx-auto mb-2 text-tech-emerald" />
                <div className="text-2xl font-bold text-tech-emerald">${metrics.totalRevenue.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Total Revenue</div>
                <div className="text-xs text-green-500 mt-1">+12.3% from yesterday</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <Users className="w-6 h-6 mx-auto mb-2 text-tech-cyan" />
                <div className="text-2xl font-bold text-tech-cyan">{metrics.activeUsers.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Active Users</div>
                <div className="text-xs text-green-500 mt-1">+8.7% from last hour</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <TrendingUp className="w-6 h-6 mx-auto mb-2 text-tech-purple" />
                <div className="text-2xl font-bold text-tech-purple">{metrics.conversionRate.toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Conversion Rate</div>
                <div className="text-xs text-green-500 mt-1">+0.4% from last week</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <Activity className="w-6 h-6 mx-auto mb-2 text-tech-orange" />
                <div className="text-2xl font-bold text-tech-orange">${metrics.avgOrderValue.toFixed(2)}</div>
                <div className="text-sm text-muted-foreground">Avg Order Value</div>
                <div className="text-xs text-red-500 mt-1">-2.1% from last month</div>
              </CardContent>
            </Card>
          </div>

          <div className="grid lg:grid-cols-2 gap-6 mb-8">
            {/* Revenue Chart */}
            <Card>
              <CardHeader>
                <h3 className="text-xl font-semibold flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Revenue Trend (Real-time)
                </h3>
              </CardHeader>
              <CardContent>
                <div className="h-64 relative">
                  <svg className="w-full h-full" viewBox="0 0 400 200">
                    {/* Grid lines */}
                    {[0, 1, 2, 3, 4, 5].map(i => (
                      <line
                        key={i}
                        x1="0"
                        y1={i * 40}
                        x2="400"
                        y2={i * 40}
                        stroke="currentColor"
                        strokeOpacity="0.1"
                      />
                    ))}

                    {/* Revenue line */}
                    <polyline
                      fill="none"
                      stroke="url(#revenueGradient)"
                      strokeWidth="3"
                      points={chartData.map((point, index) =>
                        `${(index * 400) / (chartData.length - 1)},${200 - (point.revenue / 500)}`
                      ).join(' ')}
                    />

                    {/* Data points */}
                    {chartData.map((point, index) => (
                      <circle
                        key={index}
                        cx={(index * 400) / (chartData.length - 1)}
                        cy={200 - (point.revenue / 500)}
                        r="4"
                        fill="url(#revenueGradient)"
                      />
                    ))}

                    <defs>
                      <linearGradient id="revenueGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="#10B981" />
                        <stop offset="100%" stopColor="#06B6D4" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>

                <div className="flex justify-between text-xs text-muted-foreground mt-2">
                  {chartData.slice(-6).map((point, index) => (
                    <span key={index}>{point.time}</span>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Alerts & Notifications */}
            <Card>
              <CardHeader>
                <h3 className="text-xl font-semibold flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Real-time Alerts
                </h3>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-green-800">Revenue Target Achieved</span>
                  </div>
                  <div className="text-xs text-green-600 mt-1">Daily revenue exceeded $2.8M target</div>
                </div>

                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium text-blue-800">High Traffic Alert</span>
                  </div>
                  <div className="text-xs text-blue-600 mt-1">User activity increased by 25% in last hour</div>
                </div>

                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm font-medium text-yellow-800">Conversion Drop</span>
                  </div>
                  <div className="text-xs text-yellow-600 mt-1">Mobile conversion rate down 0.3%</div>
                </div>

                <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-sm font-medium text-purple-800">New Data Source</span>
                  </div>
                  <div className="text-xs text-purple-600 mt-1">Social media analytics connected</div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Data Sources */}
          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">Connected Data Sources</h3>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-muted/30 rounded-lg">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg mx-auto mb-2 flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-white" />
                  </div>
                  <div className="font-medium">Google Analytics</div>
                  <div className="text-xs text-green-500">Connected</div>
                </div>

                <div className="text-center p-4 bg-muted/30 rounded-lg">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg mx-auto mb-2 flex items-center justify-center">
                    <DollarSign className="w-6 h-6 text-white" />
                  </div>
                  <div className="font-medium">Salesforce</div>
                  <div className="text-xs text-green-500">Syncing</div>
                </div>

                <div className="text-center p-4 bg-muted/30 rounded-lg">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg mx-auto mb-2 flex items-center justify-center">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <div className="font-medium">HubSpot</div>
                  <div className="text-xs text-green-500">Live</div>
                </div>

                <div className="text-center p-4 bg-muted/30 rounded-lg">
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg mx-auto mb-2 flex items-center justify-center">
                    <Activity className="w-6 h-6 text-white" />
                  </div>
                  <div className="font-medium">Custom API</div>
                  <div className="text-xs text-green-500">Active</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <Footer />
    </div>
  );
}
