import { Database } from "lucide-react";
import ComingSoon from "@/components/ComingSoon";

export default function EnterpriseDataPipelineDemo() {
  return (
    <ComingSoon
      title="Enterprise Data Pipeline"
      description="Scalable Azure Data Factory pipeline processing 1TB+ daily with real-time analytics"
      icon={<Database className="w-12 h-12" />}
      gradient="from-tech-cyan to-primary-600"
      technologies={["Azure Data Factory", "Synapse Analytics", "Apache Spark", "Python", "Power BI", "SQL"]}
      expectedDate="Q1 2025"
      githubUrl="https://github.com/khiwniti/azure-data-pipeline"
      features={[
        "Real-time data processing at scale",
        "Automated ETL workflows with monitoring",
        "Advanced data transformation pipelines",
        "Integration with multiple data sources",
        "Cost optimization and performance tuning",
        "Comprehensive data quality validation"
      ]}
    />
  );
}
