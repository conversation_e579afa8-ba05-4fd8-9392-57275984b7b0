import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  FileText,
  Upload,
  ArrowLeft,
  Download,
  Eye,
  CheckCircle,
  Clock,
  Brain,
  Search,
  Filter
} from "lucide-react";
import { Link } from "wouter";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import PageTransition from "@/components/PageTransition";

interface ExtractedData {
  title: string;
  value: string;
  confidence: number;
}

interface Document {
  id: string;
  name: string;
  type: string;
  size: string;
  status: 'processing' | 'completed' | 'error';
  progress: number;
  extractedData?: ExtractedData[];
  category?: string;
}

export default function DocumentAIDemo() {
  const [documents, setDocuments] = useState<Document[]>([
    {
      id: '1',
      name: 'Invoice_2024_001.pdf',
      type: 'Invoice',
      size: '2.3 MB',
      status: 'completed',
      progress: 100,
      category: 'Financial',
      extractedData: [
        { title: 'Invoice Number', value: 'INV-2024-001', confidence: 98 },
        { title: 'Total Amount', value: '$2,450.00', confidence: 99 },
        { title: 'Due Date', value: '2024-03-15', confidence: 97 },
        { title: 'Vendor', value: 'TechSupply Corp', confidence: 95 },
        { title: 'Tax Amount', value: '$245.00', confidence: 96 }
      ]
    },
    {
      id: '2',
      name: 'Contract_ServiceAgreement.pdf',
      type: 'Contract',
      size: '1.8 MB',
      status: 'completed',
      progress: 100,
      category: 'Legal',
      extractedData: [
        { title: 'Contract Type', value: 'Service Agreement', confidence: 99 },
        { title: 'Start Date', value: '2024-01-01', confidence: 98 },
        { title: 'End Date', value: '2024-12-31', confidence: 98 },
        { title: 'Total Value', value: '$50,000', confidence: 96 },
        { title: 'Parties', value: 'Company A & Company B', confidence: 94 }
      ]
    },
    {
      id: '3',
      name: 'Research_Report.pdf',
      type: 'Report',
      size: '4.1 MB',
      status: 'processing',
      progress: 75,
      category: 'Research'
    }
  ]);

  const [selectedDocument, setSelectedDocument] = useState<Document | null>(documents[0]);
  const [filter, setFilter] = useState<string>('all');

  const handleFileUpload = () => {
    const newDoc: Document = {
      id: Date.now().toString(),
      name: 'new_document.pdf',
      type: 'Unknown',
      size: '1.2 MB',
      status: 'processing',
      progress: 0,
      category: 'Uncategorized'
    };

    setDocuments(prev => [...prev, newDoc]);

    // Simulate processing
    const interval = setInterval(() => {
      setDocuments(prev => prev.map(doc => {
        if (doc.id === newDoc.id && doc.progress < 100) {
          const newProgress = Math.min(doc.progress + 10, 100);
          return {
            ...doc,
            progress: newProgress,
            status: newProgress === 100 ? 'completed' : 'processing',
            ...(newProgress === 100 && {
              type: 'Invoice',
              category: 'Financial',
              extractedData: [
                { title: 'Document Type', value: 'Purchase Order', confidence: 92 },
                { title: 'Amount', value: '$1,200.00', confidence: 94 },
                { title: 'Date', value: '2024-02-20', confidence: 96 }
              ]
            })
          };
        }
        return doc;
      }));
    }, 500);

    setTimeout(() => clearInterval(interval), 5000);
  };

  const stats = [
    { label: 'Documents Processed', value: '10,000+' },
    { label: 'Accuracy Rate', value: '97.5%' },
    { label: 'Processing Speed', value: '&lt; 30s' },
    { label: 'Data Points Extracted', value: '50+' }
  ];

  const categories = ['all', 'Financial', 'Legal', 'Research', 'Uncategorized'];

  const filteredDocuments = filter === 'all'
    ? documents
    : documents.filter(doc => doc.category === filter);

  return (
    <PageTransition>
      <div className="min-h-screen bg-background">
        <Navigation />

        <div className="pt-24 pb-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <div className="flex items-center mb-8">
              <Link href="/consulting-services">
                <Button variant="ghost" className="text-muted-foreground hover:text-foreground">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Services
                </Button>
              </Link>
            </div>

            <div className="text-center mb-12">
              <h1 className="font-display text-4xl font-bold mb-4">
                <span className="bg-gradient-to-r from-primary-800 to-tech-purple bg-clip-text text-transparent">
                  Document Intelligence Demo
                </span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                AI-powered document processing that extracts and analyzes information from complex documents
                with 97.5% accuracy in under 30 seconds.
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              {stats.map((stat, index) => (
                <Card key={index} className="tech-card text-center p-6">
                  <CardContent className="p-0">
                    <div className="text-2xl font-bold text-tech-purple mb-1">{stat.value}</div>
                    <div className="text-sm text-muted-foreground">{stat.label}</div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Main Demo */}
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Document List */}
              <div className="lg:col-span-1">
                <Card className="tech-card">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Documents</CardTitle>
                      <Button
                        size="sm"
                        onClick={handleFileUpload}
                        className="gradient-tech-primary text-white"
                      >
                        <Upload className="w-4 h-4 mr-1" />
                        Upload
                      </Button>
                    </div>

                    {/* Filter */}
                    <div className="flex flex-wrap gap-1 mt-4">
                      {categories.map((category) => (
                        <Button
                          key={category}
                          variant={filter === category ? "default" : "outline"}
                          size="sm"
                          onClick={() => setFilter(category)}
                          className="text-xs"
                        >
                          {category === 'all' ? 'All' : category}
                        </Button>
                      ))}
                    </div>
                  </CardHeader>

                  <CardContent className="p-0">
                    <div className="space-y-1">
                      {filteredDocuments.map((doc) => (
                        <div
                          key={doc.id}
                          className={`p-4 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                            selectedDocument?.id === doc.id ? 'bg-tech-purple/10 border-tech-purple/20' : ''
                          }`}
                          onClick={() => setSelectedDocument(doc)}
                        >
                          <div className="flex items-center space-x-3">
                            <FileText className="w-8 h-8 text-tech-cyan" />
                            <div className="flex-1 min-w-0">
                              <div className="text-sm font-medium truncate">{doc.name}</div>
                              <div className="text-xs text-muted-foreground">{doc.size} • {doc.type}</div>
                              <div className="flex items-center space-x-2 mt-1">
                                <Badge
                                  variant={doc.status === 'completed' ? 'default' : 'secondary'}
                                  className="text-xs"
                                >
                                  {doc.status === 'processing' ? (
                                    <>
                                      <Clock className="w-3 h-3 mr-1" />
                                      Processing
                                    </>
                                  ) : doc.status === 'completed' ? (
                                    <>
                                      <CheckCircle className="w-3 h-3 mr-1" />
                                      Completed
                                    </>
                                  ) : (
                                    'Error'
                                  )}
                                </Badge>
                                {doc.category && (
                                  <Badge variant="outline" className="text-xs">
                                    {doc.category}
                                  </Badge>
                                )}
                              </div>
                              {doc.status === 'processing' && (
                                <Progress value={doc.progress} className="mt-2 h-1" />
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Document Details */}
              <div className="lg:col-span-2">
                {selectedDocument ? (
                  <Card className="tech-card">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-lg">{selectedDocument.name}</CardTitle>
                          <p className="text-sm text-muted-foreground">
                            {selectedDocument.type} • {selectedDocument.size}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4 mr-1" />
                            Preview
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="w-4 h-4 mr-1" />
                            Export
                          </Button>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent>
                      {selectedDocument.status === 'processing' ? (
                        <div className="text-center py-12">
                          <Brain className="w-12 h-12 text-tech-purple mx-auto mb-4 animate-pulse" />
                          <h3 className="text-lg font-semibold mb-2">Processing Document</h3>
                          <p className="text-muted-foreground mb-4">
                            AI is analyzing and extracting data from your document...
                          </p>
                          <Progress value={selectedDocument.progress} className="max-w-xs mx-auto" />
                          <p className="text-sm text-muted-foreground mt-2">
                            {selectedDocument.progress}% complete
                          </p>
                        </div>
                      ) : selectedDocument.extractedData ? (
                        <div>
                          <h3 className="text-lg font-semibold mb-6 flex items-center">
                            <Brain className="w-5 h-5 text-tech-purple mr-2" />
                            Extracted Information
                          </h3>

                          <div className="grid md:grid-cols-2 gap-4">
                            {selectedDocument.extractedData.map((data, index) => (
                              <div key={index} className="border rounded-lg p-4 bg-card/50">
                                <div className="flex items-center justify-between mb-2">
                                  <h4 className="font-medium text-sm">{data.title}</h4>
                                  <Badge
                                    variant={data.confidence >= 95 ? 'default' : 'secondary'}
                                    className="text-xs"
                                  >
                                    {data.confidence}%
                                  </Badge>
                                </div>
                                <p className="text-lg font-semibold text-tech-purple">{data.value}</p>
                                <div className="mt-2">
                                  <Progress value={data.confidence} className="h-1" />
                                </div>
                              </div>
                            ))}
                          </div>

                          <div className="mt-8 p-4 bg-gradient-to-r from-tech-emerald/10 to-tech-cyan/10 rounded-lg">
                            <div className="flex items-center mb-2">
                              <CheckCircle className="w-5 h-5 text-tech-emerald mr-2" />
                              <h4 className="font-semibold">Processing Complete</h4>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Document analyzed successfully with {Math.round(
                                selectedDocument.extractedData.reduce((acc, curr) => acc + curr.confidence, 0) /
                                selectedDocument.extractedData.length
                              )}% average confidence.
                            </p>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold mb-2">No Data Extracted</h3>
                          <p className="text-muted-foreground">
                            This document hasn't been processed yet.
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  <Card className="tech-card">
                    <CardContent className="p-12 text-center">
                      <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Select a Document</h3>
                      <p className="text-muted-foreground">
                        Choose a document from the list to view extracted information.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>

            {/* Features */}
            <div className="mt-16">
              <h2 className="text-2xl font-bold text-center mb-8">Powerful Features</h2>
              <div className="grid md:grid-cols-3 gap-6">
                <Card className="tech-card text-center">
                  <CardContent className="p-6">
                    <Brain className="w-12 h-12 text-tech-purple mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">AI-Powered OCR</h3>
                    <p className="text-sm text-muted-foreground">
                      Advanced optical character recognition with machine learning
                    </p>
                  </CardContent>
                </Card>

                <Card className="tech-card text-center">
                  <CardContent className="p-6">
                    <Filter className="w-12 h-12 text-tech-cyan mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">Smart Categorization</h3>
                    <p className="text-sm text-muted-foreground">
                      Automatic document classification and data validation
                    </p>
                  </CardContent>
                </Card>

                <Card className="tech-card text-center">
                  <CardContent className="p-6">
                    <CheckCircle className="w-12 h-12 text-tech-emerald mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">High Accuracy</h3>
                    <p className="text-sm text-muted-foreground">
                      97.5% accuracy rate with confidence scoring
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* CTA */}
            <div className="mt-16 text-center">
              <Card className="tech-card bg-gradient-to-br from-tech-purple/10 to-tech-cyan/10 max-w-2xl mx-auto">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold mb-4">Ready to Automate Your Document Processing?</h3>
                  <p className="text-muted-foreground mb-6">
                    Save hours of manual work with our AI-powered document intelligence solution.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link href="/consulting-services">
                      <Button className="gradient-tech-primary text-white px-8">
                        Get Custom Solution
                      </Button>
                    </Link>
                    <Button variant="outline" className="px-8">
                      Schedule Demo
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        <Footer />
      </div>
    </PageTransition>
  );
}
