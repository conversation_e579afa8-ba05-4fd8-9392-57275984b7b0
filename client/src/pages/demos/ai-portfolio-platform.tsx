import { Laptop } from "lucide-react";
import ComingSoon from "@/components/ComingSoon";

export default function AIPortfolioPlatformDemo() {
  return (
    <ComingSoon
      title="AI-Powered Portfolio Platform"
      description="Next.js 14 portfolio with integrated AI playground, featuring FastAPI backend and modern architecture"
      icon={<Laptop className="w-12 h-12" />}
      gradient="from-tech-purple to-tech-cyan"
      technologies={["Next.js 14", "FastAPI", "AI/ML", "TypeScript", "PostgreSQL", "Docker"]}
      expectedDate="Q1 2025"
      githubUrl="https://github.com/khiwniti/portfolio-platform"
      features={[
        "Interactive AI playground with multiple demo scenarios",
        "Real-time data visualization and analytics",
        "Responsive design with smooth animations",
        "FastAPI backend with PostgreSQL database",
        "Modern CI/CD pipeline with automated testing",
        "Advanced machine learning integrations"
      ]}
    />
  );
}
        const action = actions[Math.floor(Math.random() * actions.length)];
        const timestamp = new Date().toISOString().substring(0, 19).replace('T', ' ');
        setLogs(prev => [`${timestamp} - ${action}`, ...prev.slice(0, 9)]);
      }
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const startProcessing = () => {
    setDemoData(prev => ({ ...prev, processing: true }));
    setTimeout(() => {
      setDemoData(prev => ({ ...prev, processing: false }));
      const timestamp = new Date().toISOString().substring(0, 19).replace('T', ' ');
      setLogs(prev => [`${timestamp} - Batch processing completed`, ...prev.slice(0, 9)]);
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="pt-24 pb-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={() => setLocation("/")}
              className="mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Portfolio
            </Button>
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-br from-tech-purple to-tech-cyan text-white">
                <Brain className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">AI-Powered Portfolio Platform</h1>
                <p className="text-muted-foreground">Interactive demo of the intelligent portfolio system</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Badge variant="secondary" className="bg-tech-purple text-white">Live Demo</Badge>
              <Badge variant="outline">Next.js 14</Badge>
              <Badge variant="outline">FastAPI</Badge>
              <Badge variant="outline">AI/ML</Badge>
            </div>
          </div>

          {/* Demo Dashboard */}
          <div className="grid lg:grid-cols-3 gap-6 mb-8">
            {/* Real-time Stats */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <h3 className="text-xl font-semibold flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Real-time System Metrics
                </h3>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-tech-cyan">{demoData.records.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">Total Records</div>
                  </div>
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-tech-emerald">{demoData.latency}</div>
                    <div className="text-sm text-muted-foreground">Avg Latency</div>
                  </div>
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-tech-purple">{demoData.throughput}</div>
                    <div className="text-sm text-muted-foreground">Throughput</div>
                  </div>
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-tech-orange">98.7%</div>
                    <div className="text-sm text-muted-foreground">Uptime</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Control Panel */}
            <Card>
              <CardHeader>
                <h3 className="text-xl font-semibold flex items-center gap-2">
                  <Server className="w-5 h-5" />
                  Demo Controls
                </h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={startProcessing}
                  disabled={demoData.processing}
                  className="w-full gradient-tech-primary text-white"
                >
                  {demoData.processing ? "Processing..." : "Run AI Analysis"}
                </Button>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>AI Models</span>
                    <span className="text-green-500">●</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Database</span>
                    <span className="text-green-500">●</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>API Gateway</span>
                    <span className="text-green-500">●</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* System Logs */}
          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold flex items-center gap-2">
                <Code className="w-5 h-5" />
                System Logs
              </h3>
            </CardHeader>
            <CardContent>
              <div className="bg-black text-green-400 font-mono text-sm p-4 rounded-lg max-h-64 overflow-y-auto">
                {logs.map((log, index) => (
                  <div key={index} className="mb-1">{log}</div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Technology Stack */}
          <Card className="mt-6">
            <CardHeader>
              <h3 className="text-xl font-semibold">Technology Stack</h3>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold mb-2 text-tech-cyan">Frontend</h4>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    <li>• Next.js 14 with App Router</li>
                    <li>• TypeScript & Tailwind CSS</li>
                    <li>• Framer Motion animations</li>
                    <li>• React Query for state</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2 text-tech-purple">Backend</h4>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    <li>• FastAPI with Python</li>
                    <li>• PostgreSQL database</li>
                    <li>• Drizzle ORM</li>
                    <li>• JWT authentication</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2 text-tech-emerald">AI/ML</h4>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    <li>• OpenAI GPT integration</li>
                    <li>• Custom ML models</li>
                    <li>• Real-time inference</li>
                    <li>• Model versioning</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <Footer />
    </div>
  );
}
