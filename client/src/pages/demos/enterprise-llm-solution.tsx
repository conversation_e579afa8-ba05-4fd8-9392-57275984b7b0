import { MessageSquare } from "lucide-react";
import ComingSoon from "@/components/ComingSoon";

export default function EnterpriseLLMSolutionDemo() {
  return (
    <ComingSoon
      title="Enterprise LLM Solution"
      description="Custom LLM application for document analysis and automated report generation"
      icon={<MessageSquare className="w-12 h-12" />}
      gradient="from-tech-purple via-tech-cyan to-tech-emerald"
      technologies={["OpenAI", "LangChain", "FastAPI", "Python", "Docker", "Redis"]}
      expectedDate="Q2 2025"
      githubUrl="https://github.com/khiwniti/enterprise-llm"
      features={[
        "Advanced document analysis and processing",
        "Automated report generation with AI",
        "Custom knowledge base integration",
        "Real-time document processing capabilities",
        "Multi-language support and translation",
        "Enterprise-grade security and compliance"
      ]}
    />
  );
}

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        documentsProcessed: prev.documentsProcessed + Math.floor(Math.random() * 3),
        accuracy: Math.max(95, prev.accuracy + (Math.random() - 0.5) * 0.2),
        avgProcessingTime: `${(Math.random() * 0.5 + 1).toFixed(1)}s`
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const processDocument = () => {
    setIsProcessing(true);
    setAnalysisResult("");

    // Simulate LLM processing
    setTimeout(() => {
      const results = [
        "✅ Document Classification: Business Report\n\n📊 Key Insights:\n• Revenue growth: 23% YoY\n• Customer satisfaction: 89%\n• Risk assessment: Low\n\n🎯 Recommendations:\n• Expand marketing in Q4\n• Optimize supply chain\n• Focus on customer retention",
        "✅ Document Type: Legal Agreement\n\n⚖️ Legal Analysis:\n• Contract duration: 24 months\n• Termination clauses: Standard\n• Compliance requirements: GDPR compliant\n\n⚠️ Risk Factors:\n• Penalty clause: Review recommended\n• IP ownership: Needs clarification",
        "✅ Sentiment Analysis Complete\n\n😊 Overall Sentiment: Positive (82%)\n\n📈 Key Themes:\n• Product Quality: 156 mentions (95% positive)\n• Customer Service: 89 mentions (78% positive)\n• Delivery: 67 mentions (71% positive)\n\n💡 Action Items:\n• Improve delivery speed\n• Maintain product quality standards"
      ];

      setAnalysisResult(results[Math.floor(Math.random() * results.length)]);
      setIsProcessing(false);
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="pt-24 pb-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={() => setLocation("/")}
              className="mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Portfolio
            </Button>
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-br from-tech-purple via-tech-cyan to-tech-emerald text-white">
                <MessageSquare className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">Enterprise LLM Solution</h1>
                <p className="text-muted-foreground">Custom LLM application for document analysis and automated report generation</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Badge variant="secondary" className="bg-tech-purple text-white">LLM</Badge>
              <Badge variant="outline">OpenAI</Badge>
              <Badge variant="outline">LangChain</Badge>
              <Badge variant="outline">FastAPI</Badge>
            </div>
          </div>

          {/* LLM Metrics */}
          <div className="grid lg:grid-cols-4 gap-4 mb-8">
            <Card>
              <CardContent className="p-6 text-center">
                <FileText className="w-6 h-6 mx-auto mb-2 text-tech-purple" />
                <div className="text-2xl font-bold text-tech-purple">{metrics.documentsProcessed.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Documents Processed</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <Brain className="w-6 h-6 mx-auto mb-2 text-tech-cyan" />
                <div className="text-2xl font-bold text-tech-cyan">{metrics.accuracy.toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Analysis Accuracy</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <Zap className="w-6 h-6 mx-auto mb-2 text-tech-emerald" />
                <div className="text-2xl font-bold text-tech-emerald">{metrics.avgProcessingTime}</div>
                <div className="text-sm text-muted-foreground">Avg Processing Time</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <MessageSquare className="w-6 h-6 mx-auto mb-2 text-tech-orange" />
                <div className="text-2xl font-bold text-tech-orange">{metrics.activeModels}</div>
                <div className="text-sm text-muted-foreground">Active Models</div>
              </CardContent>
            </Card>
          </div>

          <div className="grid lg:grid-cols-2 gap-6 mb-8">
            {/* Document Analysis */}
            <Card>
              <CardHeader>
                <h3 className="text-xl font-semibold flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  Document Analysis
                </h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <textarea
                  value={sampleText}
                  onChange={(e) => setSampleText(e.target.value)}
                  className="w-full h-32 p-3 border rounded-lg resize-none text-sm"
                  placeholder="Enter text or upload a document to analyze..."
                />

                <Button
                  onClick={processDocument}
                  disabled={isProcessing}
                  className="w-full gradient-tech-primary text-white"
                >
                  {isProcessing ? "Analyzing with LLM..." : "Analyze Document"}
                </Button>

                {(analysisResult || isProcessing) && (
                  <Card className="bg-muted/30">
                    <CardContent className="p-4">
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <Brain className="w-4 h-4" />
                        Analysis Results
                      </h4>
                      {isProcessing ? (
                        <div className="space-y-2">
                          <div className="w-full bg-muted rounded-full h-2">
                            <div className="bg-tech-purple h-2 rounded-full animate-pulse" style={{ width: "70%" }}></div>
                          </div>
                          <p className="text-sm text-muted-foreground">Processing with GPT-4 model...</p>
                        </div>
                      ) : (
                        <pre className="text-sm whitespace-pre-wrap text-foreground">{analysisResult}</pre>
                      )}
                    </CardContent>
                  </Card>
                )}
              </CardContent>
            </Card>

            {/* Recent Documents */}
            <Card>
              <CardHeader>
                <h3 className="text-xl font-semibold flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Recent Documents
                </h3>
              </CardHeader>
              <CardContent className="space-y-3">
                {sampleDocuments.map((doc, index) => (
                  <div key={index} className="p-3 bg-muted/30 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-medium text-sm">{doc.name}</span>
                      <Badge
                        variant={doc.status === "Analyzed" ? "default" : "secondary"}
                        className={doc.status === "Analyzed" ? "bg-green-500" : "bg-blue-500"}
                      >
                        {doc.status}
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground mb-1">Type: {doc.type}</div>
                    <div className="text-xs text-foreground">{doc.insights}</div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Model Information */}
          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">LLM Model Stack</h3>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold mb-3 text-tech-purple">Language Models</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>GPT-4 Turbo:</span>
                      <span className="text-green-500">Active</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Claude-3 Opus:</span>
                      <span className="text-green-500">Active</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Custom Fine-tuned:</span>
                      <span className="text-blue-500">Training</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Embedding Model:</span>
                      <span className="text-green-500">Ready</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-3 text-tech-cyan">Processing Pipeline</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Document Parsing:</span>
                      <span className="font-mono">98.7%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Text Extraction:</span>
                      <span className="font-mono">99.2%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Entity Recognition:</span>
                      <span className="font-mono">96.4%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Sentiment Analysis:</span>
                      <span className="font-mono">94.8%</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-3 text-tech-emerald">Security & Compliance</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Data Encryption:</span>
                      <span className="text-green-500">AES-256</span>
                    </div>
                    <div className="flex justify-between">
                      <span>GDPR Compliant:</span>
                      <span className="text-green-500">Yes</span>
                    </div>
                    <div className="flex justify-between">
                      <span>SOC 2 Type II:</span>
                      <span className="text-green-500">Certified</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Data Retention:</span>
                      <span className="text-blue-500">30 days</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <Footer />
    </div>
  );
}
