import { Wind } from "lucide-react";
import ComingSoon from "@/components/ComingSoon";

export default function CFDAnalysisPlatformDemo() {
  return (
    <ComingSoon
      title="CFD Analysis Platform"
      description="Web-based CFD simulation tool with ANSYS integration for nuclear reactor thermal analysis"
      icon={<Wind className="w-12 h-12" />}
      gradient="from-tech-orange to-primary-700"
      technologies={["ANSYS", "COMSOL", "React", "Python", "OpenFOAM", "WebGL"]}
      expectedDate="Q2 2025"
      githubUrl="https://github.com/khiwniti/cfd-platform"
      features={[
        "Nuclear reactor thermal hydraulics simulation",
        "Advanced heat transfer analysis capabilities",
        "Real-time fluid flow visualization",
        "Safety margin calculations and reporting",
        "Transient analysis and time-dependent simulations",
        "Integration with industry-standard CFD software"
      ]}
    />
  );
}
