import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Bot,
  User,
  Send,
  ArrowLeft,
  MessageSquare,
  Zap,
  Clock,
  Globe
} from "lucide-react";
import { Link } from "wouter";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import PageTransition from "@/components/PageTransition";

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
}

export default function AIChatbotDemo() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! I'm an AI-powered customer support assistant. I can help you with product information, troubleshooting, order status, and general inquiries. How can I assist you today?",
      role: 'assistant',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const sampleResponses = [
    "I'd be happy to help you with that! Let me look up the information for you.",
    "Based on your query, here are the available options that might work for you.",
    "I understand your concern. Let me connect you with the appropriate department for further assistance.",
    "That's a great question! Here's what I found in our knowledge base.",
    "I can help you troubleshoot this issue. Please follow these steps to resolve it."
  ];

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      role: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: sampleResponses[Math.floor(Math.random() * sampleResponses.length)],
        role: 'assistant',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };







  const features = [
    {
      icon: <Zap className="w-5 h-5" />,
      title: "Instant Responses",
      description: "Get immediate answers 24/7"
    },
    {
      icon: <Globe className="w-5 h-5" />,
      title: "Multi-language Support",
      description: "Supports 20+ languages"
    },
    {
      icon: <MessageSquare className="w-5 h-5" />,
      title: "Context Awareness",
      description: "Remembers conversation history"
    }
  ];

  const quickActions = [
    "Check order status",
    "Product information",
    "Technical support",
    "Return policy",
    "Account issues"
  ];

  return (
    <PageTransition>
      <div className="min-h-screen bg-background">
        <Navigation />

        <div className="pt-24 pb-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <div className="flex items-center mb-8">
              <Link href="/consulting-services">
                <Button variant="ghost" className="text-muted-foreground hover:text-foreground">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Services
                </Button>
              </Link>
            </div>

            <div className="text-center mb-12">
              <h1 className="font-display text-4xl font-bold mb-4">
                <span className="bg-gradient-to-r from-primary-800 to-tech-purple bg-clip-text text-transparent">
                  AI Customer Support Demo
                </span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Experience our intelligent chatbot that handles 90% of customer inquiries automatically,
                reducing response time by 80% and improving customer satisfaction.
              </p>
            </div>

            {/* Demo Section */}
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Chat Interface */}
              <div className="lg:col-span-2">
                <Card className="tech-card h-[600px] flex flex-col">
                  <CardHeader className="bg-gradient-to-r from-tech-purple to-tech-cyan text-white">
                    <div className="flex items-center space-x-3">
                      <Bot className="w-8 h-8" />
                      <div>
                        <CardTitle className="text-white">AI Customer Support</CardTitle>
                        <p className="text-white/80 text-sm">Powered by GPT-4 • Online</p>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="flex-1 flex flex-col p-0">
                    {/* Messages */}
                    <div className="flex-1 overflow-y-auto p-6 space-y-4">
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                        >
                          <div className={`flex items-start space-x-3 max-w-[80%] ${
                            message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                          }`}>
                            <Avatar className="w-8 h-8">
                              <AvatarFallback className={message.role === 'user' ? 'bg-tech-cyan' : 'bg-tech-purple'}>
                                {message.role === 'user' ? <User className="w-4 h-4 text-white" /> : <Bot className="w-4 h-4 text-white" />}
                              </AvatarFallback>
                            </Avatar>
                            <div className={`rounded-lg p-3 ${
                              message.role === 'user'
                                ? 'bg-tech-cyan text-white'
                                : 'bg-gray-100 text-gray-900'
                            }`}>
                              <p className="text-sm">{message.content}</p>
                              <p className={`text-xs mt-1 ${
                                message.role === 'user' ? 'text-white/70' : 'text-gray-500'
                              }`}>
                                {message.timestamp.toLocaleTimeString()}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}

                      {isTyping && (
                        <div className="flex justify-start">
                          <div className="flex items-start space-x-3 max-w-[80%]">
                            <Avatar className="w-8 h-8">
                              <AvatarFallback className="bg-tech-purple">
                                <Bot className="w-4 h-4 text-white" />
                              </AvatarFallback>
                            </Avatar>
                            <div className="bg-gray-100 rounded-lg p-3">
                              <div className="flex space-x-1">
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Quick Actions */}
                    <div className="p-4 border-t bg-gray-50">
                      <p className="text-sm text-gray-600 mb-3">Quick Actions:</p>
                      <div className="flex flex-wrap gap-2">
                        {quickActions.map((action, index) => (
                          <Button
                            key={index}
                            variant="outline"
                            size="sm"
                            className="text-xs"
                            onClick={() => setInputMessage(action)}
                          >
                            {action}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Input */}
                    <div className="p-4 border-t">
                      <div className="flex space-x-2">
                        <Input
                          value={inputMessage}
                          onChange={(e) => setInputMessage(e.target.value)}
                          placeholder="Type your message..."
                          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}

                          className="flex-1"
                        />
                        <Button
                          onClick={handleSendMessage}
                          disabled={!inputMessage.trim() || isTyping}
                          className="gradient-tech-primary text-white"
                        >
                          <Send className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Features & Info */}
              <div className="space-y-6">
                {/* Features */}
                <Card className="tech-card">
                  <CardHeader>
                    <CardTitle className="text-lg">Key Features</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {features.map((feature, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="text-tech-cyan mt-0.5">
                          {feature.icon}
                        </div>
                        <div>
                          <h4 className="font-medium text-sm">{feature.title}</h4>
                          <p className="text-xs text-muted-foreground">{feature.description}</p>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                {/* Technologies */}
                <Card className="tech-card">
                  <CardHeader>
                    <CardTitle className="text-lg">Technologies Used</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {["OpenAI GPT-4", "LangChain", "React", "Node.js", "WebSocket", "Redis"].map((tech, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Stats */}
                <Card className="tech-card">
                  <CardHeader>
                    <CardTitle className="text-lg">Performance Metrics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-tech-purple">90%</div>
                      <div className="text-xs text-muted-foreground">Automated Resolution</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-tech-cyan">&lt; 2s</div>
                      <div className="text-xs text-muted-foreground">Response Time</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-tech-emerald">24/7</div>
                      <div className="text-xs text-muted-foreground">Availability</div>
                    </div>
                  </CardContent>
                </Card>

                {/* CTA */}
                <Card className="tech-card bg-gradient-to-br from-tech-purple/10 to-tech-cyan/10">
                  <CardContent className="p-6 text-center">
                    <h3 className="font-semibold mb-2">Interested in this solution?</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Get a custom AI chatbot for your business
                    </p>
                    <Link href="/consulting-services">
                      <Button className="w-full gradient-tech-primary text-white">
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Get Quote
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>

        <Footer />
      </div>
    </PageTransition>
  );
}
