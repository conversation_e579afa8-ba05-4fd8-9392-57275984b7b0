import { Brain } from "lucide-react";
import ComingSoon from "@/components/ComingSoon";

export default function PredictiveAnalyticsPlatformDemo() {
  return (
    <ComingSoon
      title="Predictive Analytics Platform"
      description="Production ML pipeline with automated retraining and A/B testing for business intelligence"
      icon={<Brain className="w-12 h-12" />}
      gradient="from-tech-purple to-tech-emerald"
      technologies={["TensorFlow", "MLOps", "Kubernetes", "Python", "Apache Kafka", "Prometheus"]}
      expectedDate="Q2 2025"
      githubUrl="https://github.com/khiwniti/ml-platform"
      features={[
        "Automated machine learning pipeline orchestration",
        "Real-time model inference and prediction APIs",
        "A/B testing framework for model comparison",
        "Automated model retraining and deployment",
        "Advanced feature engineering and selection",
        "Comprehensive monitoring and alerting system"
      ]}
    />
  );
}
  ]);

  const [isTraining, setIsTraining] = useState(false);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        accuracy: Math.max(90, Math.min(99, prev.accuracy + (Math.random() - 0.5) * 0.5)),
        predictions: prev.predictions + Math.floor(Math.random() * 5)
      }));

      setPredictionData(prev => prev.map(item => ({
        ...item,
        probability: Math.max(0.1, Math.min(0.99, item.probability + (Math.random() - 0.5) * 0.05)),
        change: (Math.random() - 0.5) * 0.1
      })));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const startRetraining = () => {
    setIsTraining(true);
    setTimeout(() => {
      setIsTraining(false);
      setMetrics(prev => ({
        ...prev,
        accuracy: Math.min(99, prev.accuracy + 0.5),
        modelVersion: `v2.1.${parseInt(prev.modelVersion.split('.')[2]) + 1}`,
        lastTrained: "Just now"
      }));
    }, 5000);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="pt-24 pb-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={() => setLocation("/")}
              className="mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Portfolio
            </Button>
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-br from-tech-purple to-tech-emerald text-white">
                <Brain className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">Predictive Analytics Platform</h1>
                <p className="text-muted-foreground">Production ML pipeline with automated retraining and A/B testing</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Badge variant="secondary" className="bg-tech-purple text-white">AI/ML</Badge>
              <Badge variant="outline">TensorFlow</Badge>
              <Badge variant="outline">MLOps</Badge>
              <Badge variant="outline">Kubernetes</Badge>
            </div>
          </div>

          {/* ML Metrics */}
          <div className="grid lg:grid-cols-4 gap-4 mb-8">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-tech-purple">{metrics.accuracy.toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Model Accuracy</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-tech-cyan">{metrics.predictions.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Predictions Today</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-tech-emerald">{metrics.modelVersion}</div>
                <div className="text-sm text-muted-foreground">Model Version</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-tech-orange">{metrics.lastTrained}</div>
                <div className="text-sm text-muted-foreground">Last Training</div>
              </CardContent>
            </Card>
          </div>

          <div className="grid lg:grid-cols-2 gap-6 mb-8">
            {/* Prediction Results */}
            <Card>
              <CardHeader>
                <h3 className="text-xl font-semibold flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Live Predictions
                </h3>
              </CardHeader>
              <CardContent className="space-y-4">
                {predictionData.map((prediction, index) => (
                  <div key={index} className="p-4 bg-muted/30 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-medium">{prediction.category}</span>
                      <div className="flex items-center gap-2">
                        {prediction.trend === "up" && <TrendingUp className="w-4 h-4 text-green-500" />}
                        {prediction.trend === "down" && <TrendingUp className="w-4 h-4 text-red-500 rotate-180" />}
                        {prediction.trend === "stable" && <BarChart className="w-4 h-4 text-yellow-500" />}
                      </div>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2 mb-2">
                      <div
                        className="bg-gradient-to-r from-tech-purple to-tech-cyan h-2 rounded-full transition-all duration-1000"
                        style={{ width: `${prediction.probability * 100}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Confidence: {(prediction.probability * 100).toFixed(1)}%</span>
                      <span className={`${prediction.change >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                        {prediction.change >= 0 ? '+' : ''}{(prediction.change * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Model Controls */}
            <Card>
              <CardHeader>
                <h3 className="text-xl font-semibold flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  Model Management
                </h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={startRetraining}
                  disabled={isTraining}
                  className="w-full gradient-tech-primary text-white"
                >
                  {isTraining ? "Retraining Model..." : "Start Automated Retraining"}
                </Button>

                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                    <span>Training Status</span>
                    <Badge variant={isTraining ? "secondary" : "outline"}>
                      {isTraining ? "Training" : "Idle"}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                    <span>A/B Testing</span>
                    <Badge variant="secondary" className="bg-green-500">Active</Badge>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                    <span>Data Drift Detection</span>
                    <Badge variant="outline">Monitoring</Badge>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                    <span>Model Validation</span>
                    <Badge variant="secondary" className="bg-green-500">Passed</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* MLOps Pipeline */}
          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">MLOps Pipeline</h3>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-6 gap-4 items-center">
                <div className="text-center">
                  <div className="p-3 bg-gradient-to-br from-tech-blue to-tech-cyan rounded-lg mb-2">
                    <BarChart className="w-5 h-5 text-white mx-auto" />
                  </div>
                  <div className="text-sm font-medium">Data Ingestion</div>
                </div>
                <div className="text-center">
                  <div className="text-lg text-tech-cyan">→</div>
                </div>
                <div className="text-center">
                  <div className="p-3 bg-gradient-to-br from-tech-purple to-tech-pink rounded-lg mb-2">
                    <Brain className="w-5 h-5 text-white mx-auto" />
                  </div>
                  <div className="text-sm font-medium">Model Training</div>
                </div>
                <div className="text-center">
                  <div className="text-lg text-tech-cyan">→</div>
                </div>
                <div className="text-center">
                  <div className="p-3 bg-gradient-to-br from-tech-emerald to-tech-cyan rounded-lg mb-2">
                    <Target className="w-5 h-5 text-white mx-auto" />
                  </div>
                  <div className="text-sm font-medium">Validation</div>
                </div>
                <div className="text-center">
                  <div className="p-3 bg-gradient-to-br from-tech-orange to-tech-red rounded-lg mb-2">
                    <Zap className="w-5 h-5 text-white mx-auto" />
                  </div>
                  <div className="text-sm font-medium">Deployment</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <Footer />
    </div>
  );
}
