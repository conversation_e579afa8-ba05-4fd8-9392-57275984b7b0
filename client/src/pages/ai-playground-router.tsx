import React from 'react';
import { useRoute } from 'wouter';
import AIPlaygroundDashboard from './ai-playground-dashboard';
import AIPortfolioPlatform from './ai-portfolio-platform';
import EnterpriseDataPipelineAI from './enterprise-data-pipeline-ai';
import AIPortfolioExplanation from './ai-portfolio-explanation';
import EnterpriseDataPipelineExplanation from './enterprise-data-pipeline-explanation';

export default function AIPlaygroundRouter() {
  const [match, params] = useRoute('/ai-playground/:projectId?/:action?');

  if (!match) {
    return <AIPlaygroundDashboard />;
  }

  const projectId = params?.projectId;
  const action = params?.action;

  // Handle explanation pages
  if (action === 'explanation') {
    switch (projectId) {
      case 'ai-portfolio-platform':
        return <AIPortfolioExplanation />;
      case 'enterprise-data-pipeline':
        return <EnterpriseDataPipelineExplanation />;
      default:
        return <div className="p-8 text-center">Explanation page coming soon for this project</div>;
    }
  }

  // Handle demo pages
  switch (projectId) {
    case 'ai-portfolio-platform':
      return <AIPortfolioPlatform />;
    case 'enterprise-data-pipeline':
      return <EnterpriseDataPipelineAI />;
    case 'predictive-analytics-platform':
      return <div className="p-8 text-center">Predictive Analytics Platform AI - Coming Soon</div>;
    case 'cfd-analysis-platform':
      return <div className="p-8 text-center">CFD Analysis Platform AI - Coming Soon</div>;
    case 'realtime-bi-dashboard':
      return <div className="p-8 text-center">Realtime BI Dashboard AI - Coming Soon</div>;
    case 'enterprise-llm-solution':
      return <div className="p-8 text-center">Enterprise LLM Solution AI - Coming Soon</div>;
    default:
      return <AIPlaygroundDashboard />;
  }
}
