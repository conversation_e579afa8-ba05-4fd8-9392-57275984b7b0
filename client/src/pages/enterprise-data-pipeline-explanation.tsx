import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useLocation } from 'wouter';
import {
  Database,
  ArrowLeft,
  Code,
  Cpu,
  TrendingUp,
  Shield,
  Zap,
  Users,
  Globe,
  Play,
  Download,
  ExternalLink,
  CheckCircle,
  Activity,
  BarChart3,
  Settings,
  Cloud,
  GitBranch,
  MessageSquare
} from 'lucide-react';

export default function EnterpriseDataPipelineExplanation() {
  const [, setLocation] = useLocation();
  const [activeCodeTab, setActiveCodeTab] = useState('python');

  const codeExamples = {
    python: `# Enterprise Data Pipeline with ML Integration
import apache_beam as beam
from apache_beam.options.pipeline_options import PipelineOptions
import pandas as pd
import numpy as np
from sklearn.ensemble import IsolationForest
from tensorflow import keras
import logging
from typing import Dict, List, Any

class DataPipelineConfig:
    """Configuration class for the data pipeline"""
    def __init__(self):
        self.batch_size = 10000
        self.ml_models_path = "/models/"
        self.quality_threshold = 0.95
        self.anomaly_threshold = 0.1
        self.retention_days = 365

class DataQualityValidator:
    """Advanced data quality validation with ML-based anomaly detection"""

    def __init__(self, config: DataPipelineConfig):
        self.config = config
        self.anomaly_detector = IsolationForest(
            contamination=config.anomaly_threshold,
            random_state=42
        )
        self.quality_rules = self._load_quality_rules()

    def validate_batch(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Comprehensive batch validation with ML anomaly detection"""
        results = {
            'total_records': len(df),
            'quality_score': 0.0,
            'anomalies': [],
            'schema_violations': [],
            'completeness_score': 0.0,
            'accuracy_score': 0.0
        }

        # Schema validation
        schema_score = self._validate_schema(df)
        results['schema_violations'] = schema_score['violations']

        # Completeness check
        completeness = self._check_completeness(df)
        results['completeness_score'] = completeness

        # Data type validation
        accuracy = self._validate_data_types(df)
        results['accuracy_score'] = accuracy

        # ML-based anomaly detection
        if len(df) > 100:  # Need sufficient data for ML
            anomalies = self._detect_anomalies(df)
            results['anomalies'] = anomalies

        # Calculate overall quality score
        results['quality_score'] = (
            schema_score['score'] * 0.3 +
            completeness * 0.3 +
            accuracy * 0.2 +
            (1 - len(results['anomalies']) / len(df)) * 0.2
        )

        return results

    def _validate_schema(self, df: pd.DataFrame) -> Dict:
        """Validate data against expected schema"""
        violations = []
        expected_columns = self.quality_rules.get('required_columns', [])

        for col in expected_columns:
            if col not in df.columns:
                violations.append(f"Missing required column: {col}")

        # Check data types
        for col, expected_type in self.quality_rules.get('column_types', {}).items():
            if col in df.columns:
                if not df[col].dtype.name.startswith(expected_type):
                    violations.append(f"Invalid type for {col}: expected {expected_type}")

        return {
            'score': 1.0 - (len(violations) / max(len(expected_columns), 1)),
            'violations': violations
        }

    def _check_completeness(self, df: pd.DataFrame) -> float:
        """Calculate data completeness score"""
        total_cells = df.size
        missing_cells = df.isnull().sum().sum()
        return (total_cells - missing_cells) / total_cells

    def _validate_data_types(self, df: pd.DataFrame) -> float:
        """Validate data types and formats"""
        accuracy_scores = []

        for col in df.columns:
            if df[col].dtype == 'object':
                # String validation
                valid_strings = df[col].str.len() > 0
                accuracy_scores.append(valid_strings.mean())
            elif np.issubdtype(df[col].dtype, np.number):
                # Numeric validation
                finite_values = np.isfinite(df[col])
                accuracy_scores.append(finite_values.mean())

        return np.mean(accuracy_scores) if accuracy_scores else 1.0

    def _detect_anomalies(self, df: pd.DataFrame) -> List[Dict]:
        """ML-based anomaly detection"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) == 0:
            return []

        # Prepare features for anomaly detection
        features = df[numeric_cols].fillna(df[numeric_cols].median())

        # Detect anomalies
        anomaly_labels = self.anomaly_detector.fit_predict(features)
        anomaly_indices = np.where(anomaly_labels == -1)[0]

        anomalies = []
        for idx in anomaly_indices:
            anomalies.append({
                'index': int(idx),
                'score': float(self.anomaly_detector.decision_function([features.iloc[idx]])[0]),
                'values': df.iloc[idx].to_dict()
            })

        return anomalies

    def _load_quality_rules(self) -> Dict:
        """Load data quality rules from configuration"""
        return {
            'required_columns': ['id', 'timestamp', 'value'],
            'column_types': {
                'id': 'int',
                'timestamp': 'datetime',
                'value': 'float'
            },
            'range_checks': {
                'value': {'min': 0, 'max': 1000000}
            }
        }

class MLModelTrainer:
    """Automated ML model training and deployment"""

    def __init__(self, config: DataPipelineConfig):
        self.config = config
        self.models = {}
        self.model_metadata = {}

    def train_time_series_model(self, data: pd.DataFrame, target_col: str) -> Dict:
        """Train LSTM model for time series forecasting"""
        from sklearn.preprocessing import MinMaxScaler
        from tensorflow.keras.models import Sequential
        from tensorflow.keras.layers import LSTM, Dense, Dropout

        # Prepare time series data
        scaler = MinMaxScaler()
        scaled_data = scaler.fit_transform(data[[target_col]])

        # Create sequences for LSTM
        sequence_length = 60
        X, y = self._create_sequences(scaled_data, sequence_length)

        # Split data
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]

        # Build LSTM model
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(sequence_length, 1)),
            Dropout(0.2),
            LSTM(50, return_sequences=True),
            Dropout(0.2),
            LSTM(50),
            Dropout(0.2),
            Dense(1)
        ])

        model.compile(optimizer='adam', loss='mse', metrics=['mae'])

        # Train model
        history = model.fit(
            X_train, y_train,
            epochs=50,
            batch_size=32,
            validation_data=(X_test, y_test),
            verbose=0
        )

        # Evaluate model
        train_score = model.evaluate(X_train, y_train, verbose=0)
        test_score = model.evaluate(X_test, y_test, verbose=0)

        model_info = {
            'model': model,
            'scaler': scaler,
            'sequence_length': sequence_length,
            'train_loss': train_score[0],
            'test_loss': test_score[0],
            'train_mae': train_score[1],
            'test_mae': test_score[1]
        }

        self.models[f'lstm_{target_col}'] = model_info
        return model_info

    def train_classification_model(self, data: pd.DataFrame, target_col: str) -> Dict:
        """Train classification model for categorical prediction"""
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import classification_report, accuracy_score

        # Prepare features
        feature_cols = [col for col in data.columns if col != target_col]
        X = data[feature_cols]
        y = data[target_col]

        # Handle categorical features
        X_encoded = pd.get_dummies(X, drop_first=True)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_encoded, y, test_size=0.2, random_state=42, stratify=y
        )

        # Train model
        model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )

        model.fit(X_train, y_train)

        # Evaluate
        train_pred = model.predict(X_train)
        test_pred = model.predict(X_test)

        model_info = {
            'model': model,
            'feature_columns': X_encoded.columns.tolist(),
            'train_accuracy': accuracy_score(y_train, train_pred),
            'test_accuracy': accuracy_score(y_test, test_pred),
            'feature_importance': dict(zip(
                X_encoded.columns,
                model.feature_importances_
            ))
        }

        self.models[f'classifier_{target_col}'] = model_info
        return model_info

    def _create_sequences(self, data: np.ndarray, seq_length: int):
        """Create sequences for LSTM training"""
        X, y = [], []
        for i in range(seq_length, len(data)):
            X.append(data[i-seq_length:i, 0])
            y.append(data[i, 0])
        return np.array(X), np.array(y)

    def deploy_model(self, model_name: str, version: str) -> bool:
        """Deploy trained model to production"""
        if model_name not in self.models:
            return False

        try:
            model_info = self.models[model_name]
            model_path = f"{self.config.ml_models_path}/{model_name}_v{version}"

            # Save model
            if 'model' in model_info:
                if hasattr(model_info['model'], 'save'):
                    # Keras model
                    model_info['model'].save(f"{model_path}.h5")
                else:
                    # Sklearn model
                    import joblib
                    joblib.dump(model_info['model'], f"{model_path}.pkl")

            # Save metadata
            metadata = {
                'version': version,
                'created_at': pd.Timestamp.now().isoformat(),
                'model_type': type(model_info['model']).__name__,
                'performance_metrics': {
                    k: v for k, v in model_info.items()
                    if isinstance(v, (int, float))
                }
            }

            with open(f"{model_path}_metadata.json", 'w') as f:
                import json
                json.dump(metadata, f, indent=2)

            logging.info(f"Model {model_name} v{version} deployed successfully")
            return True

        except Exception as e:
            logging.error(f"Failed to deploy model {model_name}: {e}")
            return False

class DataPipelineOrchestrator:
    """Main orchestrator for the enterprise data pipeline"""

    def __init__(self, config: DataPipelineConfig):
        self.config = config
        self.validator = DataQualityValidator(config)
        self.ml_trainer = MLModelTrainer(config)
        self.pipeline_stats = {
            'total_processed': 0,
            'quality_scores': [],
            'processing_times': [],
            'error_count': 0
        }

    def create_pipeline(self) -> beam.Pipeline:
        """Create Apache Beam pipeline for data processing"""

        options = PipelineOptions([
            '--runner=DataflowRunner',
            '--project=your-project-id',
            '--region=us-central1',
            '--temp_location=gs://your-bucket/temp',
            '--job_name=enterprise-data-pipeline'
        ])

        pipeline = beam.Pipeline(options=options)

        # Define pipeline steps
        (pipeline
         | 'ReadData' >> beam.io.ReadFromPubSub(topic='your-topic')
         | 'ParseJSON' >> beam.Map(self._parse_json)
         | 'ValidateData' >> beam.Map(self._validate_record)
         | 'TransformData' >> beam.Map(self._transform_record)
         | 'BatchData' >> beam.WindowInto(beam.window.FixedWindows(300))  # 5-minute windows
         | 'ProcessBatch' >> beam.CombineGlobally(self._process_batch)
         | 'WriteResults' >> beam.io.WriteToBigQuery(
             table='your-project:dataset.processed_data',
             write_disposition=beam.io.BigQueryDisposition.WRITE_APPEND
         ))

        return pipeline

    def _parse_json(self, element: str) -> Dict:
        """Parse JSON data with error handling"""
        try:
            import json
            return json.loads(element)
        except json.JSONDecodeError as e:
            logging.error(f"JSON parsing error: {e}")
            return {'error': 'invalid_json', 'raw_data': element}

    def _validate_record(self, record: Dict) -> Dict:
        """Validate individual record"""
        if 'error' in record:
            return record

        # Add validation timestamp
        record['validation_timestamp'] = pd.Timestamp.now().isoformat()

        # Basic field validation
        required_fields = ['id', 'timestamp', 'value']
        for field in required_fields:
            if field not in record:
                record['validation_error'] = f"Missing field: {field}"
                return record

        record['is_valid'] = True
        return record

    def _transform_record(self, record: Dict) -> Dict:
        """Transform and enrich record"""
        if not record.get('is_valid', False):
            return record

        # Add derived fields
        record['processing_timestamp'] = pd.Timestamp.now().isoformat()
        record['day_of_week'] = pd.to_datetime(record['timestamp']).dayofweek
        record['hour_of_day'] = pd.to_datetime(record['timestamp']).hour

        # Apply business logic transformations
        if 'value' in record and isinstance(record['value'], (int, float)):
            record['value_normalized'] = (record['value'] - 100) / 50  # Example normalization
            record['value_category'] = self._categorize_value(record['value'])

        return record

    def _categorize_value(self, value: float) -> str:
        """Categorize numeric values"""
        if value < 50:
            return 'low'
        elif value < 150:
            return 'medium'
        else:
            return 'high'

    def _process_batch(self, records: List[Dict]) -> Dict:
        """Process batch of records with ML pipeline"""
        if not records:
            return {'status': 'empty_batch'}

        # Convert to DataFrame for processing
        df = pd.DataFrame(records)

        # Data quality validation
        quality_results = self.validator.validate_batch(df)

        # Update pipeline statistics
        self.pipeline_stats['total_processed'] += len(records)
        self.pipeline_stats['quality_scores'].append(quality_results['quality_score'])

        # Trigger ML training if conditions are met
        if (len(df) > 1000 and
            quality_results['quality_score'] > self.config.quality_threshold):

            # Train models asynchronously
            self._trigger_ml_training(df)

        return {
            'batch_size': len(records),
            'quality_score': quality_results['quality_score'],
            'anomalies_detected': len(quality_results['anomalies']),
            'processing_timestamp': pd.Timestamp.now().isoformat()
        }

    def _trigger_ml_training(self, df: pd.DataFrame):
        """Trigger ML model training asynchronously"""
        try:
            # Example: Train time series model on value column
            if 'value' in df.columns and len(df) > 100:
                model_info = self.ml_trainer.train_time_series_model(df, 'value')
                logging.info(f"Time series model trained with MAE: {model_info['test_mae']:.4f}")

            # Example: Train classification model
            if 'value_category' in df.columns:
                model_info = self.ml_trainer.train_classification_model(df, 'value_category')
                logging.info(f"Classification model trained with accuracy: {model_info['test_accuracy']:.4f}")

        except Exception as e:
            logging.error(f"ML training failed: {e}")
            self.pipeline_stats['error_count'] += 1

# Usage Example
if __name__ == "__main__":
    # Initialize configuration
    config = DataPipelineConfig()

    # Create pipeline orchestrator
    orchestrator = DataPipelineOrchestrator(config)

    # Create and run pipeline
    pipeline = orchestrator.create_pipeline()

    # Run pipeline
    result = pipeline.run()

    # Wait for completion
    result.wait_until_finish()

    print("Pipeline execution completed!")
    print(f"Statistics: {orchestrator.pipeline_stats}")`,

    docker: `# Multi-stage Docker build for Enterprise Data Pipeline
FROM python:3.9-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV POETRY_VERSION=1.4.2

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    build-essential \\
    curl \\
    git \\
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry==$POETRY_VERSION

# Configure Poetry
ENV POETRY_NO_INTERACTION=1
ENV POETRY_VENV_IN_PROJECT=1
ENV POETRY_CACHE_DIR=/opt/poetry_cache

WORKDIR /app

# Copy dependency files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry install --only=main && rm -rf $POETRY_CACHE_DIR

# Production stage
FROM python:3.9-slim as production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    libgomp1 \\
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r pipeline && useradd -r -g pipeline pipeline

WORKDIR /app

# Copy virtual environment from base stage
COPY --from=base /app/.venv /app/.venv

# Copy application code
COPY src/ ./src/
COPY config/ ./config/
COPY scripts/ ./scripts/

# Set ownership
RUN chown -R pipeline:pipeline /app

# Switch to non-root user
USER pipeline

# Add virtual environment to PATH
ENV PATH="/app/.venv/bin:$PATH"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD python scripts/health_check.py

# Default command
CMD ["python", "-m", "src.pipeline.main"]

---
# docker-compose.yml for local development
version: '3.8'

services:
  # Main pipeline service
  data-pipeline:
    build: .
    environment:
      - ENV=development
      - DATABASE_URL=********************************************/pipeline_db
      - REDIS_URL=redis://redis:6379
      - KAFKA_BROKERS=kafka:9092
    depends_on:
      - postgres
      - redis
      - kafka
    volumes:
      - ./logs:/app/logs
      - ./models:/app/models
    networks:
      - pipeline-network

  # PostgreSQL database
  postgres:
    image: postgres:14
    environment:
      - POSTGRES_DB=pipeline_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - pipeline-network

  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - pipeline-network

  # Apache Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - pipeline-network

  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    networks:
      - pipeline-network

  # Elasticsearch for search and analytics
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - es_data:/usr/share/elasticsearch/data
    networks:
      - pipeline-network

  # Kibana for data visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - pipeline-network

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - pipeline-network

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - pipeline-network

  # Airflow for workflow orchestration
  airflow-webserver:
    image: apache/airflow:2.6.0
    environment:
      - AIRFLOW__CORE__EXECUTOR=LocalExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://postgres:password@postgres:5432/airflow_db
    volumes:
      - ./dags:/opt/airflow/dags
      - ./plugins:/opt/airflow/plugins
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - pipeline-network

volumes:
  postgres_data:
  es_data:
  grafana_data:

networks:
  pipeline-network:
    driver: bridge`,

    terraform: `# Terraform configuration for AWS infrastructure
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Variables
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-west-2"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "project_name" {
  description = "Project name"
  type        = string
  default     = "enterprise-data-pipeline"
}

# VPC Configuration
resource "aws_vpc" "main" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "\${var.project_name}-vpc"
    Environment = var.environment
  }
}

# Subnets
resource "aws_subnet" "private" {
  count             = 2
  vpc_id            = aws_vpc.main.id
  cidr_block        = "10.0.\${count.index + 1}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name        = "\${var.project_name}-private-\${count.index + 1}"
    Environment = var.environment
  }
}

resource "aws_subnet" "public" {
  count                   = 2
  vpc_id                  = aws_vpc.main.id
  cidr_block              = "10.0.\${count.index + 10}.0/24"
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  map_public_ip_on_launch = true

  tags = {
    Name        = "\${var.project_name}-public-\${count.index + 1}"
    Environment = var.environment
  }
}

# Internet Gateway
resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name        = "\${var.project_name}-igw"
    Environment = var.environment
  }
}

# EKS Cluster
resource "aws_eks_cluster" "main" {
  name     = "\${var.project_name}-cluster"
  role_arn = aws_iam_role.eks_cluster.arn
  version  = "1.27"

  vpc_config {
    subnet_ids              = aws_subnet.private[*].id
    endpoint_private_access = true
    endpoint_public_access  = true
  }

  depends_on = [
    aws_iam_role_policy_attachment.eks_cluster_policy,
  ]

  tags = {
    Name        = "\${var.project_name}-eks"
    Environment = var.environment
  }
}

# EKS Node Group
resource "aws_eks_node_group" "main" {
  cluster_name    = aws_eks_cluster.main.name
  node_group_name = "\${var.project_name}-nodes"
  node_role_arn   = aws_iam_role.eks_node_group.arn
  subnet_ids      = aws_subnet.private[*].id

  scaling_config {
    desired_size = 3
    max_size     = 10
    min_size     = 1
  }

  instance_types = ["t3.large"]
  capacity_type  = "ON_DEMAND"

  depends_on = [
    aws_iam_role_policy_attachment.eks_node_group_policy,
    aws_iam_role_policy_attachment.eks_cni_policy,
    aws_iam_role_policy_attachment.eks_registry_policy,
  ]

  tags = {
    Name        = "\${var.project_name}-node-group"
    Environment = var.environment
  }
}

# RDS PostgreSQL
resource "aws_db_instance" "main" {
  identifier     = "\${var.project_name}-db"
  engine         = "postgres"
  engine_version = "14.9"
  instance_class = "db.t3.micro"

  allocated_storage     = 20
  max_allocated_storage = 100
  storage_type          = "gp2"
  storage_encrypted     = true

  db_name  = "pipeline_db"
  username = "postgres"
  password = "secure_password_here"

  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name

  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  skip_final_snapshot = true

  tags = {
    Name        = "\${var.project_name}-db"
    Environment = var.environment
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "main" {
  name       = "\${var.project_name}-cache-subnet"
  subnet_ids = aws_subnet.private[*].id
}

resource "aws_elasticache_cluster" "main" {
  cluster_id           = "\${var.project_name}-cache"
  engine               = "redis"
  node_type            = "cache.t3.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  subnet_group_name    = aws_elasticache_subnet_group.main.name
  security_group_ids   = [aws_security_group.redis.id]

  tags = {
    Name        = "\${var.project_name}-redis"
    Environment = var.environment
  }
}

# MSK Kafka Cluster
resource "aws_msk_cluster" "main" {
  cluster_name           = "\${var.project_name}-kafka"
  kafka_version          = "2.8.1"
  number_of_broker_nodes = 3

  broker_node_group_info {
    instance_type   = "kafka.t3.small"
    ebs_volume_size = 20
    client_subnets  = aws_subnet.private[*].id
    security_groups = [aws_security_group.kafka.id]
  }

  encryption_info {
    encryption_at_rest_kms_key_id = aws_kms_key.kafka.arn
  }

  tags = {
    Name        = "\${var.project_name}-kafka"
    Environment = var.environment
  }
}

# S3 Bucket for data storage
resource "aws_s3_bucket" "data" {
  bucket = "\${var.project_name}-data-\${random_string.bucket_suffix.result}"

  tags = {
    Name        = "\${var.project_name}-data"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_versioning" "data" {
  bucket = aws_s3_bucket.data.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_encryption" "data" {
  bucket = aws_s3_bucket.data.id

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }
}

# CloudWatch Log Group
resource "aws_cloudwatch_log_group" "pipeline" {
  name              = "/aws/eks/\${var.project_name}/pipeline"
  retention_in_days = 7

  tags = {
    Name        = "\${var.project_name}-logs"
    Environment = var.environment
  }
}

# IAM Roles and Policies
resource "aws_iam_role" "eks_cluster" {
  name = "\${var.project_name}-eks-cluster-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "eks.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "eks_cluster_policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"
  role       = aws_iam_role.eks_cluster.name
}

# Security Groups
resource "aws_security_group" "rds" {
  name_prefix = "\${var.project_name}-rds"
  vpc_id      = aws_vpc.main.id

  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.eks_nodes.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "\${var.project_name}-rds-sg"
    Environment = var.environment
  }
}

# Outputs
output "cluster_endpoint" {
  description = "EKS cluster endpoint"
  value       = aws_eks_cluster.main.endpoint
}

output "database_endpoint" {
  description = "RDS instance endpoint"
  value       = aws_db_instance.main.endpoint
  sensitive   = true
}

output "redis_endpoint" {
  description = "ElastiCache Redis endpoint"
  value       = aws_elasticache_cluster.main.cache_nodes[0].address
}

output "kafka_bootstrap_brokers" {
  description = "MSK bootstrap brokers"
  value       = aws_msk_cluster.main.bootstrap_brokers
}

output "s3_bucket" {
  description = "S3 bucket for data storage"
  value       = aws_s3_bucket.data.bucket
}

# Random string for unique bucket naming
resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}`
  };

  const features = [
    {
      title: "Real-time Data Processing",
      description: "Stream processing with Apache Kafka and Beam for millions of records per second",
      icon: <Activity className="w-6 h-6" />,
      details: [
        "Apache Kafka for message streaming",
        "Apache Beam for batch and stream processing",
        "Real-time anomaly detection",
        "Sub-second latency for critical data"
      ]
    },
    {
      title: "Automated ML Training",
      description: "Continuous model training and deployment based on incoming data quality and volume",
      icon: <Cpu className="w-6 h-6" />,
      details: [
        "AutoML pipeline with hyperparameter tuning",
        "A/B testing for model performance",
        "Automated model versioning and rollback",
        "MLOps best practices with monitoring"
      ]
    },
    {
      title: "Data Quality Assurance",
      description: "ML-powered data validation with automated quality scoring and anomaly detection",
      icon: <Shield className="w-6 h-6" />,
      details: [
        "Schema validation and drift detection",
        "Statistical anomaly detection",
        "Data lineage tracking",
        "Quality metrics dashboard"
      ]
    },
    {
      title: "Scalable Infrastructure",
      description: "Cloud-native architecture that scales automatically based on data volume and processing needs",
      icon: <Cloud className="w-6 h-6" />,
      details: [
        "Kubernetes orchestration",
        "Auto-scaling based on metrics",
        "Multi-region deployment",
        "Cost optimization algorithms"
      ]
    }
  ];

  const useCases = [
    {
      title: "Financial Services",
      description: "Real-time fraud detection, risk analytics, and regulatory compliance reporting",
      metrics: ["99.99% uptime", "< 100ms latency", "PCI DSS compliant"]
    },
    {
      title: "E-commerce",
      description: "Customer behavior analysis, recommendation engines, and inventory optimization",
      metrics: ["10M+ events/day", "Real-time personalization", "15% revenue increase"]
    },
    {
      title: "Healthcare",
      description: "Patient data integration, clinical analytics, and research data processing",
      metrics: ["HIPAA compliant", "Multi-source integration", "Real-time alerts"]
    },
    {
      title: "Manufacturing",
      description: "IoT sensor data processing, predictive maintenance, and quality control",
      metrics: ["1B+ sensor readings", "Predictive maintenance", "30% cost reduction"]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-to-r from-cyan-600 via-blue-700 to-indigo-800 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLocation('/ai-playground')}
            className="text-white hover:bg-white/10 mb-6"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to AI Playground
          </Button>

          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="bg-white/10 p-4 rounded-2xl">
                <Database className="w-16 h-16 text-white" />
              </div>
            </div>
            <h1 className="text-5xl font-bold text-white mb-6">
              Enterprise Data Pipeline
            </h1>
            <p className="text-xl text-cyan-100 max-w-4xl mx-auto">
              Scalable data processing pipeline with automated ML model training and real-time analytics.
              Built for enterprise-scale data processing with advanced quality assurance and monitoring.
            </p>

            <div className="flex justify-center space-x-4 mt-8">
              <Button
                onClick={() => setLocation('/ai-playground/enterprise-data-pipeline')}
                className="bg-white text-cyan-700 hover:bg-cyan-50 px-8 py-3"
              >
                <Play className="w-5 h-5 mr-2" />
                Live Demo
              </Button>
              <Button variant="outline" className="border-white text-white hover:bg-white/10 px-8 py-3">
                <Download className="w-5 h-5 mr-2" />
                Architecture Guide
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="architecture">Architecture</TabsTrigger>
            <TabsTrigger value="features">Features</TabsTrigger>
            <TabsTrigger value="code">Implementation</TabsTrigger>
            <TabsTrigger value="deployment">Deployment</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            {/* Key Features */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {features.map((feature, index) => (
                <Card key={index} className="tech-card">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className="bg-cyan-500/10 p-2 rounded-lg">
                        <div className="text-cyan-500">{feature.icon}</div>
                      </div>
                      <span>{feature.title}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4">{feature.description}</p>
                    <ul className="space-y-2">
                      {feature.details.map((detail, idx) => (
                        <li key={idx} className="flex items-start space-x-2 text-sm">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span>{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Use Cases */}
            <Card className="tech-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="w-6 h-6 text-cyan-500" />
                  <span>Industry Applications</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {useCases.map((useCase, index) => (
                    <div key={index} className="p-4 border border-border rounded-lg">
                      <h3 className="font-semibold mb-2">{useCase.title}</h3>
                      <p className="text-sm text-muted-foreground mb-3">{useCase.description}</p>
                      <div className="space-y-1">
                        {useCase.metrics.map((metric, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs mr-2">
                            {metric}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="architecture" className="space-y-6">
            <Card className="tech-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <GitBranch className="w-6 h-6 text-cyan-500" />
                  <span>System Architecture</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 p-8 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="text-center">
                      <div className="bg-green-100 dark:bg-green-800/30 p-4 rounded-lg mb-3">
                        <MessageSquare className="w-8 h-8 text-green-600 mx-auto" />
                      </div>
                      <h3 className="font-semibold mb-2">Data Ingestion</h3>
                      <p className="text-sm text-muted-foreground">Kafka, REST APIs, File uploads, Database CDC</p>
                    </div>
                    <div className="text-center">
                      <div className="bg-blue-100 dark:bg-blue-800/30 p-4 rounded-lg mb-3">
                        <Settings className="w-8 h-8 text-blue-600 mx-auto" />
                      </div>
                      <h3 className="font-semibold mb-2">Processing</h3>
                      <p className="text-sm text-muted-foreground">Apache Beam, Spark, Custom processors</p>
                    </div>
                    <div className="text-center">
                      <div className="bg-purple-100 dark:bg-purple-800/30 p-4 rounded-lg mb-3">
                        <Cpu className="w-8 h-8 text-purple-600 mx-auto" />
                      </div>
                      <h3 className="font-semibold mb-2">ML Pipeline</h3>
                      <p className="text-sm text-muted-foreground">Model training, validation, deployment</p>
                    </div>
                    <div className="text-center">
                      <div className="bg-orange-100 dark:bg-orange-800/30 p-4 rounded-lg mb-3">
                        <Database className="w-8 h-8 text-orange-600 mx-auto" />
                      </div>
                      <h3 className="font-semibold mb-2">Storage</h3>
                      <p className="text-sm text-muted-foreground">PostgreSQL, S3, Elasticsearch</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="tech-card">
                <CardHeader>
                  <CardTitle>Data Flow</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <div className="w-8 h-8 bg-green-500/10 rounded-full flex items-center justify-center">
                      <span className="text-green-500 font-bold text-sm">1</span>
                    </div>
                    <div>
                      <div className="font-medium text-sm">Data Ingestion</div>
                      <div className="text-xs text-muted-foreground">Real-time streams and batch uploads</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <div className="w-8 h-8 bg-blue-500/10 rounded-full flex items-center justify-center">
                      <span className="text-blue-500 font-bold text-sm">2</span>
                    </div>
                    <div>
                      <div className="font-medium text-sm">Quality Validation</div>
                      <div className="text-xs text-muted-foreground">ML-powered quality checks</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <div className="w-8 h-8 bg-purple-500/10 rounded-full flex items-center justify-center">
                      <span className="text-purple-500 font-bold text-sm">3</span>
                    </div>
                    <div>
                      <div className="font-medium text-sm">Processing & Enrichment</div>
                      <div className="text-xs text-muted-foreground">Transform and enhance data</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                    <div className="w-8 h-8 bg-orange-500/10 rounded-full flex items-center justify-center">
                      <span className="text-orange-500 font-bold text-sm">4</span>
                    </div>
                    <div>
                      <div className="font-medium text-sm">Storage & Analytics</div>
                      <div className="text-xs text-muted-foreground">Optimized storage and queries</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="tech-card">
                <CardHeader>
                  <CardTitle>Technology Stack</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Stream Processing</h4>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline">Apache Kafka</Badge>
                      <Badge variant="outline">Apache Beam</Badge>
                      <Badge variant="outline">Apache Spark</Badge>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Machine Learning</h4>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline">TensorFlow</Badge>
                      <Badge variant="outline">PyTorch</Badge>
                      <Badge variant="outline">Scikit-learn</Badge>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Infrastructure</h4>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline">Kubernetes</Badge>
                      <Badge variant="outline">Docker</Badge>
                      <Badge variant="outline">Terraform</Badge>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Monitoring</h4>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline">Prometheus</Badge>
                      <Badge variant="outline">Grafana</Badge>
                      <Badge variant="outline">ELK Stack</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="features" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card className="tech-card">
                <CardHeader>
                  <CardTitle>Processing Capabilities</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Throughput</span>
                      <Badge className="bg-green-500 text-white">5.2M records/hour</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Latency</span>
                      <Badge className="bg-blue-500 text-white">&lt; 100ms</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Data Quality</span>
                      <Badge className="bg-purple-500 text-white">99.87%</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Uptime</span>
                      <Badge className="bg-emerald-500 text-white">99.9%</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="tech-card">
                <CardHeader>
                  <CardTitle>ML Model Performance</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Anomaly Detection</span>
                        <span>97.8% accuracy</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{width: '97.8%'}}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Prediction Model</span>
                        <span>94.5% accuracy</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-blue-500 h-2 rounded-full" style={{width: '94.5%'}}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Classification</span>
                        <span>92.1% accuracy</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-purple-500 h-2 rounded-full" style={{width: '92.1%'}}></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="code" className="space-y-6">
            <Card className="tech-card">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Implementation Examples</span>
                  <div className="flex space-x-2">
                    <Button
                      variant={activeCodeTab === 'python' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveCodeTab('python')}
                    >
                      Python
                    </Button>
                    <Button
                      variant={activeCodeTab === 'docker' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveCodeTab('docker')}
                    >
                      Docker
                    </Button>
                    <Button
                      variant={activeCodeTab === 'terraform' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveCodeTab('terraform')}
                    >
                      Terraform
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-900 text-gray-100 p-6 rounded-lg overflow-x-auto">
                  <pre className="text-sm">
                    <code>{codeExamples[activeCodeTab as keyof typeof codeExamples]}</code>
                  </pre>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="deployment" className="space-y-6">
            <Card className="tech-card">
              <CardHeader>
                <CardTitle>Deployment Options</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-6 border border-border rounded-lg">
                    <Cloud className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">Cloud Native</h3>
                    <p className="text-sm text-muted-foreground mb-4">AWS, Azure, GCP with managed services</p>
                    <Button className="w-full">Deploy Now</Button>
                  </div>
                  <div className="text-center p-6 border border-border rounded-lg">
                    <Database className="w-12 h-12 text-green-500 mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">Hybrid Cloud</h3>
                    <p className="text-sm text-muted-foreground mb-4">Mix of on-premise and cloud resources</p>
                    <Button variant="outline" className="w-full">Contact Sales</Button>
                  </div>
                  <div className="text-center p-6 border border-border rounded-lg">
                    <Shield className="w-12 h-12 text-purple-500 mx-auto mb-4" />
                    <h3 className="font-semibold mb-2">On-Premise</h3>
                    <p className="text-sm text-muted-foreground mb-4">Full control in your data center</p>
                    <Button variant="outline" className="w-full">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Learn More
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="tech-card">
              <CardHeader>
                <CardTitle>Pricing Model</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="p-6 border border-border rounded-lg">
                    <h3 className="font-semibold text-lg mb-2">Starter</h3>
                    <div className="text-3xl font-bold mb-4">$2,999<span className="text-sm font-normal">/month</span></div>
                    <ul className="space-y-2 text-sm mb-6">
                      <li>• Up to 1M records/day</li>
                      <li>• Basic ML models</li>
                      <li>• Email support</li>
                      <li>• 99.5% SLA</li>
                    </ul>
                    <Button variant="outline" className="w-full">Start Trial</Button>
                  </div>
                  <div className="p-6 border-2 border-cyan-500 rounded-lg relative">
                    <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-cyan-500 text-white">Popular</Badge>
                    <h3 className="font-semibold text-lg mb-2">Professional</h3>
                    <div className="text-3xl font-bold mb-4">$9,999<span className="text-sm font-normal">/month</span></div>
                    <ul className="space-y-2 text-sm mb-6">
                      <li>• Up to 10M records/day</li>
                      <li>• Advanced ML pipeline</li>
                      <li>• Priority support</li>
                      <li>• 99.9% SLA</li>
                    </ul>
                    <Button className="w-full bg-cyan-500 text-white">Get Started</Button>
                  </div>
                  <div className="p-6 border border-border rounded-lg">
                    <h3 className="font-semibold text-lg mb-2">Enterprise</h3>
                    <div className="text-3xl font-bold mb-4">Custom</div>
                    <ul className="space-y-2 text-sm mb-6">
                      <li>• Unlimited processing</li>
                      <li>• Custom ML models</li>
                      <li>• Dedicated support</li>
                      <li>• Custom SLA</li>
                    </ul>
                    <Button variant="outline" className="w-full">Contact Sales</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
