import { useState } from "react";
import { <PERSON> } from "wouter";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  Brain,
  Code,
  Wind,
  Globe,
  MessageSquare,
  Rocket,
  ArrowLeft,
  CheckCircle,
  Users,
  Zap,
  Star,
  Clock,
  Shield,
  Target,
  TrendingUp,
  Award,
  Download,
  ExternalLink,
  Calendar,
  Mail,
  Phone
} from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import PageTransition from "@/components/PageTransition";

export default function ConsultingServicesPage() {
  const [selectedService, setSelectedService] = useState("ai-llm-development");

  const services = [
    {
      id: "ai-llm-development",
      title: "AI/LLM Development & Integration",
      description: "Transform your business with cutting-edge AI solutions",
      icon: <Brain className="w-8 h-8" />,
      color: "tech-purple",
      gradient: "from-tech-purple to-tech-cyan",
      pricing: "Starting at $5,000",
      duration: "4-12 weeks",
      complexity: "Advanced",
      detailedDescription: `
        Leverage the power of artificial intelligence to revolutionize your business operations.
        Our AI/LLM development services help you build intelligent systems that understand,
        process, and respond to human language with remarkable accuracy.
      `,
      features: [
        "Custom ChatGPT integrations",
        "WordPress AI plugins",
        "Intelligent chatbots",
        "Document analysis systems",
        "RAG (Retrieval Augmented Generation) implementations",
        "LLM fine-tuning and optimization",
        "AI-powered content generation",
        "Sentiment analysis systems",
        "Multi-language support",
        "Real-time AI responses"
      ],
      technologies: ["OpenAI", "LangChain", "Hugging Face", "FastAPI", "React", "Python", "Transformers"],
      process: [
        "Requirements analysis and AI strategy planning",
        "Data collection and preprocessing",
        "Model selection and fine-tuning",
        "Integration with existing systems",
        "Testing and optimization",
        "Deployment and monitoring"
      ],
      sampleProjects: [
        {
          title: "AI-Powered Customer Support Bot",
          description: "Intelligent chatbot that handles 90% of customer inquiries automatically",
          technologies: ["OpenAI GPT-4", "LangChain", "React", "Node.js"],
          features: ["Natural language understanding", "Context awareness", "Multi-channel support"],
          demo: "/demos/ai-chatbot"
        },
        {
          title: "Document Intelligence System",
          description: "Extract and analyze information from complex documents using AI",
          technologies: ["Hugging Face", "Python", "FastAPI", "OCR"],
          features: ["PDF processing", "Data extraction", "Smart categorization"],
          demo: "/demos/document-ai"
        }
      ],
      testimonial: {
        quote: "The AI solution increased our customer satisfaction by 40% and reduced response time by 80%.",
        author: "Sarah Johnson",
        position: "CTO, TechStart Inc."
      }
    },
    {
      id: "engineering-simulation",
      title: "Engineering Simulation & CFD/FEA",
      description: "Expert computational fluid dynamics and finite element analysis",
      icon: <Wind className="w-8 h-8" />,
      color: "tech-orange",
      gradient: "from-tech-orange to-primary-700",
      pricing: "Starting at $3,000",
      duration: "2-8 weeks",
      complexity: "Expert",
      detailedDescription: `
        Advanced engineering simulation services for complex industrial challenges.
        We provide expert CFD and FEA analysis for nuclear, oil & gas, and mechanical systems
        with precise modeling and optimization capabilities.
      `,
      features: [
        "ANSYS Fluent modeling",
        "COMSOL Multiphysics simulations",
        "Thermal analysis and optimization",
        "Nuclear reactor design support",
        "Heat transfer optimization",
        "Custom simulation tools development",
        "Fluid flow analysis",
        "Structural mechanics",
        "Electromagnetic simulations",
        "Multiphysics coupling"
      ],
      technologies: ["ANSYS", "COMSOL", "MATLAB", "Python", "C++", "Fortran", "OpenFOAM"],
      process: [
        "Problem definition and geometry preparation",
        "Mesh generation and quality assessment",
        "Physics setup and boundary conditions",
        "Solver configuration and validation",
        "Results analysis and interpretation",
        "Report generation and recommendations"
      ],
      sampleProjects: [
        {
          title: "Nuclear Reactor Thermal Analysis",
          description: "Comprehensive thermal-hydraulic analysis for next-generation reactor design",
          technologies: ["ANSYS Fluent", "MATLAB", "Python"],
          features: ["Multi-phase flow", "Heat transfer", "Safety analysis"],
          demo: "/demos/nuclear-simulation"
        },
        {
          title: "Oil Pipeline Flow Optimization",
          description: "CFD analysis to optimize crude oil flow in complex pipeline networks",
          technologies: ["COMSOL", "Python", "OpenFOAM"],
          features: ["Pressure drop analysis", "Flow visualization", "Cost optimization"],
          demo: "/demos/pipeline-cfd"
        }
      ],
      testimonial: {
        quote: "Their simulation expertise saved us 6 months of physical testing and $500K in development costs.",
        author: "Dr. Michael Chen",
        position: "Lead Engineer, Energy Solutions Corp"
      }
    },
    {
      id: "full-stack-development",
      title: "Full-Stack Web Development",
      description: "Professional websites and applications at affordable rates",
      icon: <Code className="w-8 h-8" />,
      color: "tech-emerald",
      gradient: "from-tech-emerald to-tech-cyan",
      pricing: "Starting at $2,000",
      duration: "2-6 weeks",
      complexity: "Intermediate",
      detailedDescription: `
        Modern, responsive web applications built with the latest technologies.
        We create scalable, maintainable solutions that grow with your business,
        from simple landing pages to complex enterprise applications.
      `,
      features: [
        "React/Next.js applications",
        "E-commerce platforms",
        "Content management systems",
        "Database design and optimization",
        "RESTful API development",
        "Mobile-responsive design",
        "SEO optimization",
        "Performance optimization",
        "Security implementation",
        "Cloud deployment"
      ],
      technologies: ["Next.js", "React", "TypeScript", "Node.js", "PostgreSQL", "Tailwind CSS", "Prisma"],
      process: [
        "Requirements gathering and wireframing",
        "UI/UX design and prototyping",
        "Frontend development",
        "Backend API development",
        "Database setup and migration",
        "Testing and deployment"
      ],
      sampleProjects: [
        {
          title: "E-commerce Platform",
          description: "Full-featured online store with payment processing and inventory management",
          technologies: ["Next.js", "Stripe", "PostgreSQL", "Tailwind CSS"],
          features: ["Payment processing", "Inventory management", "Admin dashboard"],
          demo: "/demos/ecommerce"
        },
        {
          title: "SaaS Dashboard",
          description: "Modern analytics dashboard with real-time data visualization",
          technologies: ["React", "D3.js", "Node.js", "WebSocket"],
          features: ["Real-time updates", "Data visualization", "User management"],
          demo: "/demos/saas-dashboard"
        }
      ],
      testimonial: {
        quote: "Delivered a beautiful, fast website that increased our conversion rate by 60%.",
        author: "Lisa Rodriguez",
        position: "Marketing Director, GrowthCo"
      }
    },
    {
      id: "data-engineering",
      title: "Data Engineering & Analytics",
      description: "Scalable data pipelines and business intelligence solutions",
      icon: <Globe className="w-8 h-8" />,
      color: "tech-cyan",
      gradient: "from-tech-cyan to-primary-600",
      pricing: "Starting at $4,000",
      duration: "3-10 weeks",
      complexity: "Advanced",
      detailedDescription: `
        Enterprise-grade data processing and analytics platforms that turn your raw data
        into actionable insights. We build scalable data pipelines, real-time analytics,
        and comprehensive business intelligence solutions.
      `,
      features: [
        "Azure Data Factory pipelines",
        "Real-time data processing",
        "Power BI dashboards",
        "ETL automation",
        "Data warehouse design",
        "Predictive analytics",
        "Machine learning pipelines",
        "Data quality monitoring",
        "Compliance and governance",
        "Cloud-native solutions"
      ],
      technologies: ["Azure", "Apache Spark", "Python", "SQL", "Power BI", "Databricks", "Snowflake"],
      process: [
        "Data audit and architecture planning",
        "Pipeline design and development",
        "Data transformation and cleaning",
        "Analytics and visualization setup",
        "Performance optimization",
        "Monitoring and maintenance"
      ],
      sampleProjects: [
        {
          title: "Real-time Analytics Platform",
          description: "Streaming data platform processing millions of events per day",
          technologies: ["Apache Kafka", "Spark", "Azure", "Power BI"],
          features: ["Real-time processing", "Auto-scaling", "Interactive dashboards"],
          demo: "/demos/realtime-analytics"
        },
        {
          title: "Enterprise Data Warehouse",
          description: "Centralized data warehouse with automated ETL and business intelligence",
          technologies: ["Snowflake", "dbt", "Airflow", "Tableau"],
          features: ["Data modeling", "Automated ETL", "Self-service analytics"],
          demo: "/demos/data-warehouse"
        }
      ],
      testimonial: {
        quote: "Their data platform helped us make data-driven decisions 10x faster than before.",
        author: "James Wilson",
        position: "VP of Analytics, DataCorp"
      }
    }
  ];

  const currentService = services.find(s => s.id === selectedService) || services[0];

  const stats = [
    { icon: <Users className="w-6 h-6" />, value: "50+", label: "Projects Completed" },
    { icon: <CheckCircle className="w-6 h-6" />, value: "98%", label: "Client Satisfaction" },
    { icon: <Clock className="w-6 h-6" />, value: "24hrs", label: "Response Time" },
    { icon: <Award className="w-6 h-6" />, value: "5+", label: "Years Experience" },
  ];

  const benefits = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Enterprise Security",
      description: "Bank-level security with SOC2 compliance and data encryption"
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: "Custom Solutions",
      description: "Tailored solutions designed specifically for your business needs"
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Scalable Architecture",
      description: "Built to grow with your business from startup to enterprise"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Fast Delivery",
      description: "Agile methodology with rapid prototyping and iterative development"
    }
  ];

  return (
    <PageTransition>
      <div className="min-h-screen bg-background">
        <Navigation />

        {/* Header Section */}
        <section className="pt-24 pb-16 bg-gradient-to-br from-background via-primary-50/30 to-background">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center mb-8">
              <Link href="/#consulting-services">
                <Button variant="ghost" className="text-muted-foreground hover:text-foreground">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Portfolio
                </Button>
              </Link>
            </div>

            <div className="text-center max-w-4xl mx-auto">
              <h1 className="font-display text-5xl sm:text-6xl font-bold mb-6">
                <span className="bg-gradient-to-r from-primary-800 to-tech-purple bg-clip-text text-transparent">
                  Consulting Services
                </span>
              </h1>
              <p className="text-xl text-muted-foreground mb-8">
                Transform your business with expert technical consulting. From AI integration to
                complex engineering simulations, we deliver cutting-edge solutions that drive growth.
              </p>

              {/* Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
                {stats.map((stat, index) => (
                  <Card key={index} className="text-center p-6 bg-card/50 backdrop-blur-sm">
                    <CardContent className="p-0">
                      <div className="text-tech-cyan mb-2 flex justify-center">
                        {stat.icon}
                      </div>
                      <div className="text-2xl font-bold mb-1">{stat.value}</div>
                      <div className="text-sm text-muted-foreground">{stat.label}</div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Services Navigation */}
        <section className="py-16 bg-card/30">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <Tabs value={selectedService} onValueChange={setSelectedService} className="w-full">
              <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 mb-12">
                {services.map((service) => (
                  <TabsTrigger
                    key={service.id}
                    value={service.id}
                    className="flex flex-col items-center p-4 h-auto"
                  >
                    <div className="text-tech-cyan mb-1">{service.icon}</div>
                    <span className="text-xs font-medium text-center leading-tight">
                      {service.title.split(' ').slice(0, 2).join(' ')}
                    </span>
                  </TabsTrigger>
                ))}
              </TabsList>

              {/* Service Detail Content */}
              {services.map((service) => (
                <TabsContent key={service.id} value={service.id} className="mt-0">
                  <div className="grid lg:grid-cols-3 gap-8">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-8">
                      {/* Service Overview */}
                      <Card className="tech-card">
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div className={`p-3 rounded-lg bg-gradient-to-br ${service.gradient}`}>
                                <div className="text-white">{service.icon}</div>
                              </div>
                              <div>
                                <CardTitle className="text-2xl">{service.title}</CardTitle>
                                <p className="text-muted-foreground">{service.description}</p>
                              </div>
                            </div>
                            <Badge className={`bg-${service.color} text-white`}>
                              {service.complexity}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground leading-relaxed">
                            {service.detailedDescription}
                          </p>
                        </CardContent>
                      </Card>

                      {/* Features & Technologies */}
                      <div className="grid md:grid-cols-2 gap-6">
                        <Card className="tech-card">
                          <CardHeader>
                            <CardTitle className="text-lg">What's Included</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              {service.features.map((feature, index) => (
                                <div key={index} className="flex items-center text-sm">
                                  <CheckCircle className="w-4 h-4 text-tech-emerald mr-3 flex-shrink-0" />
                                  {feature}
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>

                        <Card className="tech-card">
                          <CardHeader>
                            <CardTitle className="text-lg">Technologies Used</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="flex flex-wrap gap-2">
                              {service.technologies.map((tech, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {tech}
                                </Badge>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      </div>

                      {/* Process */}
                      <Card className="tech-card">
                        <CardHeader>
                          <CardTitle className="text-lg">Our Process</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {service.process.map((step, index) => (
                              <div key={index} className="flex items-start">
                                <div className={`w-8 h-8 rounded-full bg-gradient-to-br ${service.gradient} text-white flex items-center justify-center text-sm font-bold mr-4 flex-shrink-0`}>
                                  {index + 1}
                                </div>
                                <p className="text-sm text-muted-foreground pt-1">{step}</p>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>

                      {/* Sample Projects */}
                      <Card className="tech-card">
                        <CardHeader>
                          <CardTitle className="text-lg">Sample Projects</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-6">
                            {service.sampleProjects.map((project, index) => (
                              <div key={index} className="border rounded-lg p-6 bg-card/50">
                                <div className="flex items-start justify-between mb-4">
                                  <div>
                                    <h4 className="font-semibold text-lg mb-2">{project.title}</h4>
                                    <p className="text-muted-foreground text-sm mb-3">{project.description}</p>
                                  </div>
                                  <Button variant="outline" size="sm" className="ml-4">




                                    <Link href={project.demo}>
                                      <div className="flex items-center">
                                        <ExternalLink className="w-4 h-4 mr-1" />
                                        View Demo
                                      </div>
                                    </Link>
                                  </Button>
                                </div>

                                <div className="grid md:grid-cols-2 gap-4">
                                  <div>
                                    <h5 className="font-medium text-sm mb-2">Technologies</h5>
                                    <div className="flex flex-wrap gap-1">
                                      {project.technologies.map((tech, techIndex) => (
                                        <Badge key={techIndex} variant="outline" className="text-xs">
                                          {tech}
                                        </Badge>
                                      ))}
                                    </div>
                                  </div>
                                  <div>
                                    <h5 className="font-medium text-sm mb-2">Key Features</h5>
                                    <ul className="text-xs text-muted-foreground space-y-1">
                                      {project.features.map((feature, featureIndex) => (
                                        <li key={featureIndex} className="flex items-center">
                                          <Star className="w-3 h-3 mr-1 text-tech-emerald" />
                                          {feature}
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>

                      {/* Testimonial */}
                      <Card className="tech-card bg-gradient-to-br from-primary-50/50 to-tech-purple/5">
                        <CardContent className="p-8">
                          <div className="flex items-start space-x-4">
                            <div className="text-tech-purple">
                              <MessageSquare className="w-8 h-8" />
                            </div>
                            <div>
                              <blockquote className="text-lg italic mb-4">
                                "{service.testimonial.quote}"
                              </blockquote>
                              <div>
                                <div className="font-semibold">{service.testimonial.author}</div>
                                <div className="text-sm text-muted-foreground">{service.testimonial.position}</div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                      {/* Pricing Card */}
                      <Card className="tech-card sticky top-8">
                        <CardHeader>
                          <CardTitle className="text-lg">Project Details</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <div className="text-2xl font-bold text-tech-purple mb-1">
                              {service.pricing}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Final cost depends on project scope
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-3">
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">Duration:</span>
                              <span className="text-sm font-medium">{service.duration}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">Complexity:</span>
                              <Badge variant="outline" className="text-xs">
                                {service.complexity}
                              </Badge>
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-3">
                            <Button className="w-full gradient-tech-primary text-white">
                              <Calendar className="w-4 h-4 mr-2" />
                              Schedule Consultation
                            </Button>
                            <Button variant="outline" className="w-full">
                              <Download className="w-4 h-4 mr-2" />
                              Download Proposal
                            </Button>
                          </div>
                        </CardContent>
                      </Card>

                      {/* Contact Card */}
                      <Card className="tech-card">
                        <CardHeader>
                          <CardTitle className="text-lg">Get Started Today</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <p className="text-sm text-muted-foreground">
                            Ready to discuss your project? Let's schedule a free consultation.
                          </p>
                          <div className="space-y-2">
                            <Button variant="ghost" className="w-full justify-start text-sm">
                              <Mail className="w-4 h-4 mr-2" />
                              <EMAIL>
                            </Button>
                            <Button variant="ghost" className="w-full justify-start text-sm">
                              <Phone className="w-4 h-4 mr-2" />
                              +****************
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 bg-background">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="font-display text-3xl font-bold mb-4">Why Choose Our Services?</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                We deliver exceptional results through proven methodologies and cutting-edge technology.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {benefits.map((benefit, index) => (
                <Card key={index} className="tech-card text-center">
                  <CardContent className="p-6">
                    <div className="text-tech-cyan mb-4 flex justify-center">
                      {benefit.icon}
                    </div>
                    <h3 className="font-semibold mb-2">{benefit.title}</h3>
                    <p className="text-sm text-muted-foreground">{benefit.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-br from-tech-purple/10 to-tech-cyan/10">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="font-display text-3xl font-bold mb-4">Ready to Start Your Project?</h2>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              Let's discuss your requirements and create a custom solution that drives your business forward.
              Free consultation for all new projects.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="gradient-tech-primary text-white px-8 py-3 hover:scale-105 transition-transform"
              >
                <MessageSquare className="w-5 h-5 mr-2" />
                Schedule Free Consultation
              </Button>
              <Link href="/#projects">
                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-tech-cyan text-tech-cyan hover:bg-tech-cyan hover:text-white px-8 py-3"
                >
                  View Portfolio
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </PageTransition>
  );
}
