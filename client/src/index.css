@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&family=Poppins:wght@400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(248, 100%, 99%);
  --foreground: hsl(210, 25%, 7.8431%);
  --card: hsl(180, 6.6667%, 97.0588%);
  --card-foreground: hsl(210, 25%, 7.8431%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(210, 25%, 7.8431%);
  --primary: hsl(224, 76%, 48%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(210, 25%, 7.8431%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --muted: hsl(240, 1.9608%, 90%);
  --muted-foreground: hsl(210, 25%, 7.8431%);
  --accent: hsl(211.5789, 51.3514%, 92.7451%);
  --accent-foreground: hsl(224, 76%, 48%);
  --destructive: hsl(356.3033, 90.5579%, 54.3137%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --border: hsl(201.4286, 30.4348%, 90.9804%);
  --input: hsl(200, 23.0769%, 97.4510%);
  --ring: hsl(224, 76%, 48%);
  --chart-1: hsl(224, 76%, 48%);
  --chart-2: hsl(159.7826, 100%, 36.0784%);
  --chart-3: hsl(42.0290, 92.8251%, 56.2745%);
  --chart-4: hsl(147.1429, 78.5047%, 41.9608%);
  --chart-5: hsl(341.4894, 75.2000%, 50.9804%);
  --sidebar: hsl(180, 6.6667%, 97.0588%);
  --sidebar-foreground: hsl(210, 25%, 7.8431%);
  --sidebar-primary: hsl(224, 76%, 48%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(211.5789, 51.3514%, 92.7451%);
  --sidebar-accent-foreground: hsl(224, 76%, 48%);
  --sidebar-border: hsl(205.0000, 25.0000%, 90.5882%);
  --sidebar-ring: hsl(224, 76%, 48%);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: 'JetBrains Mono', Consolas, monospace;
  --font-display: 'Poppins', system-ui, sans-serif;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(224, 76%, 48%);
  --shadow-xs: 0px 2px 0px 0px hsl(224, 76%, 48%);
  --shadow-sm: 0px 2px 0px 0px hsl(224, 76%, 48%), 0px 1px 2px -1px hsl(224, 76%, 48%);
  --shadow: 0px 2px 0px 0px hsl(224, 76%, 48%), 0px 1px 2px -1px hsl(224, 76%, 48%);
  --shadow-md: 0px 2px 0px 0px hsl(224, 76%, 48%), 0px 2px 4px -1px hsl(224, 76%, 48%);
  --shadow-lg: 0px 2px 0px 0px hsl(224, 76%, 48%), 0px 4px 6px -1px hsl(224, 76%, 48%);
  --shadow-xl: 0px 2px 0px 0px hsl(224, 76%, 48%), 0px 8px 10px -1px hsl(224, 76%, 48%);
  --shadow-2xl: 0px 2px 0px 0px hsl(224, 76%, 48%);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  /* Design System Colors */
  --primary-blue: hsl(224, 76%, 48%);
  --primary-dark: hsl(224, 76%, 42%);
  --primary-light: hsl(211, 100%, 95%);
  --tech-cyan: hsl(195, 95%, 42%);
  --tech-emerald: hsl(147, 74%, 41%);
  --tech-purple: hsl(258, 87%, 66%);
  --tech-orange: hsl(24, 95%, 53%);
  --neutral-50: hsl(248, 100%, 99%);
  --neutral-100: hsl(240, 20%, 96%);
  --neutral-800: hsl(215, 16%, 18%);
  --neutral-900: hsl(222, 84%, 5%);

  /* Dark theme colors */
  --dark-primary: hsl(222, 84%, 5%);
  --dark-secondary: hsl(215, 28%, 17%);
  --dark-tertiary: hsl(215, 25%, 27%);
  --dark-text-primary: hsl(248, 100%, 99%);
  --dark-text-secondary: hsl(215, 16%, 84%);
}

.dark {
  --background: hsl(222, 84%, 5%);
  --foreground: hsl(248, 100%, 99%);
  --card: hsl(215, 28%, 17%);
  --card-foreground: hsl(248, 100%, 99%);
  --popover: hsl(222, 84%, 5%);
  --popover-foreground: hsl(248, 100%, 99%);
  --primary: hsl(224, 76%, 48%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(215, 16%, 84%);
  --secondary-foreground: hsl(210, 25%, 7.8431%);
  --muted: hsl(215, 28%, 17%);
  --muted-foreground: hsl(215, 16%, 47%);
  --accent: hsl(215, 28%, 17%);
  --accent-foreground: hsl(224, 76%, 48%);
  --destructive: hsl(356.3033, 90.5579%, 54.3137%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --border: hsl(215, 16%, 27%);
  --input: hsl(215, 28%, 17%);
  --ring: hsl(224, 76%, 48%);
  --chart-1: hsl(224, 76%, 48%);
  --chart-2: hsl(159.7826, 100%, 36.0784%);
  --chart-3: hsl(42.0290, 92.8251%, 56.2745%);
  --chart-4: hsl(147.1429, 78.5047%, 41.9608%);
  --chart-5: hsl(341.4894, 75.2000%, 50.9804%);
  --sidebar: hsl(215, 28%, 17%);
  --sidebar-foreground: hsl(248, 100%, 99%);
  --sidebar-primary: hsl(224, 76%, 48%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(215, 28%, 17%);
  --sidebar-accent-foreground: hsl(224, 76%, 48%);
  --sidebar-border: hsl(215, 16%, 27%);
  --sidebar-ring: hsl(224, 76%, 48%);
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px; /* Account for fixed navbar */
  }

  body {
    @apply font-sans antialiased bg-background text-foreground transition-colors duration-300;
  }

  /* Enhanced scroll animations for sections */
  section {
    @apply transition-all duration-700 ease-out;
    transform: translateY(0);
  }

  /* Fade-in animation for sections */
  .section-fade-in {
    @apply opacity-0 translate-y-8 transition-all duration-700 ease-out;
  }

  .section-fade-in.in-view {
    @apply opacity-100 translate-y-0;
  }

  /* Smooth scroll reveal animations */
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(50px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .animate-slide-up {
    animation: slideInUp 0.8s ease-out forwards;
  }

  .animate-slide-left {
    animation: slideInLeft 0.8s ease-out forwards;
  }

  .animate-slide-right {
    animation: slideInRight 0.8s ease-out forwards;
  }
}

@layer components {
  /* Tech Card Component */
  .tech-card {
    @apply backdrop-blur-sm border border-white/10 dark:border-gray-700/50 transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  /* Gradient Backgrounds */
  .gradient-tech-primary {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--tech-cyan) 100%);
  }

  .gradient-ai-ml {
    background: linear-gradient(135deg, var(--tech-purple) 0%, var(--tech-cyan) 50%, var(--tech-emerald) 100%);
  }

  .gradient-data-flow {
    background: linear-gradient(90deg, var(--tech-cyan), var(--tech-emerald), var(--tech-purple), var(--tech-orange));
  }

  /* Glass Morphism */
  .glass-morphism {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-morphism {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Typing Animation */
  .typing-animation {
    overflow: hidden;
    border-right: 2px solid var(--tech-cyan);
    white-space: nowrap;
    animation: typing 4s steps(40) infinite, blink 1s infinite;
  }

  /* Skill Bar Animation */
  .skill-bar {
    position: relative;
    overflow: hidden;
  }

  .skill-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: skill-shine 2s infinite;
  }

  /* Processing Animation */
  .processing-animation {
    position: relative;
    overflow: hidden;
  }

  .processing-animation::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
    animation: dataFlow 2s infinite;
  }

  /* Neural Dots */
  .neural-dot {
    width: 6px;
    height: 6px;
    background: var(--tech-cyan);
    border-radius: 50%;
    position: absolute;
    animation: neural-pulse 2s infinite;
  }
}

@layer utilities {
  /* Custom animations */
  @keyframes typing {
    0%, 20% { width: 0; }
    40%, 60% { width: 100%; }
    80%, 100% { width: 0; }
  }

  @keyframes blink {
    50% { border-color: transparent; }
  }

  @keyframes dataFlow {
    0%, 100% { transform: translateX(-100%); opacity: 0; }
    50% { transform: translateX(0%); opacity: 1; }
  }

  @keyframes aiPulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.05); opacity: 1; }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes skill-shine {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  @keyframes neural-pulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
  }

  @keyframes gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* Enhanced Navigation Animations */
  @keyframes navGlow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(6, 182, 212, 0.3), 0 0 10px rgba(6, 182, 212, 0.2);
    }
    50% {
      box-shadow: 0 0 20px rgba(6, 182, 212, 0.6), 0 0 30px rgba(6, 182, 212, 0.4);
    }
  }

  @keyframes navShimmer {
    0% { transform: translateX(-100%) skewX(-15deg); }
    100% { transform: translateX(200%) skewX(-15deg); }
  }

  @keyframes navFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-2px) scale(1.05); }
  }

  @keyframes navPulse {
    0%, 100% { opacity: 0.8; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
  }

  @keyframes navRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes navBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
  }

  /* Animation utilities */
  .animate-data-flow {
    animation: dataFlow 3s ease-in-out infinite;
  }

  .animate-ai-pulse {
    animation: aiPulse 2s ease-in-out infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-gradient {
    animation: gradient 15s ease infinite;
  }

  .animate-typing {
    animation: typing 4s steps(40) infinite, blink 1s infinite;
  }

  /* Enhanced Navigation Animation Classes */
  .animate-nav-glow {
    animation: navGlow 2s ease-in-out infinite;
  }

  .animate-nav-shimmer {
    animation: navShimmer 2s ease-in-out infinite;
  }

  .animate-nav-float {
    animation: navFloat 3s ease-in-out infinite;
  }

  .animate-nav-pulse {
    animation: navPulse 2s ease-in-out infinite;
  }

  .animate-nav-rotate {
    animation: navRotate 10s linear infinite;
  }

  .animate-nav-bounce {
    animation: navBounce 1s ease-in-out;
  }
}
