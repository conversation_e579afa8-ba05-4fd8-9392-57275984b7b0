import { But<PERSON> } from "@/components/ui/button";
import { Rocket, Download } from "lucide-react";

export default function Hero() {
  const techStack = [
    { icon: "🔷", name: "Azure Ecosystem" },
    { icon: "🐍", name: "Python/FastAPI" },
    { icon: "🧠", name: "AI/ML Models" },
    { icon: "📊", name: "Data Analytics" },
  ];

  const handleExploreAI = () => {
    console.log('Explore AI button clicked'); // Debug log
    const element = document.querySelector('#ai-playground');
    console.log('Found element:', element); // Debug log
    if (element) {
      // Add a slight delay for better UX
      setTimeout(() => {
        const offsetTop = (element as HTMLElement).offsetTop - 80; // Account for fixed navbar
        window.scrollTo({
          top: offsetTop,
          behavior: 'smooth'
        });
      }, 100);
    } else {
      console.error('AI Playground element not found');
    }
  };

  const handleDownloadResume = () => {
    console.log('Download resume button clicked'); // Debug log
    try {
      // Download CV from Google Drive
      const resumeUrl = 'https://drive.google.com/uc?export=download&id=1pJ3k8FlQRcUBCZUb0Wr6E9Oc5PDQMrbO';
      window.open(resumeUrl, '_blank');
    } catch (error) {
      console.error('Error opening resume:', error);
    }
  };

  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center relative overflow-hidden pt-16"
    >
      {/* Background Animation */}
      <div className="absolute inset-0 opacity-10 -z-10">
        {Array.from({ length: 5 }).map((_, i) => (
          <div
            key={i}
            className="neural-dot"
            style={{
              top: `${20 + i * 10}%`,
              left: `${10 + i * 15}%`,
              animationDelay: `${i * 0.5}s`,
            }}
          />
        ))}
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          <h1 className="font-display text-4xl sm:text-5xl lg:text-7xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-800 via-tech-cyan to-tech-purple bg-clip-text text-transparent animate-gradient bg-400%">
              Khiw (Ikkyu) Nitithadachot
            </span>
          </h1>

          <div className="text-xl sm:text-2xl lg:text-3xl font-medium mb-8 h-16 flex items-center justify-center">
            <span className="typing-animation font-mono text-tech-cyan">
              Data Engineer & AI/ML Specialist
            </span>
          </div>

          <p className="text-lg sm:text-xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
            Transforming complex data into intelligent solutions through advanced analytics,
            machine learning, and scalable engineering. Specialized in Azure ecosystems,
            LLM applications, and enterprise-grade AI implementations.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
            <Button
              size="lg"
              onClick={handleExploreAI}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg hover:scale-105 transition-transform"
              data-testid="explore-ai-playground-btn"
            >
              <Rocket className="mr-2 h-5 w-5" />
              Explore AI Playground
            </Button>
            <Button
              variant="outline"
              onClick={handleDownloadResume}
              className="border-2 border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white px-8 py-4 text-lg"
              data-testid="download-resume-btn"
            >
              <Download className="mr-2 h-5 w-5" />
              Download CV
            </Button>
          </div>

          {/* Tech Stack Preview */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-6 max-w-4xl mx-auto">

            {techStack.map((tech, index) => (
              <div
                key={tech.name}
                className="tech-card p-6 bg-card rounded-xl shadow-lg hover:shadow-xl transition-all"
                data-testid={`tech-stack-${tech.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}`}
              >
                <div className="text-3xl mb-2">{tech.icon}</div>
                <h3 className="font-semibold text-sm">{tech.name}</h3>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
