import { useState } from "react";
import { motion } from "framer-motion";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation } from "@tanstack/react-query";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  Upload, 
  X, 
  Send, 
  Phone, 
  Mail, 
  MapPin,
  Calendar,
  Clock,
  FileText,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { SectionTransition } from "@/components/PageTransition";

const contactSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email"),
  company: z.string().optional(),
  phone: z.string().optional(),
  projectType: z.string().min(1, "Please select a project type"),
  budget: z.string().optional(),
  timeline: z.string().optional(),
  description: z.string().min(10, "Please provide more details about your project"),
  preferredContact: z.string().optional(),
  newsletter: z.boolean().default(false),
});

type ContactFormData = z.infer<typeof contactSchema>;

const projectTypes = [
  "Data Engineering Pipeline",
  "Machine Learning Model",
  "AI Application Development", 
  "Data Analytics Dashboard",
  "CFD/FEA Analysis",
  "Cloud Infrastructure",
  "Full-Stack Web Application",
  "Consulting & Strategy",
  "Other"
];

const budgetRanges = [
  "Under $5,000",
  "$5,000 - $15,000", 
  "$15,000 - $50,000",
  "$50,000 - $100,000",
  "$100,000+",
  "Let's discuss"
];

const timelines = [
  "ASAP (Rush job)",
  "1-2 weeks",
  "1 month",
  "2-3 months", 
  "3-6 months",
  "6+ months",
  "Flexible"
];

export default function EnhancedContact() {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      newsletter: false,
    }
  });

  const submitContact = useMutation({
    mutationFn: async (data: ContactFormData) => {
      const formData = new FormData();
      Object.entries(data).forEach(([key, value]) => {
        formData.append(key, value?.toString() || '');
      });
      
      uploadedFiles.forEach((file) => {
        formData.append('files', file);
      });

      const response = await fetch('/api/contact', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to submit form');
      }
      
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Message sent successfully!",
        description: "I'll get back to you within 24 hours.",
        duration: 5000,
      });
      form.reset();
      setUploadedFiles([]);
    },
    onError: () => {
      toast({
        title: "Failed to send message",
        description: "Please try again or contact me directly.",
        variant: "destructive",
      });
    }
  });

  const onSubmit = (data: ContactFormData) => {
    setIsSubmitting(true);
    submitContact.mutate(data);
    setTimeout(() => setIsSubmitting(false), 2000);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'image/jpeg', 'image/png'];

    const validFiles = files.filter(file => {
      if (file.size > maxSize) {
        toast({
          title: "File too large",
          description: `${file.name} is larger than 10MB`,
          variant: "destructive",
        });
        return false;
      }
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Invalid file type",
          description: `${file.name} is not a supported file type`,
          variant: "destructive",
        });
        return false;
      }
      return true;
    });

    setUploadedFiles(prev => [...prev, ...validFiles].slice(0, 5)); // Max 5 files
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const contactInfo = [
    {
      icon: <Mail className="w-5 h-5" />,
      label: "Email",
      value: "<EMAIL>",
      action: "mailto:<EMAIL>"
    },
    {
      icon: <Phone className="w-5 h-5" />,
      label: "Phone",
      value: "+66 123 456 789",
      action: "tel:+66123456789"
    },
    {
      icon: <MapPin className="w-5 h-5" />,
      label: "Location",
      value: "Bangkok, Thailand",
      action: null
    },
    {
      icon: <Clock className="w-5 h-5" />,
      label: "Response Time",
      value: "Within 24 hours",
      action: null
    }
  ];

  return (
    <section id="contact" className="py-24 bg-gradient-to-br from-background to-muted/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <SectionTransition>
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="font-display text-3xl md:text-5xl font-bold mb-6">
                <span className="bg-gradient-to-r from-primary-800 via-tech-cyan to-tech-purple bg-clip-text text-transparent">
                  Let's Work Together
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Ready to transform your data into intelligent solutions? Let's discuss your project.
              </p>
            </motion.div>
          </div>
        </SectionTransition>

        <div className="grid lg:grid-cols-3 gap-12">
          
          {/* Contact Information */}
          <SectionTransition delay={0.2}>
            <div className="space-y-8">
              <Card>
                <CardContent className="p-8">
                  <h3 className="text-xl font-semibold mb-6">Get In Touch</h3>
                  <div className="space-y-6">
                    {contactInfo.map((info, index) => (
                      <motion.div
                        key={info.label}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        viewport={{ once: true }}
                        className={`flex items-center gap-4 p-3 rounded-lg transition-colors ${
                          info.action ? 'hover:bg-muted cursor-pointer' : ''
                        }`}
                        onClick={() => info.action && window.open(info.action)}
                      >
                        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                          {info.icon}
                        </div>
                        <div>
                          <div className="font-medium">{info.label}</div>
                          <div className="text-sm text-muted-foreground">{info.value}</div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card>
                <CardContent className="p-8">
                  <h3 className="text-xl font-semibold mb-6">Why Work With Me?</h3>
                  <div className="space-y-4">
                    {[
                      { icon: <CheckCircle className="w-5 h-5 text-green-500" />, text: "6+ years of experience" },
                      { icon: <CheckCircle className="w-5 h-5 text-green-500" />, text: "50+ successful projects" },
                      { icon: <CheckCircle className="w-5 h-5 text-green-500" />, text: "99% client satisfaction" },
                      { icon: <CheckCircle className="w-5 h-5 text-green-500" />, text: "24/7 project support" }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center gap-3">
                        {item.icon}
                        <span className="text-sm">{item.text}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </SectionTransition>

          {/* Enhanced Contact Form */}
          <SectionTransition delay={0.4}>
            <div className="lg:col-span-2">
              <Card>
                <CardContent className="p-8">
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    
                    {/* Basic Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="name">Full Name *</Label>
                        <Input
                          {...form.register("name")}
                          placeholder="Your full name"
                          className="mt-2"
                        />
                        {form.formState.errors.name && (
                          <p className="text-sm text-destructive mt-1">
                            {form.formState.errors.name.message}
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <Label htmlFor="email">Email Address *</Label>
                        <Input
                          {...form.register("email")}
                          type="email"
                          placeholder="<EMAIL>"
                          className="mt-2"
                        />
                        {form.formState.errors.email && (
                          <p className="text-sm text-destructive mt-1">
                            {form.formState.errors.email.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="company">Company/Organization</Label>
                        <Input
                          {...form.register("company")}
                          placeholder="Your company name"
                          className="mt-2"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="phone">Phone Number</Label>
                        <Input
                          {...form.register("phone")}
                          placeholder="+****************"
                          className="mt-2"
                        />
                      </div>
                    </div>

                    {/* Project Details */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <Label htmlFor="projectType">Project Type *</Label>
                        <Select onValueChange={(value) => form.setValue("projectType", value)}>
                          <SelectTrigger className="mt-2">
                            <SelectValue placeholder="Select project type" />
                          </SelectTrigger>
                          <SelectContent>
                            {projectTypes.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {form.formState.errors.projectType && (
                          <p className="text-sm text-destructive mt-1">
                            {form.formState.errors.projectType.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="budget">Budget Range</Label>
                        <Select onValueChange={(value) => form.setValue("budget", value)}>
                          <SelectTrigger className="mt-2">
                            <SelectValue placeholder="Select budget" />
                          </SelectTrigger>
                          <SelectContent>
                            {budgetRanges.map((range) => (
                              <SelectItem key={range} value={range}>
                                {range}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="timeline">Timeline</Label>
                        <Select onValueChange={(value) => form.setValue("timeline", value)}>
                          <SelectTrigger className="mt-2">
                            <SelectValue placeholder="Select timeline" />
                          </SelectTrigger>
                          <SelectContent>
                            {timelines.map((timeline) => (
                              <SelectItem key={timeline} value={timeline}>
                                {timeline}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Project Description */}
                    <div>
                      <Label htmlFor="description">Project Description *</Label>
                      <Textarea
                        {...form.register("description")}
                        placeholder="Please describe your project requirements, goals, and any specific challenges you're facing..."
                        className="mt-2 min-h-[120px]"
                      />
                      {form.formState.errors.description && (
                        <p className="text-sm text-destructive mt-1">
                          {form.formState.errors.description.message}
                        </p>
                      )}
                    </div>

                    {/* File Upload */}
                    <div>
                      <Label>Attachments (Optional)</Label>
                      <div className="mt-2">
                        <Label htmlFor="file-upload" className="flex items-center justify-center w-full h-32 border-2 border-dashed border-muted-foreground/25 rounded-lg cursor-pointer hover:border-primary/50 transition-colors">
                          <div className="text-center">
                            <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                            <p className="text-sm text-muted-foreground">
                              Click to upload files or drag and drop
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              PDF, DOC, TXT, JPG, PNG (Max 10MB each, up to 5 files)
                            </p>
                          </div>
                          <Input
                            id="file-upload"
                            type="file"
                            multiple
                            onChange={handleFileUpload}
                            className="hidden"
                            accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
                          />
                        </Label>
                        
                        {uploadedFiles.length > 0 && (
                          <div className="mt-4 space-y-2">
                            {uploadedFiles.map((file, index) => (
                              <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                                <div className="flex items-center gap-2">
                                  <FileText className="w-4 h-4" />
                                  <span className="text-sm truncate">{file.name}</span>
                                  <Badge variant="secondary" className="text-xs">
                                    {(file.size / 1024 / 1024).toFixed(1)}MB
                                  </Badge>
                                </div>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeFile(index)}
                                >
                                  <X className="w-4 h-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Preferences */}
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="preferredContact">Preferred Contact Method</Label>
                        <Select onValueChange={(value) => form.setValue("preferredContact", value)}>
                          <SelectTrigger className="mt-2">
                            <SelectValue placeholder="How would you like me to reach out?" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="email">Email</SelectItem>
                            <SelectItem value="phone">Phone Call</SelectItem>
                            <SelectItem value="video">Video Call</SelectItem>
                            <SelectItem value="any">Any method</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="newsletter"
                          checked={form.watch("newsletter")}
                          onCheckedChange={(checked) => form.setValue("newsletter", !!checked)}
                        />
                        <Label htmlFor="newsletter" className="text-sm">
                          Subscribe to my newsletter for AI/ML insights and project updates
                        </Label>
                      </div>
                    </div>

                    {/* Submit Button */}
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        type="submit"
                        className="w-full md:w-auto gradient-tech-primary text-white px-8 py-3 text-lg"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="w-5 h-5 mr-2" />
                            Send Message
                          </>
                        )}
                      </Button>
                    </motion.div>

                    {/* Form Status */}
                    {form.formState.isSubmitSuccessful && (
                      <div className="flex items-center gap-2 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        <span className="text-green-800 dark:text-green-200">
                          Message sent successfully! I'll get back to you soon.
                        </span>
                      </div>
                    )}

                  </form>
                </CardContent>
              </Card>
            </div>
          </SectionTransition>

        </div>
      </div>
    </section>
  );
}