import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Github, Laptop, Database, Brain, Wind, BarChart, MessageSquare } from "lucide-react";


import { useLocation } from "wouter";

export default function Projects() {
  const [, setLocation] = useLocation();

  const projects = [
    {
      title: "AI-Powered Portfolio Platform",
      slug: "ai-portfolio-platform",
      description: "Next.js 14 portfolio with integrated AI playground, featuring FastAPI backend and modern architecture",
      icon: <Laptop className="w-12 h-12" />,
      gradient: "from-tech-purple to-tech-cyan",
      techs: ["Next.js", "FastAPI", "AI/ML"],
      status: "Live",
      statusColor: "tech-purple",
    },
    {
      title: "Enterprise Data Pipeline",
      slug: "enterprise-data-pipeline",
      description: "Scalable Azure Data Factory pipeline processing 1TB+ daily with real-time analytics",
      icon: <Database className="w-12 h-12" />,
      gradient: "from-tech-cyan to-primary-600",
      techs: ["Azure ADF", "Synapse", "Apache Spark"],
      status: "Production",
      statusColor: "tech-cyan",
    },
    {
      title: "Predictive Analytics Platform",
      slug: "predictive-analytics-platform",
      description: "Production ML pipeline with automated retraining and A/B testing for business intelligence",
      icon: <Brain className="w-12 h-12" />,
      gradient: "from-tech-purple to-tech-emerald",
      techs: ["TensorFlow", "MLOps", "Kubernetes"],
      status: "AI/ML",
      statusColor: "tech-purple",
    },
    {
      title: "CFD Analysis Platform",
      slug: "cfd-analysis-platform",
      description: "Web-based CFD simulation tool with ANSYS integration for nuclear reactor thermal analysis",
      icon: <Wind className="w-12 h-12" />,
      gradient: "from-tech-orange to-primary-700",
      techs: ["ANSYS", "COMSOL", "React"],
      status: "Engineering",
      statusColor: "tech-orange",
    },
    {
      title: "Real-time BI Dashboard",
      slug: "realtime-bi-dashboard",
      description: "Interactive business intelligence platform with real-time data visualization and alerting",
      icon: <BarChart className="w-12 h-12" />,
      gradient: "from-tech-emerald to-tech-cyan",
      techs: ["Power BI", "Real-time", "Analytics"],
      status: "Dashboard",
      statusColor: "tech-emerald",
    },
    {
      title: "Enterprise LLM Solution",
      slug: "enterprise-llm-solution",
      description: "Custom LLM application for document analysis and automated report generation",
      icon: <MessageSquare className="w-12 h-12" />,
      gradient: "from-tech-purple via-tech-cyan to-tech-emerald",
      techs: ["OpenAI", "LangChain", "FastAPI"],
      status: "LLM",
      statusColor: "tech-purple",
    },
  ];

  return (
    <section id="projects" className="py-24 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-display text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-800 to-tech-emerald bg-clip-text text-transparent">
              Featured Projects
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Showcase of innovative solutions spanning data engineering, AI/ML, and full-stack development
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <Card
              key={project.title}
              className="tech-card overflow-hidden group hover:shadow-2xl transition-all duration-300"
              data-testid={`project-${project.title.toLowerCase().replace(/[^a-z0-9]/g, '-')}`}
            >
              {/* Project showcase image */}
              <div className={`h-48 bg-gradient-to-br ${project.gradient} relative overflow-hidden`}>
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                  <div className="text-white">{project.icon}</div>
                </div>
                <div className="absolute top-4 right-4">
                  <Badge
                    variant="secondary"
                    className={`px-2 py-1 bg-${project.statusColor} text-white text-xs`}
                  >
                    {project.status}
                  </Badge>
                </div>
              </div>

              <CardContent className="p-6">
                <h3 className="font-display text-xl font-bold mb-2">{project.title}</h3>
                <p className="text-muted-foreground text-sm mb-4">
                  {project.description}
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.techs.map((tech, techIndex) => (
                    <Badge
                      key={techIndex}
                      variant="outline"
                      className="text-xs"
                    >
                      {tech}
                    </Badge>
                  ))}
                </div>
                <div className="flex items-center justify-between">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setLocation(`/projects/${project.slug}`)}
                    className="text-tech-cyan hover:text-tech-purple transition-colors p-0"
                    data-testid={`project-demo-${index}`}
                  >
                    <ExternalLink className="w-4 h-4 mr-1" />
                    View Demo
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-muted-foreground hover:text-foreground transition-colors p-0"
                    data-testid={`project-github-${index}`}
                  >
                    <Github className="w-4 h-4" />

                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button
            size="lg"
            className="gradient-tech-primary text-white px-8 py-3 hover:scale-105 transition-transform"
            data-testid="view-all-projects-btn"
          >


            <Github className="w-5 h-5 mr-2" />

            View All Projects on GitHub
          </Button>
        </div>
      </div>
    </section>
  );
}
