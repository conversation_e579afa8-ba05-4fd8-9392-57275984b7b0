import { useState, useEffect } from "react";
import { useTheme } from "@/hooks/use-theme";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import { Moon, Sun, Menu, X } from "lucide-react";

export default function Navigation() {
  const { theme, setTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState("home");
  const [, setLocation] = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);

      // Determine which section is currently in view
      const sections = ["home", "projects", "ai-playground", "consulting-services", "blog"];
      const scrollPosition = window.scrollY + 100; // Offset for navbar

      for (let i = sections.length - 1; i >= 0; i--) {
        const element = document.getElementById(sections[i]);
        if (element && element.offsetTop <= scrollPosition) {
          setActiveSection(sections[i]);
          break;
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navItems = [
    { href: "#home", label: "Home", isRoute: false },
    { href: "#projects", label: "Projects", isRoute: false },
    { href: "/ai-playground", label: "AI Playground", isRoute: true },
    { href: "#consulting-services", label: "Services", isRoute: false },
    { href: "#blog", label: "Blog", isRoute: false },
  ];

  const handleNavClick = (item: { href: string; label: string; isRoute: boolean }) => {
    if (item.isRoute) {
      setLocation(item.href);
    } else {
      // Enhanced smooth scrolling with offset for fixed navbar
      const element = document.querySelector(item.href);
      if (element) {
        const offsetTop = element.offsetTop - 80; // Account for fixed navbar height
        window.scrollTo({
          top: offsetTop,
          behavior: 'smooth'
        });
      }
    }
    setIsOpen(false);
  };

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled
          ? "glass-morphism backdrop-blur-md"
          : "bg-transparent"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <div className="font-display font-bold text-xl">
              <span className="bg-gradient-to-r from-primary-800 to-tech-cyan bg-clip-text text-transparent">
                Khiw.dev
              </span>
            </div>
          </div>

          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-2">
              {navItems.map((item) => {
                const isActive = activeSection === item.href.replace('#', '');
                return (
                  <button
                    key={item.href}
                    onClick={() => handleNavClick(item)}
                    className={`relative px-4 py-2 text-sm font-medium rounded-full
                               transform hover:scale-105 hover:-translate-y-0.5
                               transition-all duration-300 ease-out
                               border backdrop-blur-sm
                               ${isActive
                                 ? 'bg-gradient-to-r from-tech-cyan/30 to-tech-purple/30 text-white border-tech-cyan/50 shadow-lg shadow-tech-cyan/25'
                                 : 'text-foreground bg-gradient-to-r from-transparent to-transparent hover:from-tech-cyan/20 hover:to-tech-purple/20 hover:text-white hover:shadow-lg hover:shadow-tech-cyan/25 border-transparent hover:border-tech-cyan/30'
                               }`}
                    data-testid={`nav-link-${item.label.toLowerCase()}`}
                  >
                    <span className="relative z-10">{item.label}</span>
                    <div className={`absolute inset-0 rounded-full bg-gradient-to-r from-tech-cyan to-tech-purple
                                  transition-opacity duration-300 ${isActive ? 'opacity-20' : 'opacity-0 hover:opacity-20'}`}></div>
                  </button>
                );
              })}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              data-testid="theme-toggle"
            >
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setIsOpen(!isOpen)}
              data-testid="mobile-menu-toggle"
            >
              {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden bg-background/95 backdrop-blur-sm border-t border-border">
          <div className="px-2 pt-2 pb-3 space-y-2">
            {navItems.map((item) => {
              const isActive = activeSection === item.href.replace('#', '');
              return (
                <button
                  key={item.href}
                  onClick={() => handleNavClick(item)}
                  className={`relative w-full text-left px-4 py-3 rounded-xl text-base font-medium
                           transform hover:scale-[1.02] hover:translate-x-1
                           transition-all duration-300 ease-out
                           border backdrop-blur-sm
                           ${isActive
                             ? 'bg-gradient-to-r from-tech-cyan/30 to-tech-purple/30 text-white border-tech-cyan/50 shadow-md shadow-tech-cyan/20'
                             : 'text-foreground bg-gradient-to-r from-transparent to-transparent hover:from-tech-cyan/20 hover:to-tech-purple/20 hover:text-white hover:shadow-md hover:shadow-tech-cyan/20 border-transparent hover:border-tech-cyan/30'
                           }`}
                  data-testid={`mobile-nav-link-${item.label.toLowerCase()}`}
                >
                  <span className="relative z-10">{item.label}</span>
                  <div className={`absolute inset-0 rounded-xl bg-gradient-to-r from-tech-cyan to-tech-purple
                                transition-opacity duration-300 ${isActive ? 'opacity-15' : 'opacity-0 hover:opacity-15'}`}></div>
                </button>
              );
            })}
          </div>
        </div>
      )}
    </nav>
  );
}
