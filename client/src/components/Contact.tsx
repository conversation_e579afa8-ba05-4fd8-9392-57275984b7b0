import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Mail, Globe, MapPin, Clock, Database, Brain, Code, Wind, Send, Shield } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";

export default function Contact() {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    projectType: "",
    budget: "",
    description: "",
    timeline: "",
  });

  const contactInfo = [
    {
      icon: <Mail className="w-5 h-5" />,
      title: "Email",
      value: "<EMAIL>",
      color: "tech-cyan",
    },
    {
      icon: <Globe className="w-5 h-5" />,
      title: "Website",
      value: "getintheq.space",
      color: "tech-purple",
    },
    {
      icon: <MapPin className="w-5 h-5" />,
      title: "Location",
      value: "Available for remote & on-site projects",
      color: "tech-emerald",
    },
    {
      icon: <Clock className="w-5 h-5" />,
      title: "Response Time",
      value: "Within 24 hours for project inquiries",
      color: "tech-orange",
    },
  ];

  const expertiseAreas = [
    { icon: <Database className="w-4 h-4" />, name: "Data Engineering", color: "tech-cyan" },
    { icon: <Brain className="w-4 h-4" />, name: "AI/ML Solutions", color: "tech-purple" },
    { icon: <Code className="w-4 h-4" />, name: "Full-Stack Dev", color: "tech-emerald" },
    { icon: <Wind className="w-4 h-4" />, name: "CFD/FEA Analysis", color: "tech-orange" },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.email || !formData.projectType || !formData.description) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      await apiRequest("POST", "/api/contact", formData);

      toast({
        title: "Message Sent!",
        description: "Thank you for your inquiry. I'll get back to you within 24 hours.",
      });

      // Reset form
      setFormData({
        name: "",
        email: "",
        company: "",
        projectType: "",
        budget: "",
        description: "",
        timeline: "",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message. Please try again or email directly.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <section id="contact" className="py-24 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-display text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-800 to-tech-cyan bg-clip-text text-transparent">
              Let's Build Something Amazing
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Ready to transform your data challenges into intelligent solutions? Let's discuss your next project.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 max-w-6xl mx-auto">
          {/* Contact Information */}
          <div>
            <h3 className="font-display text-2xl font-bold mb-8">Get In Touch</h3>
            <div className="space-y-6">
              {contactInfo.map((info, index) => (
                <div key={index} className="flex items-start gap-4" data-testid={`contact-info-${index}`}>
                  <div className={`flex-shrink-0 w-12 h-12 bg-${info.color}/10 rounded-xl flex items-center justify-center`}>
                    <div className={`text-${info.color}`}>
                      {info.icon}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">{info.title}</h4>
                    <p className="text-muted-foreground">{info.value}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-12">
              <h4 className="font-semibold mb-6">Areas of Expertise</h4>
              <div className="grid grid-cols-2 gap-4">
                {expertiseAreas.map((area, index) => (
                  <Card key={index} className="p-4" data-testid={`expertise-${index}`}>
                    <CardContent className="p-0 flex items-center gap-3">
                      <div className={`text-${area.color}`}>
                        {area.icon}
                      </div>
                      <div className="font-semibold text-sm">{area.name}</div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <Card className="shadow-xl">
            <CardContent className="p-8">
              <h3 className="font-display text-2xl font-bold mb-6">Start Your Project</h3>

              <form onSubmit={handleSubmit} className="space-y-6" data-testid="contact-form">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Name *</Label>
                    <Input
                      id="name"
                      type="text"
                      required
                      value={formData.name}
                      onChange={(e) => handleInputChange("name", e.target.value)}
                      placeholder="Your full name"
                      data-testid="input-name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      required
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      placeholder="<EMAIL>"
                      data-testid="input-email"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="company">Company</Label>
                  <Input
                    id="company"
                    type="text"
                    value={formData.company}
                    onChange={(e) => handleInputChange("company", e.target.value)}
                    placeholder="Your company name"
                    data-testid="input-company"
                  />
                </div>

                <div>
                  <Label htmlFor="projectType">Project Type *</Label>
                  <Select value={formData.projectType} onValueChange={(value) => handleInputChange("projectType", value)}>
                    <SelectTrigger data-testid="select-project-type">
                      <SelectValue placeholder="Select project type..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="data-engineering">Data Engineering & ETL</SelectItem>
                      <SelectItem value="ai-ml">AI/ML Development</SelectItem>
                      <SelectItem value="full-stack">Full-Stack Application</SelectItem>
                      <SelectItem value="cfd-fea">CFD/FEA Analysis</SelectItem>
                      <SelectItem value="consulting">Technical Consulting</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="budget">Project Budget</Label>
                  <Select value={formData.budget} onValueChange={(value) => handleInputChange("budget", value)}>
                    <SelectTrigger data-testid="select-budget">
                      <SelectValue placeholder="Select budget range..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5k-10k">$5,000 - $10,000</SelectItem>
                      <SelectItem value="10k-25k">$10,000 - $25,000</SelectItem>
                      <SelectItem value="25k-50k">$25,000 - $50,000</SelectItem>
                      <SelectItem value="50k+">$50,000+</SelectItem>
                      <SelectItem value="consulting">Hourly Consulting</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="description">Project Description *</Label>
                  <Textarea
                    id="description"
                    required
                    rows={4}
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder="Describe your project requirements, goals, and any technical specifications..."
                    data-testid="textarea-description"
                  />
                </div>

                <div>
                  <Label htmlFor="timeline">Timeline</Label>
                  <Select value={formData.timeline} onValueChange={(value) => handleInputChange("timeline", value)}>
                    <SelectTrigger data-testid="select-timeline">
                      <SelectValue placeholder="Select timeline..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="asap">As soon as possible</SelectItem>
                      <SelectItem value="1-month">Within 1 month</SelectItem>
                      <SelectItem value="3-months">Within 3 months</SelectItem>
                      <SelectItem value="6-months">Within 6 months</SelectItem>
                      <SelectItem value="flexible">Flexible timeline</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  type="submit"
                  size="lg"
                  className="w-full gradient-tech-primary text-white py-4 text-lg hover:scale-105 transition-transform"
                  disabled={isSubmitting}
                  data-testid="submit-contact-form"
                >
                  <Send className="w-5 h-5 mr-2" />
                  {isSubmitting ? "Sending..." : "Send Project Inquiry"}
                </Button>

                <div className="text-center text-sm text-muted-foreground">
                  <Shield className="w-4 h-4 inline mr-1 text-tech-emerald" />
                  Your information is secure and will only be used to respond to your inquiry.
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
