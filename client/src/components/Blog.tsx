import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, ArrowRight, BarChart, Database, Code, Wind, Rocket, Brain } from "lucide-react";

export default function Blog() {
  const featuredArticle = {
    title: "Building Production-Ready ML Pipelines with Azure and Python",
    description: "A comprehensive guide to designing, implementing, and deploying scalable machine learning pipelines using Azure Data Factory, MLflow, and modern DevOps practices. Learn how to automate model training, validation, and deployment while ensuring data quality and model performance monitoring.",
    readTime: "5 min read",
    date: "December 15, 2024",
    tags: ["AI/ML", "Data Engineering"],
    icon: <BarChart className="w-12 h-12" />,
  };

  const recentArticles = [
    {
      title: "Next.js 14 App Router: Advanced Patterns",
      description: "Exploring advanced routing patterns, server components, and performance optimizations.",
      readTime: "3 min read",
      date: "Dec 10, 2024",
      tag: "Frontend",
      icon: <Code className="w-6 h-6" />,
      gradient: "from-tech-emerald to-tech-cyan",
    },
    {
      title: "CFD Analysis for Nuclear Reactor Design",
      description: "Using ANSYS Fluent for thermal-hydraulic analysis in nuclear reactor components.",
      readTime: "7 min read",
      date: "Dec 5, 2024",
      tag: "Engineering",
      icon: <Wind className="w-6 h-6" />,
      gradient: "from-tech-orange to-primary-700",
    },
    {
      title: "Azure Synapse Analytics Best Practices",
      description: "Optimizing data warehouse performance and cost management strategies.",
      readTime: "6 min read",
      date: "Nov 28, 2024",
      tag: "Data Engineering",
      icon: <Database className="w-6 h-6" />,
      gradient: "from-tech-cyan to-primary-600",
    },
  ];

  return (
    <section id="blog" className="py-24 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-display text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-800 to-tech-emerald bg-clip-text text-transparent">
              Technical Blog
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Insights, tutorials, and deep dives into AI/ML, data engineering, and modern web development
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Featured Article */}
          <div className="lg:col-span-2">
            <Card className="tech-card overflow-hidden hover:shadow-2xl transition-all" data-testid="featured-article">
              {/* Featured blog post image */}
              <div className="h-64 bg-gradient-to-br from-tech-purple to-tech-cyan relative">
                <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                  <div className="text-center text-white">
                    {featuredArticle.icon}
                    <h3 className="text-xl font-bold mt-4">Featured Article</h3>
                  </div>
                </div>
              </div>
              <CardContent className="p-8">
                <div className="flex items-center gap-2 mb-4">
                  {featuredArticle.tags.map((tag, index) => (
                    <Badge 
                      key={index}
                      variant="secondary"
                      className={`px-3 py-1 ${
                        tag === "AI/ML" 
                          ? "bg-tech-purple/10 text-tech-purple" 
                          : "bg-tech-cyan/10 text-tech-cyan"
                      }`}
                    >
                      {tag}
                    </Badge>
                  ))}
                  <span className="text-muted-foreground text-sm flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {featuredArticle.readTime}
                  </span>
                </div>
                <h3 className="font-display text-2xl font-bold mb-4">{featuredArticle.title}</h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  {featuredArticle.description}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-tech-cyan to-tech-purple rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">K</span>
                    </div>
                    <div>
                      <div className="font-semibold text-sm">Khiw Nitithadachot</div>
                      <div className="text-muted-foreground text-xs">{featuredArticle.date}</div>
                    </div>
                  </div>
                  <Button variant="ghost" className="text-tech-cyan hover:text-tech-purple p-0" data-testid="read-featured-article">
                    Read More <ArrowRight className="w-4 h-4 ml-1" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Articles */}
          <div className="space-y-8">
            {recentArticles.map((article, index) => (
              <Card 
                key={article.title}
                className="tech-card overflow-hidden hover:shadow-xl transition-all"
                data-testid={`article-${index}`}
              >
                <div className={`h-32 bg-gradient-to-br ${article.gradient} relative`}>
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                    <div className="text-white">{article.icon}</div>
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge 
                      variant="secondary"
                      className="px-2 py-1 text-xs"
                    >
                      {article.tag}
                    </Badge>
                    <span className="text-muted-foreground text-xs flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {article.readTime}
                    </span>
                  </div>
                  <h4 className="font-display text-lg font-bold mb-2">{article.title}</h4>
                  <p className="text-muted-foreground text-sm mb-4">
                    {article.description}
                  </p>
                  <div className="text-xs text-muted-foreground">{article.date}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="text-center mt-12">
          <Button 
            size="lg"
            className="gradient-tech-primary text-white px-8 py-3 hover:scale-105 transition-transform"
            data-testid="view-all-articles-btn"
          >
            <Brain className="w-5 h-5 mr-2" />
            View All Articles
          </Button>
        </div>
      </div>
    </section>
  );
}
