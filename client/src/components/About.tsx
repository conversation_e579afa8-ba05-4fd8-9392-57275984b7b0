import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Mail, Linkedin, Github } from "lucide-react";

export default function About() {
  const expertise = [
    { title: "Data Engineering", color: "text-tech-cyan", description: "Azure Data Factory, Synapse Analytics, ETL pipelines, and real-time data processing" },
    { title: "AI/ML Development", color: "text-tech-purple", description: "LLMs, NLP, Computer Vision, and predictive analytics with production deployment" },
    { title: "Full-Stack Development", color: "text-tech-emerald", description: "Next.js, FastAPI, TypeScript, and modern web application architectures" },
    { title: "Engineering Analysis", color: "text-tech-orange", description: "CFD/FEA simulation, ANSYS, COMSOL, and mechanical engineering solutions" },
  ];

  const industries = ["Nuclear Technology", "Food Packaging", "Oil & Gas", "Business Intelligence"];

  return (
    <section id="about" className="py-24 bg-card">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-display text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-800 to-tech-cyan bg-clip-text text-transparent">
              About Me
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Passionate about leveraging cutting-edge technology to solve complex business challenges
            through data-driven insights and intelligent automation.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-6">
            {/* Professional image placeholder */}
            <div className="w-full h-96 bg-gradient-to-br from-tech-cyan to-tech-purple rounded-2xl animate-float shadow-2xl relative overflow-hidden">
              <div className="absolute inset-4 bg-background rounded-xl flex items-center justify-center">
                <div className="text-6xl text-muted-foreground">👨‍💻</div>
              </div>
            </div>

            <div className="flex justify-center space-x-6">
              <a
                href="mailto:<EMAIL>"
                className="text-tech-cyan hover:text-tech-purple transition-colors"
                data-testid="contact-email"
              >
                <Mail className="h-8 w-8" />
              </a>
              <a
                href="https://www.linkedin.com/in/getintheq"
                className="text-tech-cyan hover:text-tech-purple transition-colors"
                data-testid="contact-linkedin"
              >
                <Linkedin className="h-8 w-8" />
              </a>
              <a
                href="https://github.com/khiwniti"
                className="text-tech-cyan hover:text-tech-purple transition-colors"
                data-testid="contact-github"
              >
                <Github className="h-8 w-8" />
              </a>
            </div>
          </div>

          <div className="space-y-8">
            <div>
              <h3 className="font-display text-2xl font-bold mb-4">Professional Journey</h3>
              <p className="text-muted-foreground leading-relaxed mb-6">
                With extensive experience in data engineering and AI/ML development, I specialize in creating
                scalable solutions that bridge the gap between complex data challenges and business objectives.
                My expertise spans from traditional CFD/FEA analysis to cutting-edge LLM applications.
              </p>
            </div>

            <div className="grid sm:grid-cols-2 gap-6">
              {expertise.map((item, index) => (
                <Card key={index} className="p-6">
                  <CardContent className="p-0">
                    <h4 className={`font-semibold text-lg mb-2 ${item.color}`}>
                      {item.title}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {item.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div>
              <h4 className="font-semibold text-lg mb-4">Industry Experience</h4>
              <div className="flex flex-wrap gap-2">
                {industries.map((industry, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="px-3 py-1 bg-tech-cyan/10 text-tech-cyan border border-tech-cyan/20"
                  >
                    {industry}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
