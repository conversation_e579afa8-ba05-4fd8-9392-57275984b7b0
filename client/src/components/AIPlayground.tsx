import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Languages,
  Image,
  TrendingUp,
  Wind,
  Code,
  Gauge,
  Play,
  Upload,
  Eye,
  Calculator,
  BarChart,
  Wand2
} from "lucide-react";

interface AnalysisResult {
  sentiment: string;
  entities: string;
  category: string;
}

export default function AIPlayground() {
  const [textAnalysisResult, setTextAnalysisResult] = useState<AnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isClassifyingImage, setIsClassifyingImage] = useState(false);
  const [isAnalyzingData, setIsAnalyzingData] = useState(false);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [showImageResults, setShowImageResults] = useState(false);
  const [showDataResults, setShowDataResults] = useState(false);
  const [showCodeResults, setShowCodeResults] = useState(false);

  const handleTextAnalysis = async () => {
    setIsAnalyzing(true);
    // Simulate API call
    setTimeout(() => {
      setTextAnalysisResult({
        sentiment: "Positive (0.85)",
        entities: "AI, data analysis",
        category: "Technology"
      });
      setIsAnalyzing(false);
    }, 2000);
  };

  const handleImageClassification = async () => {
    setIsClassifyingImage(true);
    setTimeout(() => {
      setShowImageResults(true);
      setIsClassifyingImage(false);
    }, 1500);
  };

  const handleDataAnalysis = async () => {
    setIsAnalyzingData(true);
    setTimeout(() => {
      setShowDataResults(true);
      setIsAnalyzingData(false);
    }, 2500);
  };

  const handleCodeGeneration = async () => {
    setIsGeneratingCode(true);
    setTimeout(() => {
      setShowCodeResults(true);
      setIsGeneratingCode(false);
    }, 3000);
  };

  const playgroundItems = [
    {
      title: "NLP Text Analysis",
      description: "Sentiment analysis, entity extraction, and text classification",
      icon: <Languages className="w-6 h-6" />,
      color: "tech-purple",
      component: (
        <div className="space-y-4">
          <Textarea
            placeholder="Enter text to analyze..."
            defaultValue="The AI revolution is transforming how we approach data analysis and business intelligence."
            rows={3}
            data-testid="text-analysis-input"
          />
          <Button
            className="w-full gradient-tech-primary text-white hover:scale-105 transition-transform"
            onClick={handleTextAnalysis}
            disabled={isAnalyzing}
            data-testid="analyze-text-btn"
          >
            <Play className="w-4 h-4 mr-2" />
            {isAnalyzing ? "Analyzing..." : "Analyze Text"}
          </Button>
          {textAnalysisResult && (
            <div className="space-y-2" data-testid="text-analysis-results">
              <div className="flex justify-between text-sm">
                <span>Sentiment</span>
                <span className="text-tech-emerald font-semibold">{textAnalysisResult.sentiment}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Entities</span>
                <span className="text-tech-cyan">{textAnalysisResult.entities}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Category</span>
                <span className="text-tech-purple">{textAnalysisResult.category}</span>
              </div>
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Image Classification",
      description: "Computer vision for object detection and image analysis",
      icon: <Image className="w-6 h-6" />,
      color: "tech-cyan",
      component: (
        <div className="space-y-4">
          <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
            <Upload className="w-8 h-8 mx-auto text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground">Upload or drag an image</p>
            <input type="file" className="hidden" accept="image/*" data-testid="image-upload" />
          </div>
          <Button
            className="w-full gradient-tech-primary text-white hover:scale-105 transition-transform"
            onClick={handleImageClassification}
            disabled={isClassifyingImage}
            data-testid="classify-image-btn"
          >
            <Eye className="w-4 h-4 mr-2" />
            {isClassifyingImage ? "Classifying..." : "Classify Image"}
          </Button>
          {showImageResults && (
            <div className="space-y-2" data-testid="image-analysis-results">
              <div className="flex justify-between text-sm">
                <span>Object</span>
                <span className="text-tech-emerald font-semibold">Technology (92%)</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Scene</span>
                <span className="text-tech-cyan">Office Environment</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Color Palette</span>
                <span className="text-tech-purple">Blue, Gray, White</span>
              </div>
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Data Analysis",
      description: "Statistical analysis and predictive modeling",
      icon: <TrendingUp className="w-6 h-6" />,
      color: "tech-emerald",
      component: (
        <div className="space-y-4">
          <Select>
            <SelectTrigger data-testid="dataset-selector">
              <SelectValue placeholder="Select dataset..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sales">Sales Performance Dataset</SelectItem>
              <SelectItem value="customer">Customer Behavior Analytics</SelectItem>
              <SelectItem value="market">Market Trends Analysis</SelectItem>
            </SelectContent>
          </Select>
          <Button
            className="w-full gradient-tech-primary text-white hover:scale-105 transition-transform"
            onClick={handleDataAnalysis}
            disabled={isAnalyzingData}
            data-testid="analyze-data-btn"
          >
            <Calculator className="w-4 h-4 mr-2" />
            {isAnalyzingData ? "Analyzing..." : "Analyze Dataset"}
          </Button>
          {showDataResults && (
            <Card className="bg-muted/50">
              <CardContent className="p-4" data-testid="data-analysis-results">
                <div className="text-center mb-3">
                  <Badge className="bg-tech-emerald text-white">Analysis Complete</Badge>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Revenue Growth</span>
                    <span className="text-tech-emerald font-semibold">+24.7%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Customer Retention</span>
                    <span className="text-tech-cyan">87.3%</span>
                  </div>
                  <Progress value={87} className="h-2" />
                  <div className="text-xs text-muted-foreground text-center">
                    Confidence Score: 94.2%
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      ),
    },
    {
      title: "CFD Simulation",
      description: "Fluid dynamics and heat transfer analysis",
      icon: <Wind className="w-6 h-6" />,
      color: "tech-orange",
      component: (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-3">
            <input
              type="number"
              placeholder="Velocity"
              className="p-2 border border-border rounded bg-background text-sm"
              data-testid="velocity-input"
            />
            <input
              type="number"
              placeholder="Pressure"
              className="p-2 border border-border rounded bg-background text-sm"
              data-testid="pressure-input"
            />
          </div>
          <Button
            className="w-full gradient-tech-primary text-white hover:scale-105 transition-transform"
            data-testid="run-cfd-btn"
          >
            <Play className="w-4 h-4 mr-2" />
            Run Simulation
          </Button>
          <Card className="bg-muted/50">
            <CardContent className="p-4" data-testid="cfd-results">
              <div className="h-32 bg-gradient-to-r from-blue-400 via-green-500 to-red-500 rounded opacity-75 flex items-center justify-center mb-3">
                <span className="text-white font-semibold">Flow Visualization</span>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Max Velocity:</span>
                  <span className="font-semibold ml-1">45.2 m/s</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Pressure Drop:</span>
                  <span className="font-semibold ml-1">1250 Pa</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ),
    },
    {
      title: "Code Generator",
      description: "AI-powered code generation and optimization",
      icon: <Code className="w-6 h-6" />,
      color: "primary-600",
      component: (
        <div className="space-y-4">
          <Select>
            <SelectTrigger data-testid="code-type-selector">
              <SelectValue placeholder="Select code type..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="python">Python Data Analysis</SelectItem>
              <SelectItem value="sql">SQL Query Generator</SelectItem>
              <SelectItem value="api">API Endpoint Creator</SelectItem>
            </SelectContent>
          </Select>
          <Textarea
            placeholder="Describe what you want to build..."
            defaultValue="Create a function to calculate moving average"
            rows={2}
            className="font-mono text-sm"
            data-testid="code-description-input"
          />
          <Button
            className="w-full gradient-tech-primary text-white hover:scale-105 transition-transform"
            onClick={handleCodeGeneration}
            disabled={isGeneratingCode}
            data-testid="generate-code-btn"
          >
            <Wand2 className="w-4 h-4 mr-2" />
            {isGeneratingCode ? "Generating..." : "Generate Code"}
          </Button>
          {showCodeResults && (
            <Card className="bg-neutral-900 text-green-400">
              <CardContent className="p-4 font-mono text-sm" data-testid="generated-code">
                <div className="text-tech-emerald"># Generated Python Code</div>
                <div className="text-gray-300">def moving_average(data, window):</div>
                <div className="text-gray-300 ml-4">return data.rolling(window).mean()</div>
                <div className="text-gray-300 ml-4">return data.fillna(method='ffill')</div>
                <div className="text-green-400 mt-2"># Function ready to use!</div>
              </CardContent>
            </Card>
          )}
        </div>
      ),
    },
    {
      title: "Real-time Analytics",
      description: "Live data processing and visualization",
      icon: <Gauge className="w-6 h-6" />,
      color: "pink-500",
      component: (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Card className="text-center p-3 bg-muted/50">
              <div className="text-xl font-bold text-tech-cyan">1,247</div>
              <div className="text-xs text-muted-foreground">Active Users</div>
            </Card>
            <Card className="text-center p-3 bg-muted/50">
              <div className="text-xl font-bold text-tech-emerald">98.5%</div>
              <div className="text-xs text-muted-foreground">Uptime</div>
            </Card>
          </div>
          <Button
            className="w-full gradient-tech-primary text-white hover:scale-105 transition-transform"
            data-testid="view-dashboard-btn"
          >
            <BarChart className="w-4 h-4 mr-2" />
            View Dashboard
          </Button>
          <div className="space-y-2" data-testid="real-time-metrics">
            <div className="flex items-center justify-between">
              <span className="text-sm">Data Processing</span>
              <div className="flex-1 mx-3">
                <Progress value={75} className="h-2" />
              </div>
              <span className="text-sm font-semibold">75%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Model Training</span>
              <div className="flex-1 mx-3">
                <Progress value={45} className="h-2" />
              </div>
              <span className="text-sm font-semibold">45%</span>
            </div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <section id="ai-playground" className="py-24 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-display text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-tech-purple via-tech-cyan to-tech-emerald bg-clip-text text-transparent">
              AI Playground
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Interactive demonstrations of AI/ML capabilities and data processing tools
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {playgroundItems.map((item, index) => (
            <Card
              key={item.title}
              className="tech-card p-6 hover:shadow-xl transition-all"
              data-testid={`playground-${item.title.toLowerCase().replace(/[^a-z0-9]/g, '-')}`}
            >
              <CardContent className="p-0">
                <div className="text-center mb-6">
                  <div className={`inline-flex items-center justify-center w-16 h-16 bg-${item.color}/10 rounded-2xl mb-4 animate-ai-pulse`}>
                    <div className={`text-${item.color}`}>
                      {item.icon}
                    </div>
                  </div>
                  <h3 className="font-display text-xl font-bold mb-2">{item.title}</h3>
                  <p className="text-muted-foreground text-sm">
                    {item.description}
                  </p>
                </div>

                {item.component}
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-muted-foreground mb-6">
            These are interactive demonstrations of AI/ML capabilities. Full implementations are available for enterprise clients.
          </p>
          <Button
            size="lg"
            className="gradient-tech-primary text-white px-8 py-3 hover:scale-105 transition-transform"
            data-testid="request-ai-solution-btn"
          >
            <Wand2 className="w-5 h-5 mr-2" />
            Request Custom AI Solution
          </Button>
        </div>
      </div>
    </section>
  );
}
