import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON> } from "wouter";
import {
  Brain,
  Code,
  Wind,
  Globe,
  MessageSquare,
  Rocket,
  ArrowRight,
  CheckCircle,
  Users,
  Zap
} from "lucide-react";

export default function ConsultingServices() {
  const services = [
    {
      title: "AI/LLM Development & Integration",
      slug: "ai-llm-development",
      description: "Transform your business with cutting-edge AI solutions",
      content: "Custom AI/LLM applications, chatbots, and intelligent automation systems",
      category: "ai-ml",
      icon: <Brain className="w-8 h-8" />,
      color: "tech-purple",
      gradient: "from-tech-purple to-tech-cyan",
      pricing: "Starting at $5,000",
      features: [
        "Custom ChatGPT integrations",
        "WordPress AI plugins",
        "Intelligent chatbots",
        "Document analysis systems",
        "RAG implementations",
        "LLM fine-tuning"
      ],
      technologies: ["OpenAI", "Lang<PERSON>hain", "Hugging Face", "FastAPI", "React"],
      featured: true,
    },
    {
      title: "Engineering Simulation & CFD/FEA",
      slug: "engineering-simulation",
      description: "Expert computational fluid dynamics and finite element analysis",
      content: "Advanced engineering simulation for nuclear, oil & gas, and mechanical systems",
      category: "engineering",
      icon: <Wind className="w-8 h-8" />,
      color: "tech-orange",
      gradient: "from-tech-orange to-primary-700",
      pricing: "Starting at $3,000",
      features: [
        "ANSYS Fluent modeling",
        "COMSOL simulations",
        "Thermal analysis",
        "Nuclear reactor design",
        "Heat transfer optimization",
        "Custom simulation tools"
      ],
      technologies: ["ANSYS", "COMSOL", "MATLAB", "Python", "C++"],
      featured: true,
    },
    {
      title: "Full-Stack Web Development",
      slug: "full-stack-development",
      description: "Professional websites and applications at affordable rates",
      content: "Modern, responsive web applications built with the latest technologies",
      category: "development",
      icon: <Code className="w-8 h-8" />,
      color: "tech-emerald",
      gradient: "from-tech-emerald to-tech-cyan",
      pricing: "Starting at $2,000",
      features: [
        "React/Next.js applications",
        "E-commerce platforms",
        "Content management systems",
        "Database design",
        "API development",
        "Mobile-responsive design"
      ],
      technologies: ["Next.js", "React", "TypeScript", "Node.js", "PostgreSQL"],
      featured: false,
    },
    {
      title: "Data Engineering & Analytics",
      slug: "data-engineering",
      description: "Scalable data pipelines and business intelligence solutions",
      content: "Enterprise-grade data processing and analytics platforms",
      category: "data",
      icon: <Globe className="w-8 h-8" />,
      color: "tech-cyan",
      gradient: "from-tech-cyan to-primary-600",
      pricing: "Starting at $4,000",
      features: [
        "Azure Data Factory pipelines",
        "Real-time data processing",
        "Power BI dashboards",
        "ETL automation",
        "Data warehouse design",
        "Predictive analytics"
      ],
      technologies: ["Azure", "Apache Spark", "Python", "SQL", "Power BI"],
      featured: true,
    },
  ];

  const stats = [
    { icon: <Users className="w-6 h-6" />, value: "50+", label: "Projects Completed" },
    { icon: <CheckCircle className="w-6 h-6" />, value: "98%", label: "Client Satisfaction" },
    { icon: <Zap className="w-6 h-6" />, value: "24hrs", label: "Response Time" },
    { icon: <Rocket className="w-6 h-6" />, value: "5+", label: "Years Experience" },
  ];

  return (
    <section id="consulting-services" className="py-24 bg-card">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="font-display text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-800 to-tech-purple bg-clip-text text-transparent">
              Consulting Services
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Expert technical consulting services to transform your business with cutting-edge technology solutions
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center p-6">
              <CardContent className="p-0">
                <div className="text-tech-cyan mb-2 flex justify-center">
                  {stat.icon}
                </div>
                <div className="text-2xl font-bold mb-1">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {services.map((service, index) => (
            <Card
              key={service.slug}
              className={`tech-card overflow-hidden hover:shadow-2xl transition-all duration-300 ${
                service.featured ? 'ring-2 ring-tech-purple/20' : ''
              }`}
              data-testid={`service-${service.slug}`}
            >
              {/* Service Header */}
              <div className={`h-32 bg-gradient-to-br ${service.gradient} relative`}>
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                  <div className="text-white">{service.icon}</div>
                </div>
                {service.featured && (
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-tech-purple text-white">Featured</Badge>
                  </div>
                )}
              </div>

              <CardContent className="p-8">
                <h3 className="font-display text-xl font-bold mb-2">{service.title}</h3>
                <p className="text-muted-foreground mb-4">{service.description}</p>

                <div className="mb-6">
                  <div className="text-lg font-semibold text-tech-purple mb-2">
                    {service.pricing}
                  </div>
                </div>

                {/* Features */}
                <div className="mb-6">
                  <h4 className="font-semibold mb-3">What's Included:</h4>
                  <div className="space-y-2">
                    {service.features.slice(0, 3).map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-sm">
                        <CheckCircle className="w-4 h-4 text-tech-emerald mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                    {service.features.length > 3 && (
                      <div className="text-sm text-muted-foreground">
                        +{service.features.length - 3} more features
                      </div>
                    )}
                  </div>
                </div>

                {/* Technologies */}
                <div className="mb-6">
                  <div className="flex flex-wrap gap-2">
                    {service.technologies.slice(0, 3).map((tech, techIndex) => (
                      <Badge
                        key={techIndex}
                        variant="secondary"
                        className="text-xs"
                      >
                        {tech}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between">
                  <Link href="/consulting-services">
                    <Button
                      variant="ghost"
                      className="text-tech-cyan hover:text-tech-purple transition-colors p-0"
                      data-testid={`service-learn-more-${index}`}
                    >
                      Learn More <ArrowRight className="w-4 h-4 ml-1" />
                    </Button>
                  </Link>
                  <Button
                    size="sm"
                    className="gradient-tech-primary text-white hover:scale-105 transition-transform"
                    data-testid={`service-get-quote-${index}`}
                  >
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Get Quote
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-tech-purple/10 to-tech-cyan/10 rounded-2xl p-8 mb-8">
            <h3 className="font-display text-2xl font-bold mb-4">Ready to Start Your Project?</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Let's discuss your requirements and create a custom solution that drives your business forward.
              Free consultation for all new projects.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="gradient-tech-primary text-white px-8 py-3 hover:scale-105 transition-transform"
                data-testid="schedule-consultation-btn"
              >
                <MessageSquare className="w-5 h-5 mr-2" />
                Schedule Free Consultation
              </Button>
              <Link href="/consulting-services">
                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-tech-cyan text-tech-cyan hover:bg-tech-cyan hover:text-white px-8 py-3"
                  data-testid="view-services-detail-btn"
                >
                  View Detailed Services
                </Button>
              </Link>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-tech-purple text-tech-purple hover:bg-tech-purple hover:text-white px-8 py-3"
                data-testid="view-portfolio-btn"
              >
                View Portfolio
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
