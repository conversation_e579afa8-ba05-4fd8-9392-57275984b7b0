import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Github,
  Linkedin,
  Twitter,
  Youtube,
  Mail,
  ExternalLink,
  MessageCircle,
  Calendar
} from "lucide-react";

interface SocialLink {
  platform: string;
  url: string;
  icon: React.ReactNode;
  color: string;
  stats?: string;
  description: string;
}

export default function SocialMedia() {
  const socialLinks: SocialLink[] = [
    {
      platform: "GitHub",
      url: "https://github.com/khiwniti",
      icon: <Github className="w-6 h-6" />,
      color: "hover:bg-gray-800 hover:text-white",
      stats: "150+ repos",
      description: "Open source projects and contributions"
    },
    {
      platform: "LinkedIn",
      url: "https://www.linkedin.com/in/getintheq",
      icon: <Linkedin className="w-6 h-6" />,
      color: "hover:bg-blue-600 hover:text-white",
      stats: "5K+ connections",
      description: "Professional network and career updates"
    },
    {
      platform: "Twitter",
      url: "https://twitter.com/khiw_dev",
      icon: <Twitter className="w-6 h-6" />,
      color: "hover:bg-sky-500 hover:text-white",
      stats: "2K+ followers",
      description: "Tech insights and industry thoughts"
    },
    {
      platform: "YouTube",
      url: "https://youtube.com/@khiw-dev",
      icon: <Youtube className="w-6 h-6" />,
      color: "hover:bg-red-600 hover:text-white",
      stats: "10K+ subscribers",
      description: "Tech tutorials and AI/ML content"
    }
  ];

  const contactMethods = [
    {
      title: "Email Me",
      description: "For project inquiries and collaborations",
      icon: <Mail className="w-6 h-6" />,
      action: "mailto:<EMAIL>",
      color: "hover:bg-green-600 hover:text-white"
    },
    {
      title: "Schedule Call",
      description: "Book a consultation meeting",
      icon: <Calendar className="w-6 h-6" />,
      action: "https://calendly.com/khiw-dev",
      color: "hover:bg-purple-600 hover:text-white"
    },
    {
      title: "Live Chat",
      description: "Quick questions and discussions",
      icon: <MessageCircle className="w-6 h-6" />,
      action: "#contact",
      color: "hover:bg-blue-500 hover:text-white"
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-br from-muted/30 to-background">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* Social Media Links */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h3 className="text-2xl font-bold mb-4">Connect With Me</h3>
          <p className="text-muted-foreground mb-8">
            Follow my work and connect across different platforms
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {socialLinks.map((social, index) => (
              <motion.div
                key={social.platform}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="group hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      className={`w-16 h-16 mx-auto rounded-full border-2 border-border flex items-center justify-center mb-4 transition-all duration-300 ${social.color}`}
                    >
                      {social.icon}
                    </motion.div>
                    <h4 className="font-semibold mb-2">{social.platform}</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      {social.description}
                    </p>
                    {social.stats && (
                      <div className="text-xs text-tech-cyan font-medium mb-3">
                        {social.stats}
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => window.open(social.url, '_blank')}
                    >
                      Follow
                      <ExternalLink className="w-3 h-3 ml-2" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Quick Contact Methods */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center"
        >
          <h3 className="text-2xl font-bold mb-4">Get In Touch</h3>
          <p className="text-muted-foreground mb-8">
            Multiple ways to reach out for projects and collaborations
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {contactMethods.map((method, index) => (
              <motion.div
                key={method.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer">
                  <CardContent className="p-6 text-center">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      className={`w-16 h-16 mx-auto rounded-full border-2 border-border flex items-center justify-center mb-4 transition-all duration-300 ${method.color}`}
                    >
                      {method.icon}
                    </motion.div>
                    <h4 className="font-semibold mb-2">{method.title}</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      {method.description}
                    </p>
                    <Button
                      variant="default"
                      size="sm"
                      className="w-full gradient-tech-primary text-white"
                      onClick={() => {
                        if (method.action.startsWith('mailto:')) {
                          window.location.href = method.action;
                        } else if (method.action.startsWith('http')) {
                          window.open(method.action, '_blank');
                        } else {
                          // Handle anchor links
                          document.querySelector(method.action)?.scrollIntoView({ behavior: 'smooth' });
                        }
                      }}
                    >
                      {method.title.split(' ')[0]}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Social Proof/Activity Feed Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-16 text-center"
        >
          <Card>
            <CardContent className="p-8">
              <h4 className="text-lg font-semibold mb-4">Recent Activity</h4>
              <div className="space-y-3 text-sm text-muted-foreground">
                <div className="flex items-center justify-center gap-2">
                  <Github className="w-4 h-4" />
                  <span>Published new ML tutorial repository</span>
                  <span className="text-xs">2 days ago</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <Linkedin className="w-4 h-4" />
                  <span>Shared insights on Azure Data Engineering</span>
                  <span className="text-xs">5 days ago</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <Youtube className="w-4 h-4" />
                  <span>Released new video: "Scaling ML Pipelines"</span>
                  <span className="text-xs">1 week ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

      </div>
    </section>
  );
}
