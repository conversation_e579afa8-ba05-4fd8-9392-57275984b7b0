import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Database, Brain, Code, ServerCog } from "lucide-react";

export default function Skills() {
  const skills = [
    { name: "Azure Data Engineering", level: 95, color: "tech-cyan" },
    { name: "Python & ML Frameworks", level: 92, color: "tech-purple" },
    { name: "Next.js & React", level: 88, color: "tech-emerald" },
    { name: "FastAPI & Backend", level: 90, color: "tech-orange" },
    { name: "CFD/FEA Analysis", level: 85, color: "primary-700" },
  ];

  const techCategories = [
    {
      title: "Data Engineering",
      icon: <Database className="w-5 h-5" />,
      color: "tech-cyan",
      techs: ["Azure Data Factory", "Synapse Analytics", "Apache Spark", "Kubernetes"],
    },
    {
      title: "AI/ML Development",
      icon: <Brain className="w-5 h-5" />,
      color: "tech-purple",
      techs: ["TensorFlow", "PyTorch", "LLMs", "Computer Vision"],
    },
    {
      title: "Full-Stack Development",
      icon: <Code className="w-5 h-5" />,
      color: "tech-emerald",
      techs: ["Next.js 14", "TypeScript", "Tailwind CSS", "PostgreSQL"],
    },
    {
      title: "Engineering Tools",
      icon: <ServerCog className="w-5 h-5" />,
      color: "tech-orange",
      techs: ["ANSYS", "COMSOL", "Power BI", "Docker"],
    },
  ];

  return (
    <section id="skills" className="py-24 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="font-display text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-800 to-tech-purple bg-clip-text text-transparent">
              Technical Expertise
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Comprehensive skill set spanning modern data engineering, AI/ML development, and full-stack solutions
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Technical Skills */}
          <div>
            <h3 className="font-display text-2xl font-bold mb-8 text-center">Technical Proficiency</h3>
            <div className="space-y-6">
              {skills.map((skill, index) => (
                <div key={skill.name} data-testid={`skill-${skill.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}`}>
                  <div className="flex justify-between items-center mb-2">
                    <span className={`font-semibold text-${skill.color}`}>
                      {skill.name}
                    </span>
                    <span className="text-sm font-mono">{skill.level}%</span>
                  </div>
                  <div className="h-3 bg-muted rounded-full overflow-hidden skill-bar">
                    <div 
                      className={`h-full bg-gradient-to-r from-${skill.color} to-primary-600 rounded-full transition-all duration-1000 ease-out`}
                      style={{ width: `${skill.level}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Technology Categories */}
          <div>
            <h3 className="font-display text-2xl font-bold mb-8 text-center">Technology Stack</h3>
            <div className="grid gap-4">
              {techCategories.map((category, index) => (
                <Card 
                  key={category.title}
                  className="tech-card p-6 hover:shadow-xl transition-all"
                  data-testid={`tech-category-${category.title.toLowerCase().replace(/[^a-z0-9]/g, '-')}`}
                >
                  <CardContent className="p-0">
                    <h4 className={`font-semibold text-lg mb-3 text-${category.color} flex items-center`}>
                      {category.icon}
                      <span className="ml-2">{category.title}</span>
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {category.techs.map((tech, techIndex) => (
                        <Badge 
                          key={techIndex}
                          variant="secondary"
                          className={`px-3 py-1 bg-${category.color}/10 text-${category.color} border border-${category.color}/20`}
                        >
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
