import { Badge } from "@/components/ui/badge";
import { Mail, Linkedin, Github } from "lucide-react";

export default function Footer() {
  const services = [
    "Data Engineering",
    "AI/ML Development",
    "Full-Stack Apps",
    "CFD/FEA Analysis",
    "Technical Consulting"
  ];

  const resources = [
    { name: "Technical Blog", href: "#blog" },
    { name: "AI Playground", href: "#ai-playground" },
    { name: "Case Studies", href: "#projects" },
    { name: "Documentation", href: "#" },
    { name: "Contact", href: "#contact" },
  ];

  return (
    <footer className="bg-background border-t border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          <div className="md:col-span-2">
            <div className="font-display font-bold text-2xl mb-4">
              <span className="bg-gradient-to-r from-primary-800 to-tech-cyan bg-clip-text text-transparent">
                Khiw.dev
              </span>
            </div>
            <p className="text-muted-foreground mb-6 max-w-md">
              Transforming complex data challenges into intelligent solutions through advanced AI/ML engineering and scalable architecture.
            </p>
            <div className="flex space-x-4">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 text-muted-foreground hover:text-tech-cyan"
                data-testid="footer-email"
              >
                <Mail className="w-5 h-5" />
              </a>
              <a
                href="https://www.linkedin.com/in/getintheq"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 text-muted-foreground hover:text-tech-cyan"
                data-testid="footer-linkedin"
              >
                <Linkedin className="w-5 h-5" />
              </a>
              <a
                href="https://github.com/khiwniti"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 text-muted-foreground hover:text-tech-cyan"
                data-testid="footer-github"
              >
                <Github className="w-5 h-5" />
              </a>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Services</h4>
            <ul className="space-y-2">
              {services.map((service, index) => (
                <li key={index}>
                  <span className="text-muted-foreground hover:text-tech-cyan transition-colors cursor-default">
                    {service}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Resources</h4>
            <ul className="space-y-2">
              {resources.map((resource, index) => (
                <li key={index}>
                  <a
                    href={resource.href}
                    className="text-muted-foreground hover:text-tech-cyan transition-colors"
                    data-testid={`footer-link-${resource.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}`}
                  >
                    {resource.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="border-t border-border mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-muted-foreground text-sm">
              © 2024 Khiw (Ikkyu) Nitithadachot. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a
                href="#"
                className="text-muted-foreground hover:text-tech-cyan text-sm transition-colors"
                data-testid="footer-privacy"
              >
                Privacy Policy
              </a>
              <a
                href="#"
                className="text-muted-foreground hover:text-tech-cyan text-sm transition-colors"
                data-testid="footer-terms"
              >
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
