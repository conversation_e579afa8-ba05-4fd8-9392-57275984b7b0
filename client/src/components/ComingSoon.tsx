import { <PERSON> } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Clock,
  Construction,
  Github,


  ExternalLink,
  Mail,
  Calendar
} from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import PageTransition from "@/components/PageTransition";

interface ComingSoonProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  gradient: string;
  technologies: string[];
  expectedDate?: string;
  githubUrl?: string;
  features?: string[];
}

export default function ComingSoon({
  title,
  description,
  icon,
  gradient,
  technologies,
  expectedDate = "Q1 2025",
  githubUrl,
  features = []
}: ComingSoonProps) {
  return (
    <PageTransition>
      <div className="min-h-screen bg-background">
        <Navigation />

        <div className="pt-24 pb-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <div className="flex items-center mb-8">
              <Link href="/#projects">
                <Button variant="ghost" className="text-muted-foreground hover:text-foreground">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Projects
                </Button>
              </Link>
            </div>

            {/* Coming Soon Hero */}
            <div className="text-center mb-12">
              <div className={`w-24 h-24 mx-auto mb-6 rounded-2xl bg-gradient-to-br ${gradient} flex items-center justify-center`}>
                <div className="text-white scale-150">{icon}</div>
              </div>

              <h1 className="font-display text-4xl font-bold mb-4">
                <span className="bg-gradient-to-r from-primary-800 to-tech-purple bg-clip-text text-transparent">
                  {title}
                </span>
              </h1>

              <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-6">
                {description}
              </p>

              <Badge className="bg-tech-orange text-white mb-8">
                <Construction className="w-4 h-4 mr-1" />
                Coming Soon
              </Badge>
            </div>

            {/* Main Content */}
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Project Info */}
              <div className="lg:col-span-2 space-y-6">
                <Card className="tech-card">
                  <CardContent className="p-8 text-center">
                    <div className="text-tech-orange mb-4">
                      <Clock className="w-16 h-16 mx-auto" />
                    </div>
                    <h2 className="text-2xl font-bold mb-4">Project in Development</h2>
                    <p className="text-muted-foreground mb-6 leading-relaxed">
                      This exciting project is currently under active development. We're building something amazing
                      that will showcase cutting-edge technology and innovative solutions. Stay tuned for updates!
                    </p>

                    <div className="bg-gradient-to-r from-tech-purple/10 to-tech-cyan/10 rounded-lg p-6 mb-6">
                      <div className="flex items-center justify-center mb-2">
                        <Calendar className="w-5 h-5 text-tech-purple mr-2" />
                        <span className="font-semibold">Expected Launch</span>
                      </div>
                      <div className="text-2xl font-bold text-tech-purple">{expectedDate}</div>
                    </div>

                    {features.length > 0 && (
                      <div className="text-left">
                        <h3 className="font-semibold mb-4 text-center">Planned Features</h3>
                        <ul className="space-y-2">
                          {features.map((feature, index) => (
                            <li key={index} className="flex items-center text-sm text-muted-foreground">
                              <div className="w-2 h-2 bg-tech-purple rounded-full mr-3"></div>
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Progress Update */}
                <Card className="tech-card">
                  <CardContent className="p-6">
                    <h3 className="font-semibold mb-4">Development Progress</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Planning & Architecture</span>
                        <span className="text-sm text-tech-emerald font-medium">Completed</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-tech-emerald h-2 rounded-full w-full"></div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm">Core Development</span>
                        <span className="text-sm text-tech-cyan font-medium">In Progress</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-tech-cyan h-2 rounded-full w-3/4"></div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm">Testing & Deployment</span>
                        <span className="text-sm text-muted-foreground font-medium">Planned</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-gray-300 h-2 rounded-full w-1/4"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Technologies */}
                <Card className="tech-card">
                  <CardContent className="p-6">
                    <h3 className="font-semibold mb-4">Technology Stack</h3>
                    <div className="flex flex-wrap gap-2">
                      {technologies.map((tech, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Actions */}
                <Card className="tech-card">
                  <CardContent className="p-6 space-y-4">
                    <h3 className="font-semibold mb-4">Stay Updated</h3>

                    <Button className="w-full gradient-tech-primary text-white">
                      <Mail className="w-4 h-4 mr-2" />
                      Get Notified When Ready
                    </Button>

                    {githubUrl && (
                      <Button variant="outline" className="w-full">


                        <Github className="w-4 h-4 mr-2" />

                        View Repository
                      </Button>
                    )}

                    <Button variant="outline" className="w-full">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      See Similar Projects
                    </Button>
                  </CardContent>
                </Card>

                {/* Contact */}
                <Card className="tech-card bg-gradient-to-br from-tech-purple/10 to-tech-cyan/10">
                  <CardContent className="p-6 text-center">
                    <h3 className="font-semibold mb-2">Have Questions?</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Interested in this project or have specific requirements?
                    </p>
                    <Link href="/consulting-services">
                      <Button variant="outline" className="w-full text-sm">
                        Contact for Custom Development
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>

        <Footer />
      </div>
    </PageTransition>
  );
}
