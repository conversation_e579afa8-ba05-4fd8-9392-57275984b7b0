import { Switch, Route } from "wouter";
import { AnimatePresence } from "framer-motion";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/hooks/use-theme";
import ScrollProgress from "@/components/ScrollProgress";
import BackToTop from "@/components/BackToTop";
import Home from "@/pages/home";
import Resume from "@/pages/resume";
import ConsultingServices from "@/pages/consulting-services";
import AIChatbotDemo from "@/pages/demos/ai-chatbot";
import DocumentAIDemo from "@/pages/demos/document-ai";
import AdminLogin from "@/pages/admin-login";
import AdminDashboard from "@/pages/admin-dashboard";
import BlogDetail from "@/pages/blog-detail";
import ProjectDetail from "@/pages/project-detail";
import AIPlaygroundRouter from "@/pages/ai-playground-router";
import AIPlaygroundDetail from "@/pages/ai-playground-detail";
import NotFound from "@/pages/not-found";

function Router() {
  return (
    <AnimatePresence mode="wait">
      <Switch>
        <Route path="/" component={Home} />
        <Route path="/resume" component={Resume} />
        <Route path="/consulting-services" component={ConsultingServices} />
        <Route path="/demos/ai-chatbot" component={AIChatbotDemo} />
        <Route path="/demos/document-ai" component={DocumentAIDemo} />
        <Route path="/admin/login" component={AdminLogin} />
        <Route path="/admin" component={AdminDashboard} />
        <Route path="/blog/:slug" component={BlogDetail} />
        <Route path="/projects/:slug" component={ProjectDetail} />
        <Route path="/ai-playground/:projectId?/:action?" component={AIPlaygroundRouter} />
        <Route path="/ai-playground-legacy/:slug" component={AIPlaygroundDetail} />
        <Route component={NotFound} />
      </Switch>
    </AnimatePresence>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="light" storageKey="portfolio-theme">
        <TooltipProvider>
          <ScrollProgress />
          <BackToTop />
          <Toaster />
          <Router />
        </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
