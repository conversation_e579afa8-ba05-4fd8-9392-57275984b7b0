<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> (Ikkyu) <PERSON><PERSON><PERSON>da<PERSON>t - Data Engineer & AI/ML Specialist</title>
    <meta name="description" content="Professional Data Engineer and AI/ML Specialist specializing in Azure, MLOps, and AI-powered solutions. Available for freelance projects.">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700;800&family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <script>
    tailwind.config = {
        darkMode: 'class',
        theme: {
            extend: {
                colors: {
                    primary: {
                        50: '#eff6ff',
                        100: '#dbeafe', 
                        500: '#3b82f6',
                        600: '#2563eb',
                        700: '#1d4ed8',
                        800: '#1e40af',
                        900: '#1e3a8a',
                    },
                    tech: {
                        cyan: '#06b6d4',
                        emerald: '#10b981', 
                        purple: '#8b5cf6',
                        orange: '#f97316',
                    },
                    neutral: {
                        50: '#f8fafc',
                        100: '#f1f5f9',
                        900: '#0f172a',
                    },
                    dark: {
                        primary: '#0f172a',
                        secondary: '#1e293b',
                        tertiary: '#334155',
                    }
                },
                fontFamily: {
                    display: ['Poppins', 'system-ui', 'sans-serif'],
                    sans: ['Inter', 'system-ui', 'sans-serif'],
                    mono: ['JetBrains Mono', 'Consolas', 'monospace'],
                },
                animation: {
                    'data-flow': 'dataFlow 3s ease-in-out infinite',
                    'ai-pulse': 'aiPulse 2s ease-in-out infinite',
                    'code-type': 'codeType 4s steps(40) infinite',
                    'neural-network': 'neuralNetwork 8s linear infinite',
                    'float': 'float 6s ease-in-out infinite',
                    'glow': 'glow 2s ease-in-out infinite alternate',
                }
            }
        }
    };
    </script>
    <style>
        @keyframes dataFlow {
            0%, 100% { transform: translateX(-100%); opacity: 0; }
            50% { transform: translateX(0%); opacity: 1; }
        }
        @keyframes aiPulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.05); opacity: 1; }
        }
        @keyframes codeType {
            0% { width: 0%; }
            50% { width: 100%; }
            100% { width: 0%; }
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
            to { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
        }
        
        .gradient-tech-primary {
            background: linear-gradient(135deg, #1e40af 0%, #06b6d4 100%);
        }
        .gradient-ai-ml {
            background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 50%, #10b981 100%);
        }
        .gradient-data-flow {
            background: linear-gradient(90deg, #06b6d4 0%, #8b5cf6 25%, #10b981 50%, #f97316 75%, #1e40af 100%);
        }
        
        .tech-card {
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .tech-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        .typing-animation::after {
            content: '|';
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .processing-animation {
            position: relative;
            overflow: hidden;
        }
        .processing-animation::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
            animation: dataFlow 2s infinite;
        }
    </style>
</head>
<body class="bg-neutral-50 dark:bg-dark-primary text-gray-900 dark:text-neutral-50 font-sans transition-colors duration-300">
    
    <!-- @COMPONENT: Navigation -->
    <nav class="fixed top-0 w-full bg-white/80 dark:bg-dark-primary/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 z-50 transition-colors duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <span class="text-2xl font-display font-bold gradient-tech-primary bg-clip-text text-transparent">
                        Khiw.dev
                    </span>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="#about" class="text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors">About</a>
                        <a href="#expertise" class="text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors">Expertise</a>
                        <a href="#projects" class="text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors">Projects</a>
                        <a href="#ai-playground" class="text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors">AI Playground</a>
                        <a href="#blog" class="text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors">Blog</a>
                        <a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors">Contact</a>
                    </div>
                </div>
                
                <!-- Theme Toggle & Mobile Menu -->
                <div class="flex items-center space-x-4">
                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                        <i class="fas fa-sun dark:hidden w-5 h-5"></i>
                        <i class="fas fa-moon hidden dark:block w-5 h-5"></i>
                    </button>
                    
                    <!-- Mobile menu button -->
                    <button id="mobile-menu-toggle" class="md:hidden p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                        <i class="fas fa-bars w-5 h-5"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white dark:bg-dark-primary border-t border-gray-200 dark:border-gray-700">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#about" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800">About</a>
                <a href="#expertise" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800">Expertise</a>
                <a href="#projects" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800">Projects</a>
                <a href="#ai-playground" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800">AI Playground</a>
                <a href="#blog" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800">Blog</a>
                <a href="#contact" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-700 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800">Contact</a>
            </div>
        </div>
    </nav>
    <!-- @END_COMPONENT: Navigation -->

    <!-- @COMPONENT: Hero Section -->
    <section id="hero" class="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-neutral-50 via-primary-50/30 to-tech-cyan/10 dark:from-dark-primary dark:via-dark-secondary/50 dark:to-tech-cyan/5">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <!-- Data Flow Lines -->
            <div class="absolute top-1/4 left-0 w-full h-1 bg-gradient-data-flow opacity-20 animate-data-flow"></div>
            <div class="absolute top-1/2 left-0 w-full h-1 bg-gradient-data-flow opacity-30 animate-data-flow" style="animation-delay: 1s;"></div>
            <div class="absolute top-3/4 left-0 w-full h-1 bg-gradient-data-flow opacity-20 animate-data-flow" style="animation-delay: 2s;"></div>
            
            <!-- Floating Elements -->
            <div class="absolute top-20 right-20 w-20 h-20 bg-tech-cyan/20 rounded-full animate-float"></div>
            <div class="absolute bottom-40 left-20 w-16 h-16 bg-tech-purple/20 rounded-full animate-float" style="animation-delay: 1s;"></div>
            <div class="absolute top-1/3 right-1/3 w-12 h-12 bg-tech-emerald/20 rounded-full animate-float" style="animation-delay: 2s;"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center">
                <!-- Main Heading -->
                <h1 class="text-4xl sm:text-5xl lg:text-7xl font-display font-bold mb-6">
                    <span class="block gradient-tech-primary bg-clip-text text-transparent mb-2">
                        Khiw (Ikkyu) Nitithadachot
                    </span>
                    <span class="block text-2xl sm:text-3xl lg:text-4xl text-gray-700 dark:text-gray-300 font-semibold">
                        Data Engineer & AI/ML Specialist
                    </span>
                </h1>
                
                <!-- Typing Animation -->
                <div class="text-lg sm:text-xl lg:text-2xl text-gray-600 dark:text-gray-400 mb-8 font-mono">
                    <span id="typing-text" class="typing-animation">Building intelligent data solutions...</span>
                </div>
                
                <!-- Expertise Tags -->
                <div class="flex flex-wrap justify-center gap-3 mb-12">
                    <span class="px-4 py-2 bg-tech-cyan/10 text-tech-cyan border border-tech-cyan/20 rounded-full text-sm font-medium animate-ai-pulse">
                        <i class="fas fa-database mr-2"></i>Azure Data Factory
                    </span>
                    <span class="px-4 py-2 bg-tech-purple/10 text-tech-purple border border-tech-purple/20 rounded-full text-sm font-medium animate-ai-pulse" style="animation-delay: 0.5s;">
                        <i class="fas fa-brain mr-2"></i>Machine Learning
                    </span>
                    <span class="px-4 py-2 bg-tech-emerald/10 text-tech-emerald border border-tech-emerald/20 rounded-full text-sm font-medium animate-ai-pulse" style="animation-delay: 1s;">
                        <i class="fas fa-code mr-2"></i>MLOps & K8s
                    </span>
                    <span class="px-4 py-2 bg-tech-orange/10 text-tech-orange border border-tech-orange/20 rounded-full text-sm font-medium animate-ai-pulse" style="animation-delay: 1.5s;">
                        <i class="fas fa-chart-line mr-2"></i>Business Intelligence
                    </span>
                </div>
                
                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#projects" class="inline-flex items-center px-8 py-4 gradient-tech-primary text-white font-semibold rounded-xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group">
                        <i class="fas fa-rocket mr-2 group-hover:animate-bounce"></i>
                        View My Work
                    </a>
                    <a href="#contact" class="inline-flex items-center px-8 py-4 bg-white dark:bg-dark-secondary text-primary-700 dark:text-primary-400 font-semibold rounded-xl border-2 border-primary-700 dark:border-primary-400 hover:bg-primary-700 hover:text-white dark:hover:bg-primary-700 dark:hover:text-white hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group">
                        <i class="fas fa-comment mr-2 group-hover:animate-pulse"></i>
                        Get In Touch
                    </a>
                </div>
                
                <!-- Stats -->
                <div class="mt-16 grid grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-primary-700 dark:text-primary-400">5+</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Years Experience</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-tech-cyan">50+</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Projects Completed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-tech-purple">10+</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">AI Models Deployed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-tech-emerald">24/7</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Availability</div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: Hero Section -->

    <!-- @COMPONENT: About Section -->
    <section id="about" class="py-24 bg-white dark:bg-dark-secondary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-display font-bold mb-6">
                    <span class="gradient-tech-primary bg-clip-text text-transparent">About Me</span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                    Experienced Data Engineer and AI/ML Specialist with expertise in building scalable data pipelines, 
                    deploying machine learning models, and creating intelligent business solutions across multiple industries.
                </p>
            </div>
            
            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <!-- Profile Content -->
                <div class="space-y-8">
                    <div class="tech-card bg-white/70 dark:bg-dark-tertiary/70 p-8 rounded-2xl">
                        <h3 class="text-2xl font-bold mb-4 text-primary-700 dark:text-primary-400">
                            <i class="fas fa-user-graduate mr-3"></i>Professional Background
                        </h3>
                        <p class="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                            I'm a dedicated Data Engineer and AI/ML Specialist with extensive experience in designing and implementing 
                            scalable data solutions. My expertise spans from ETL pipeline development with Azure Data Factory to 
                            deploying production-ready machine learning models using modern MLOps practices.
                        </p>
                        
                        <div class="space-y-4">
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-map-marker-alt w-5 text-tech-cyan mr-3"></i>
                                <span>Thailand | Remote Worldwide</span>
                            </div>
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-envelope w-5 text-tech-emerald mr-3"></i>
                                <span><EMAIL></span>
                            </div>
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-globe w-5 text-tech-purple mr-3"></i>
                                <span>getintheq.space</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Core Competencies -->
                    <div class="tech-card bg-white/70 dark:bg-dark-tertiary/70 p-8 rounded-2xl">
                        <h3 class="text-2xl font-bold mb-6 text-primary-700 dark:text-primary-400">
                            <i class="fas fa-cogs mr-3"></i>Core Competencies
                        </h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div class="flex items-center text-gray-700 dark:text-gray-300">
                                <i class="fas fa-check-circle text-tech-emerald mr-3"></i>
                                <span>Data Engineering & ETL</span>
                            </div>
                            <div class="flex items-center text-gray-700 dark:text-gray-300">
                                <i class="fas fa-check-circle text-tech-emerald mr-3"></i>
                                <span>Machine Learning & AI</span>
                            </div>
                            <div class="flex items-center text-gray-700 dark:text-gray-300">
                                <i class="fas fa-check-circle text-tech-emerald mr-3"></i>
                                <span>MLOps & Kubernetes</span>
                            </div>
                            <div class="flex items-center text-gray-700 dark:text-gray-300">
                                <i class="fas fa-check-circle text-tech-emerald mr-3"></i>
                                <span>Cloud Architecture (Azure)</span>
                            </div>
                            <div class="flex items-center text-gray-700 dark:text-gray-300">
                                <i class="fas fa-check-circle text-tech-emerald mr-3"></i>
                                <span>Business Intelligence</span>
                            </div>
                            <div class="flex items-center text-gray-700 dark:text-gray-300">
                                <i class="fas fa-check-circle text-tech-emerald mr-3"></i>
                                <span>CFD/FEA Analysis</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Skills Visualization -->
                <div class="space-y-8">
                    <div class="tech-card bg-white/70 dark:bg-dark-tertiary/70 p-8 rounded-2xl">
                        <h3 class="text-2xl font-bold mb-6 text-primary-700 dark:text-primary-400">
                            <i class="fas fa-chart-bar mr-3"></i>Technical Expertise
                        </h3>
                        
                        <!-- Skill Bars -->
                        <div class="space-y-6">
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Data Engineering (Azure, ETL)</span>
                                    <span class="text-sm text-tech-cyan font-semibold">95%</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="h-2 rounded-full bg-gradient-to-r from-tech-cyan to-primary-600 processing-animation" style="width: 95%"></div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Machine Learning & AI</span>
                                    <span class="text-sm text-tech-purple font-semibold">90%</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="h-2 rounded-full bg-gradient-to-r from-tech-purple to-primary-600 processing-animation" style="width: 90%; animation-delay: 0.5s;"></div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Full-Stack Development</span>
                                    <span class="text-sm text-tech-emerald font-semibold">85%</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="h-2 rounded-full bg-gradient-to-r from-tech-emerald to-primary-600 processing-animation" style="width: 85%; animation-delay: 1s;"></div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Business Intelligence</span>
                                    <span class="text-sm text-tech-orange font-semibold">88%</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="h-2 rounded-full bg-gradient-to-r from-tech-orange to-primary-600 processing-animation" style="width: 88%; animation-delay: 1.5s;"></div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">CFD/FEA Engineering</span>
                                    <span class="text-sm text-primary-600 font-semibold">80%</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="h-2 rounded-full bg-gradient-to-r from-primary-600 to-tech-cyan processing-animation" style="width: 80%; animation-delay: 2s;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Experience Timeline -->
                    <div class="tech-card bg-white/70 dark:bg-dark-tertiary/70 p-8 rounded-2xl">
                        <h3 class="text-2xl font-bold mb-6 text-primary-700 dark:text-primary-400">
                            <i class="fas fa-timeline mr-3"></i>Experience Highlights
                        </h3>
                        <div class="space-y-4">
                            <div class="border-l-4 border-tech-cyan pl-4">
                                <div class="text-sm text-tech-cyan font-semibold">2023 - Present</div>
                                <div class="font-medium text-gray-800 dark:text-gray-200">Senior Data Engineer</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">AI/ML Pipeline Development & MLOps</div>
                            </div>
                            <div class="border-l-4 border-tech-purple pl-4">
                                <div class="text-sm text-tech-purple font-semibold">2021 - 2023</div>
                                <div class="font-medium text-gray-800 dark:text-gray-200">Data Engineer</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Azure Data Factory & Business Intelligence</div>
                            </div>
                            <div class="border-l-4 border-tech-emerald pl-4">
                                <div class="text-sm text-tech-emerald font-semibold">2019 - 2021</div>
                                <div class="font-medium text-gray-800 dark:text-gray-200">CFD/FEA Engineer</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">ANSYS & COMSOL Simulation Analysis</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: About Section -->

    <!-- @COMPONENT: Expertise Section -->
    <section id="expertise" class="py-24 bg-gradient-to-br from-neutral-50 to-primary-50/20 dark:from-dark-primary dark:to-dark-secondary/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-display font-bold mb-6">
                    <span class="gradient-ai-ml bg-clip-text text-transparent">Technical Expertise</span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                    Comprehensive skill set spanning data engineering, AI/ML development, and full-stack solutions 
                    with proven industry experience across multiple domains.
                </p>
            </div>
            
            <!-- Expertise Categories -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Data Engineering -->
                <div class="tech-card bg-white/80 dark:bg-dark-tertiary/80 p-8 rounded-2xl group hover:animate-glow">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-tech-cyan/20 rounded-xl flex items-center justify-center mr-4 group-hover:animate-ai-pulse">
                            <i class="fas fa-database text-tech-cyan text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">Data Engineering</h3>
                    </div>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Building scalable data pipelines and ETL processes using modern cloud technologies and best practices.
                    </p>
                    
                    <div class="space-y-3">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-tech-cyan/10 text-tech-cyan text-xs rounded-full border border-tech-cyan/20">Azure Data Factory</span>
                            <span class="px-3 py-1 bg-tech-cyan/10 text-tech-cyan text-xs rounded-full border border-tech-cyan/20">Synapse Analytics</span>
                            <span class="px-3 py-1 bg-tech-cyan/10 text-tech-cyan text-xs rounded-full border border-tech-cyan/20">Apache Spark</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-tech-cyan/10 text-tech-cyan text-xs rounded-full border border-tech-cyan/20">PostgreSQL</span>
                            <span class="px-3 py-1 bg-tech-cyan/10 text-tech-cyan text-xs rounded-full border border-tech-cyan/20">Redis</span>
                            <span class="px-3 py-1 bg-tech-cyan/10 text-tech-cyan text-xs rounded-full border border-tech-cyan/20">Snowflake</span>
                        </div>
                    </div>
                </div>
                
                <!-- AI/ML Development -->
                <div class="tech-card bg-white/80 dark:bg-dark-tertiary/80 p-8 rounded-2xl group hover:animate-glow">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-tech-purple/20 rounded-xl flex items-center justify-center mr-4 group-hover:animate-ai-pulse">
                            <i class="fas fa-brain text-tech-purple text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">AI/ML Development</h3>
                    </div>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Developing and deploying machine learning models with focus on NLP, computer vision, and predictive analytics.
                    </p>
                    
                    <div class="space-y-3">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-tech-purple/10 text-tech-purple text-xs rounded-full border border-tech-purple/20">TensorFlow</span>
                            <span class="px-3 py-1 bg-tech-purple/10 text-tech-purple text-xs rounded-full border border-tech-purple/20">PyTorch</span>
                            <span class="px-3 py-1 bg-tech-purple/10 text-tech-purple text-xs rounded-full border border-tech-purple/20">Scikit-learn</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-tech-purple/10 text-tech-purple text-xs rounded-full border border-tech-purple/20">Hugging Face</span>
                            <span class="px-3 py-1 bg-tech-purple/10 text-tech-purple text-xs rounded-full border border-tech-purple/20">OpenCV</span>
                            <span class="px-3 py-1 bg-tech-purple/10 text-tech-purple text-xs rounded-full border border-tech-purple/20">LangChain</span>
                        </div>
                    </div>
                </div>
                
                <!-- Full-Stack Development -->
                <div class="tech-card bg-white/80 dark:bg-dark-tertiary/80 p-8 rounded-2xl group hover:animate-glow">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-tech-emerald/20 rounded-xl flex items-center justify-center mr-4 group-hover:animate-ai-pulse">
                            <i class="fas fa-code text-tech-emerald text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">Full-Stack Development</h3>
                    </div>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Creating modern web applications and APIs with emphasis on performance, scalability, and user experience.
                    </p>
                    
                    <div class="space-y-3">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-tech-emerald/10 text-tech-emerald text-xs rounded-full border border-tech-emerald/20">Next.js 14</span>
                            <span class="px-3 py-1 bg-tech-emerald/10 text-tech-emerald text-xs rounded-full border border-tech-emerald/20">React</span>
                            <span class="px-3 py-1 bg-tech-emerald/10 text-tech-emerald text-xs rounded-full border border-tech-emerald/20">TypeScript</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-tech-emerald/10 text-tech-emerald text-xs rounded-full border border-tech-emerald/20">FastAPI</span>
                            <span class="px-3 py-1 bg-tech-emerald/10 text-tech-emerald text-xs rounded-full border border-tech-emerald/20">Node.js</span>
                            <span class="px-3 py-1 bg-tech-emerald/10 text-tech-emerald text-xs rounded-full border border-tech-emerald/20">Tailwind CSS</span>
                        </div>
                    </div>
                </div>
                
                <!-- MLOps & DevOps -->
                <div class="tech-card bg-white/80 dark:bg-dark-tertiary/80 p-8 rounded-2xl group hover:animate-glow">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-primary-600/20 rounded-xl flex items-center justify-center mr-4 group-hover:animate-ai-pulse">
                            <i class="fas fa-rocket text-primary-600 text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">MLOps & DevOps</h3>
                    </div>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Implementing CI/CD pipelines, containerization, and orchestration for scalable AI/ML deployment.
                    </p>
                    
                    <div class="space-y-3">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-primary-600/10 text-primary-600 text-xs rounded-full border border-primary-600/20">Docker</span>
                            <span class="px-3 py-1 bg-primary-600/10 text-primary-600 text-xs rounded-full border border-primary-600/20">Kubernetes</span>
                            <span class="px-3 py-1 bg-primary-600/10 text-primary-600 text-xs rounded-full border border-primary-600/20">GitHub Actions</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-primary-600/10 text-primary-600 text-xs rounded-full border border-primary-600/20">MLflow</span>
                            <span class="px-3 py-1 bg-primary-600/10 text-primary-600 text-xs rounded-full border border-primary-600/20">Azure ML</span>
                            <span class="px-3 py-1 bg-primary-600/10 text-primary-600 text-xs rounded-full border border-primary-600/20">Terraform</span>
                        </div>
                    </div>
                </div>
                
                <!-- Business Intelligence -->
                <div class="tech-card bg-white/80 dark:bg-dark-tertiary/80 p-8 rounded-2xl group hover:animate-glow">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-tech-orange/20 rounded-xl flex items-center justify-center mr-4 group-hover:animate-ai-pulse">
                            <i class="fas fa-chart-line text-tech-orange text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">Business Intelligence</h3>
                    </div>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Creating insightful dashboards and analytics solutions that drive data-driven business decisions.
                    </p>
                    
                    <div class="space-y-3">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-tech-orange/10 text-tech-orange text-xs rounded-full border border-tech-orange/20">Power BI</span>
                            <span class="px-3 py-1 bg-tech-orange/10 text-tech-orange text-xs rounded-full border border-tech-orange/20">Tableau</span>
                            <span class="px-3 py-1 bg-tech-orange/10 text-tech-orange text-xs rounded-full border border-tech-orange/20">Looker</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-tech-orange/10 text-tech-orange text-xs rounded-full border border-tech-orange/20">DAX</span>
                            <span class="px-3 py-1 bg-tech-orange/10 text-tech-orange text-xs rounded-full border border-tech-orange/20">SQL</span>
                            <span class="px-3 py-1 bg-tech-orange/10 text-tech-orange text-xs rounded-full border border-tech-orange/20">Excel VBA</span>
                        </div>
                    </div>
                </div>
                
                <!-- CFD/FEA Engineering -->
                <div class="tech-card bg-white/80 dark:bg-dark-tertiary/80 p-8 rounded-2xl group hover:animate-glow">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-gray-600/20 rounded-xl flex items-center justify-center mr-4 group-hover:animate-ai-pulse">
                            <i class="fas fa-calculator text-gray-600 text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">CFD/FEA Engineering</h3>
                    </div>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Advanced computational fluid dynamics and finite element analysis for engineering simulations.
                    </p>
                    
                    <div class="space-y-3">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-gray-600/10 text-gray-600 text-xs rounded-full border border-gray-600/20">ANSYS Fluent</span>
                            <span class="px-3 py-1 bg-gray-600/10 text-gray-600 text-xs rounded-full border border-gray-600/20">COMSOL</span>
                            <span class="px-3 py-1 bg-gray-600/10 text-gray-600 text-xs rounded-full border border-gray-600/20">SolidWorks</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-gray-600/10 text-gray-600 text-xs rounded-full border border-gray-600/20">OpenFOAM</span>
                            <span class="px-3 py-1 bg-gray-600/10 text-gray-600 text-xs rounded-full border border-gray-600/20">MATLAB</span>
                            <span class="px-3 py-1 bg-gray-600/10 text-gray-600 text-xs rounded-full border border-gray-600/20">AutoCAD</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: Expertise Section -->

    <!-- @COMPONENT: Projects Section -->
    <section id="projects" class="py-24 bg-white dark:bg-dark-secondary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-display font-bold mb-6">
                    <span class="gradient-tech-primary bg-clip-text text-transparent">Featured Projects</span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                    Showcase of real-world solutions spanning data engineering, AI/ML applications, and full-stack development 
                    across various industries and use cases.
                </p>
            </div>
            
            <!-- Projects Grid -->
            <!-- @MAP: projects.map(project => ( -->
            <div class="grid lg:grid-cols-2 gap-8 mb-16">
                <!-- AI-Powered Data Pipeline -->
                <div class="tech-card bg-white dark:bg-dark-tertiary rounded-2xl overflow-hidden group">
                    <!-- Project Image with Processing Animation -->
                    <div class="relative h-64 bg-gradient-to-br from-tech-cyan/20 to-tech-purple/20 overflow-hidden">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-project-diagram text-6xl text-tech-cyan mb-4 animate-ai-pulse"></i>
                                <div class="text-lg font-mono text-gray-700 dark:text-gray-300">ETL Pipeline Visualization</div>
                            </div>
                        </div>
                        <!-- Data Flow Animation -->
                        <div class="absolute top-4 left-0 w-full h-1 bg-gradient-data-flow opacity-50 animate-data-flow"></div>
                        <div class="absolute bottom-4 left-0 w-full h-1 bg-gradient-data-flow opacity-50 animate-data-flow" style="animation-delay: 1s;"></div>
                    </div>
                    
                    <div class="p-8">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                AI-Powered ETL Pipeline
                            </h3>
                            <div class="flex space-x-2">
                                <a href="#" class="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" data-mock="true">
                                    <i class="fab fa-github text-gray-600 dark:text-gray-400"></i>
                                </a>
                                <a href="#" class="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" data-mock="true">
                                    <i class="fas fa-external-link-alt text-gray-600 dark:text-gray-400"></i>
                                </a>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 dark:text-gray-300 mb-6">
                            Scalable ETL pipeline using Azure Data Factory with integrated ML models for data quality assessment 
                            and anomaly detection. Processes 1M+ records daily with automated error handling and monitoring.
                        </p>
                        
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-tech-cyan/10 text-tech-cyan text-sm rounded-full border border-tech-cyan/20">Azure Data Factory</span>
                            <span class="px-3 py-1 bg-tech-purple/10 text-tech-purple text-sm rounded-full border border-tech-purple/20">Machine Learning</span>
                            <span class="px-3 py-1 bg-tech-emerald/10 text-tech-emerald text-sm rounded-full border border-tech-emerald/20">Python</span>
                            <span class="px-3 py-1 bg-tech-orange/10 text-tech-orange text-sm rounded-full border border-tech-orange/20">SQL</span>
                        </div>
                        
                        <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                            <span><i class="fas fa-calendar mr-2"></i>2023</span>
                            <span class="px-2 py-1 bg-tech-emerald/10 text-tech-emerald rounded-full text-xs">Production</span>
                        </div>
                    </div>
                </div>
                
                <!-- NLP Text Analytics Platform -->
                <div class="tech-card bg-white dark:bg-dark-tertiary rounded-2xl overflow-hidden group">
                    <!-- Project Image -->
                    <div class="relative h-64 bg-gradient-to-br from-tech-purple/20 to-tech-emerald/20 overflow-hidden">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-language text-6xl text-tech-purple mb-4 animate-ai-pulse" style="animation-delay: 0.5s;"></i>
                                <div class="text-lg font-mono text-gray-700 dark:text-gray-300">NLP Processing Engine</div>
                            </div>
                        </div>
                        <!-- AI Processing Animation -->
                        <div class="absolute top-1/2 left-1/4 w-12 h-12 bg-tech-purple/30 rounded-full animate-ai-pulse"></div>
                        <div class="absolute top-1/3 right-1/4 w-8 h-8 bg-tech-emerald/30 rounded-full animate-ai-pulse" style="animation-delay: 1s;"></div>
                    </div>
                    
                    <div class="p-8">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                NLP Text Analytics Platform
                            </h3>
                            <div class="flex space-x-2">
                                <a href="#" class="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" data-mock="true">
                                    <i class="fab fa-github text-gray-600 dark:text-gray-400"></i>
                                </a>
                                <a href="#" class="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" data-mock="true">
                                    <i class="fas fa-external-link-alt text-gray-600 dark:text-gray-400"></i>
                                </a>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 dark:text-gray-300 mb-6">
                            Advanced NLP platform for sentiment analysis, entity extraction, and document classification 
                            using transformer models. Deployed on Kubernetes with auto-scaling capabilities.
                        </p>
                        
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-tech-purple/10 text-tech-purple text-sm rounded-full border border-tech-purple/20">Transformers</span>
                            <span class="px-3 py-1 bg-tech-emerald/10 text-tech-emerald text-sm rounded-full border border-tech-emerald/20">FastAPI</span>
                            <span class="px-3 py-1 bg-primary-600/10 text-primary-600 text-sm rounded-full border border-primary-600/20">Kubernetes</span>
                            <span class="px-3 py-1 bg-tech-cyan/10 text-tech-cyan text-sm rounded-full border border-tech-cyan/20">PostgreSQL</span>
                        </div>
                        
                        <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                            <span><i class="fas fa-calendar mr-2"></i>2024</span>
                            <span class="px-2 py-1 bg-tech-emerald/10 text-tech-emerald rounded-full text-xs">Production</span>
                        </div>
                    </div>
                </div>
                
                <!-- Business Intelligence Dashboard -->
                <div class="tech-card bg-white dark:bg-dark-tertiary rounded-2xl overflow-hidden group">
                    <!-- Project Image -->
                    <div class="relative h-64 bg-gradient-to-br from-tech-orange/20 to-primary-600/20 overflow-hidden">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-chart-bar text-6xl text-tech-orange mb-4 animate-ai-pulse" style="animation-delay: 1s;"></i>
                                <div class="text-lg font-mono text-gray-700 dark:text-gray-300">BI Dashboard Analytics</div>
                            </div>
                        </div>
                        <!-- Chart Animation -->
                        <div class="absolute bottom-8 left-8 space-y-2">
                            <div class="w-16 h-2 bg-tech-orange/50 rounded processing-animation"></div>
                            <div class="w-12 h-2 bg-primary-600/50 rounded processing-animation" style="animation-delay: 0.5s;"></div>
                            <div class="w-20 h-2 bg-tech-cyan/50 rounded processing-animation" style="animation-delay: 1s;"></div>
                        </div>
                    </div>
                    
                    <div class="p-8">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                Interactive BI Dashboard
                            </h3>
                            <div class="flex space-x-2">
                                <a href="#" class="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" data-mock="true">
                                    <i class="fab fa-github text-gray-600 dark:text-gray-400"></i>
                                </a>
                                <a href="#" class="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" data-mock="true">
                                    <i class="fas fa-external-link-alt text-gray-600 dark:text-gray-400"></i>
                                </a>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 dark:text-gray-300 mb-6">
                            Comprehensive business intelligence solution with real-time KPI monitoring, predictive analytics, 
                            and interactive visualizations. Reduced report generation time by 80%.
                        </p>
                        
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-tech-orange/10 text-tech-orange text-sm rounded-full border border-tech-orange/20">Power BI</span>
                            <span class="px-3 py-1 bg-tech-emerald/10 text-tech-emerald text-sm rounded-full border border-tech-emerald/20">React</span>
                            <span class="px-3 py-1 bg-tech-cyan/10 text-tech-cyan text-sm rounded-full border border-tech-cyan/20">SQL Server</span>
                            <span class="px-3 py-1 bg-tech-purple/10 text-tech-purple text-sm rounded-full border border-tech-purple/20">DAX</span>
                        </div>
                        
                        <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                            <span><i class="fas fa-calendar mr-2"></i>2023</span>
                            <span class="px-2 py-1 bg-tech-emerald/10 text-tech-emerald rounded-full text-xs">Production</span>
                        </div>
                    </div>
                </div>
                
                <!-- CFD Thermal Analysis -->
                <div class="tech-card bg-white dark:bg-dark-tertiary rounded-2xl overflow-hidden group">
                    <!-- Project Image -->
                    <div class="relative h-64 bg-gradient-to-br from-gray-600/20 to-tech-cyan/20 overflow-hidden">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-fire text-6xl text-gray-600 mb-4 animate-ai-pulse" style="animation-delay: 1.5s;"></i>
                                <div class="text-lg font-mono text-gray-700 dark:text-gray-300">CFD Thermal Simulation</div>
                            </div>
                        </div>
                        <!-- Heat Transfer Animation -->
                        <div class="absolute top-4 right-4 w-8 h-8 bg-red-400/50 rounded-full animate-ai-pulse"></div>
                        <div class="absolute bottom-4 left-4 w-6 h-6 bg-blue-400/50 rounded-full animate-ai-pulse" style="animation-delay: 1s;"></div>
                    </div>
                    
                    <div class="p-8">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                CFD Thermal Analysis System
                            </h3>
                            <div class="flex space-x-2">
                                <a href="#" class="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" data-mock="true">
                                    <i class="fab fa-github text-gray-600 dark:text-gray-400"></i>
                                </a>
                                <a href="#" class="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" data-mock="true">
                                    <i class="fas fa-external-link-alt text-gray-600 dark:text-gray-400"></i>
                                </a>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 dark:text-gray-300 mb-6">
                            Advanced computational fluid dynamics simulation for thermal management in nuclear reactor components. 
                            Optimized heat transfer efficiency by 25% through design modifications.
                        </p>
                        
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-gray-600/10 text-gray-600 text-sm rounded-full border border-gray-600/20">ANSYS Fluent</span>
                            <span class="px-3 py-1 bg-tech-cyan/10 text-tech-cyan text-sm rounded-full border border-tech-cyan/20">COMSOL</span>
                            <span class="px-3 py-1 bg-tech-orange/10 text-tech-orange text-sm rounded-full border border-tech-orange/20">MATLAB</span>
                            <span class="px-3 py-1 bg-tech-purple/10 text-tech-purple text-sm rounded-full border border-tech-purple/20">Python</span>
                        </div>
                        
                        <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                            <span><i class="fas fa-calendar mr-2"></i>2022</span>
                            <span class="px-2 py-1 bg-tech-emerald/10 text-tech-emerald rounded-full text-xs">Completed</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- @END_MAP )) -->
            
            <!-- View More Projects -->
            <div class="text-center">
                <a href="#" class="inline-flex items-center px-8 py-4 gradient-tech-primary text-white font-semibold rounded-xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group" data-mock="true">
                    <i class="fab fa-github mr-3 group-hover:animate-bounce"></i>
                    View All Projects on GitHub
                    <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform"></i>
                </a>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: Projects Section -->

    <!-- @COMPONENT: AI Playground Section -->
    <section id="ai-playground" class="py-24 bg-gradient-to-br from-tech-purple/5 to-tech-cyan/5 dark:from-tech-purple/10 dark:to-tech-cyan/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-display font-bold mb-6">
                    <span class="gradient-ai-ml bg-clip-text text-transparent">AI Playground</span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                    Interactive demonstrations of AI/ML capabilities including natural language processing, 
                    computer vision, and data analytics. Try the demos below to experience the technology in action.
                </p>
            </div>
            
            <div class="grid lg:grid-cols-2 gap-8">
                <!-- Text Analytics Demo -->
                <div class="tech-card bg-white/90 dark:bg-dark-tertiary/90 p-8 rounded-2xl">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-tech-purple/20 rounded-xl flex items-center justify-center mr-4 animate-ai-pulse">
                            <i class="fas fa-language text-tech-purple text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100">Text Analytics Engine</h3>
                    </div>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Advanced NLP processing for sentiment analysis, entity extraction, and text classification.
                    </p>
                    
                    <!-- Demo Interface -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Enter text for analysis:
                            </label>
                            <textarea 
                                class="w-full p-4 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-dark-primary text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-tech-purple focus:border-tech-purple resize-none" 
                                rows="4" 
                                placeholder="Paste your text here for sentiment analysis, entity extraction, and classification..."
                                data-mock="true"
                            ></textarea>
                        </div>
                        
                        <button class="w-full bg-gradient-to-r from-tech-purple to-primary-600 text-white py-3 px-6 rounded-lg hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300 font-semibold processing-animation" data-implementation="Should integrate with NLP API">
                            <i class="fas fa-magic mr-2"></i>
                            Analyze Text
                        </button>
                        
                        <!-- Mock Results -->
                        <div class="mt-6 p-4 bg-gray-50 dark:bg-dark-primary rounded-lg border-l-4 border-tech-purple" data-mock="true">
                            <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">Analysis Results:</div>
                            <div class="space-y-2 text-sm">
                                <div><span class="font-semibold">Sentiment:</span> <span class="text-tech-emerald">Positive (0.85)</span></div>
                                <div><span class="font-semibold">Entities:</span> <span class="text-tech-cyan">Technology, Machine Learning, Data Science</span></div>
                                <div><span class="font-semibold">Category:</span> <span class="text-tech-purple">Technical Documentation</span></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Data Analytics Demo -->
                <div class="tech-card bg-white/90 dark:bg-dark-tertiary/90 p-8 rounded-2xl">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-tech-cyan/20 rounded-xl flex items-center justify-center mr-4 animate-ai-pulse" style="animation-delay: 0.5s;">
                            <i class="fas fa-chart-line text-tech-cyan text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100">Data Analytics Suite</h3>
                    </div>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Upload datasets for automated analysis, visualization, and insights generation.
                    </p>
                    
                    <!-- Demo Interface -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Upload Dataset:
                            </label>
                            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-tech-cyan transition-colors cursor-pointer" data-implementation="Should integrate with file upload API">
                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                <div class="text-sm text-gray-600 dark:text-gray-400">
                                    Drop your CSV file here or click to browse
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                    Supports CSV, Excel, JSON formats
                                </div>
                            </div>
                        </div>
                        
                        <button class="w-full bg-gradient-to-r from-tech-cyan to-tech-emerald text-white py-3 px-6 rounded-lg hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300 font-semibold processing-animation" data-implementation="Should trigger data analysis pipeline">
                            <i class="fas fa-chart-bar mr-2"></i>
                            Generate Analytics
                        </button>
                        
                        <!-- Mock Insights -->
                        <div class="mt-6 space-y-3" data-mock="true">
                            <div class="p-3 bg-tech-cyan/10 border border-tech-cyan/20 rounded-lg">
                                <div class="text-sm font-semibold text-tech-cyan mb-1">Key Insight</div>
                                <div class="text-xs text-gray-600 dark:text-gray-400">
                                    Strong correlation (0.87) detected between features A and C
                                </div>
                            </div>
                            <div class="p-3 bg-tech-orange/10 border border-tech-orange/20 rounded-lg">
                                <div class="text-sm font-semibold text-tech-orange mb-1">Recommendation</div>
                                <div class="text-xs text-gray-600 dark:text-gray-400">
                                    Consider feature engineering for improved model performance
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Computer Vision Demo -->
                <div class="tech-card bg-white/90 dark:bg-dark-tertiary/90 p-8 rounded-2xl">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-tech-emerald/20 rounded-xl flex items-center justify-center mr-4 animate-ai-pulse" style="animation-delay: 1s;">
                            <i class="fas fa-eye text-tech-emerald text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100">Computer Vision API</h3>
                    </div>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Object detection, image classification, and feature extraction powered by deep learning models.
                    </p>
                    
                    <!-- Demo Interface -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Upload Image:
                            </label>
                            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-tech-emerald transition-colors cursor-pointer" data-implementation="Should integrate with computer vision API">
                                <i class="fas fa-image text-3xl text-gray-400 mb-2"></i>
                                <div class="text-sm text-gray-600 dark:text-gray-400">
                                    Drop your image here or click to browse
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                    Supports JPG, PNG, WebP formats
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2">
                            <button class="bg-tech-emerald text-white py-2 px-3 rounded text-sm font-medium hover:shadow-md transition-all" data-implementation="Should trigger object detection">
                                Detect Objects
                            </button>
                            <button class="bg-tech-purple text-white py-2 px-3 rounded text-sm font-medium hover:shadow-md transition-all" data-implementation="Should trigger image classification">
                                Classify Image
                            </button>
                            <button class="bg-tech-cyan text-white py-2 px-3 rounded text-sm font-medium hover:shadow-md transition-all" data-implementation="Should trigger feature extraction">
                                Extract Features
                            </button>
                        </div>
                        
                        <!-- Mock Results -->
                        <div class="mt-4 p-4 bg-gray-50 dark:bg-dark-primary rounded-lg border-l-4 border-tech-emerald" data-mock="true">
                            <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">Detection Results:</div>
                            <div class="text-xs space-y-1">
                                <div>🚗 Vehicle detected (confidence: 94%)</div>
                                <div>🌳 Tree detected (confidence: 87%)</div>
                                <div>🏢 Building detected (confidence: 91%)</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Model Training Simulator -->
                <div class="tech-card bg-white/90 dark:bg-dark-tertiary/90 p-8 rounded-2xl">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-tech-orange/20 rounded-xl flex items-center justify-center mr-4 animate-ai-pulse" style="animation-delay: 1.5s;">
                            <i class="fas fa-brain text-tech-orange text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100">ML Model Trainer</h3>
                    </div>
                    
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Interactive machine learning model training with real-time progress monitoring and metrics.
                    </p>
                    
                    <!-- Training Interface -->
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Model Type:
                                </label>
                                <select class="w-full p-2 border border-gray-200 dark:border-gray-600 rounded bg-white dark:bg-dark-primary text-gray-900 dark:text-gray-100" data-mock="true">
                                    <option>Random Forest</option>
                                    <option>Neural Network</option>
                                    <option>XGBoost</option>
                                    <option>SVM</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Epochs:
                                </label>
                                <input 
                                    type="number" 
                                    value="100" 
                                    class="w-full p-2 border border-gray-200 dark:border-gray-600 rounded bg-white dark:bg-dark-primary text-gray-900 dark:text-gray-100" 
                                    data-mock="true"
                                />
                            </div>
                        </div>
                        
                        <button class="w-full bg-gradient-to-r from-tech-orange to-tech-purple text-white py-3 px-6 rounded-lg hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300 font-semibold processing-animation" data-implementation="Should start ML training simulation">
                            <i class="fas fa-play mr-2"></i>
                            Start Training
                        </button>
                        
                        <!-- Training Progress -->
                        <div class="mt-6 space-y-3" data-mock="true">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">Training Progress</span>
                                <span class="text-tech-orange font-semibold">67%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="h-2 rounded-full bg-gradient-to-r from-tech-orange to-tech-purple processing-animation" style="width: 67%"></div>
                            </div>
                            <div class="grid grid-cols-2 gap-4 text-xs">
                                <div class="text-center p-2 bg-gray-50 dark:bg-dark-primary rounded">
                                    <div class="font-semibold text-tech-emerald">Accuracy</div>
                                    <div class="text-gray-600 dark:text-gray-400">94.2%</div>
                                </div>
                                <div class="text-center p-2 bg-gray-50 dark:bg-dark-primary rounded">
                                    <div class="font-semibold text-tech-cyan">Loss</div>
                                    <div class="text-gray-600 dark:text-gray-400">0.058</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Call to Action -->
            <div class="text-center mt-16">
                <div class="tech-card bg-white/90 dark:bg-dark-tertiary/90 p-8 rounded-2xl max-w-3xl mx-auto">
                    <h3 class="text-2xl font-bold mb-4 text-gray-800 dark:text-gray-100">
                        Ready to Build Custom AI Solutions?
                    </h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        These demos showcase just a fraction of what's possible. Let's discuss how AI/ML can transform your business processes and drive intelligent automation.
                    </p>
                    <a href="#contact" class="inline-flex items-center px-8 py-4 gradient-ai-ml text-white font-semibold rounded-xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group">
                        <i class="fas fa-rocket mr-3 group-hover:animate-bounce"></i>
                        Start Your AI Project
                        <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: AI Playground Section -->

    <!-- @COMPONENT: Blog Section -->
    <section id="blog" class="py-24 bg-white dark:bg-dark-secondary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-display font-bold mb-6">
                    <span class="gradient-tech-primary bg-clip-text text-transparent">Technical Blog</span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                    Insights, tutorials, and deep dives into data engineering, AI/ML development, and modern software architecture. 
                    Sharing knowledge and best practices from real-world implementations.
                </p>
            </div>
            
            <!-- Featured Article -->
            <div class="mb-16">
                <div class="tech-card bg-gradient-to-r from-white to-neutral-50 dark:from-dark-tertiary dark:to-dark-tertiary/80 rounded-2xl overflow-hidden">
                    <div class="lg:flex">
                        <div class="lg:w-1/2 p-8 lg:p-12">
                            <div class="flex items-center mb-4">
                                <span class="px-3 py-1 bg-tech-purple/10 text-tech-purple text-sm rounded-full border border-tech-purple/20 mr-3">Featured</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">5 min read</span>
                            </div>
                            <h3 class="text-3xl lg:text-4xl font-bold mb-4 text-gray-800 dark:text-gray-100">
                                Building Production-Ready MLOps Pipelines with Kubernetes
                            </h3>
                            <p class="text-gray-600 dark:text-gray-300 mb-6 text-lg">
                                A comprehensive guide to deploying machine learning models at scale using Kubernetes, 
                                covering CI/CD integration, monitoring, and automated model versioning.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-calendar mr-2"></i>
                                    <span>December 15, 2024</span>
                                </div>
                                <a href="#" class="inline-flex items-center text-primary-600 dark:text-primary-400 font-semibold hover:text-primary-700 dark:hover:text-primary-300 transition-colors group" data-mock="true">
                                    Read Article
                                    <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                                </a>
                            </div>
                        </div>
                        <div class="lg:w-1/2 bg-gradient-to-br from-tech-purple/10 to-tech-cyan/10 p-12 flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-server text-6xl text-primary-600 mb-4 animate-ai-pulse"></i>
                                <div class="text-lg font-mono text-gray-700 dark:text-gray-300">MLOps Architecture</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Blog Articles Grid -->
            <!-- @MAP: articles.map(article => ( -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Article 1: Data Engineering -->
                <article class="tech-card bg-white dark:bg-dark-tertiary rounded-xl overflow-hidden group hover:shadow-xl transition-all duration-300">
                    <div class="h-48 bg-gradient-to-br from-tech-cyan/20 to-primary-600/20 flex items-center justify-center">
                        <i class="fas fa-database text-4xl text-tech-cyan animate-ai-pulse"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="px-2 py-1 bg-tech-cyan/10 text-tech-cyan text-xs rounded-full border border-tech-cyan/20">Data Engineering</span>
                            <span class="ml-auto text-xs text-gray-500 dark:text-gray-400">3 min read</span>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            Optimizing Azure Data Factory Performance
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            Best practices for designing efficient ETL pipelines that can handle millions of records with minimal latency and cost optimization.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                <i class="fas fa-calendar mr-1"></i>Dec 10, 2024
                            </span>
                            <a href="#" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors text-sm font-medium" data-mock="true">
                                Read More →
                            </a>
                        </div>
                    </div>
                </article>
                
                <!-- Article 2: Machine Learning -->
                <article class="tech-card bg-white dark:bg-dark-tertiary rounded-xl overflow-hidden group hover:shadow-xl transition-all duration-300">
                    <div class="h-48 bg-gradient-to-br from-tech-purple/20 to-tech-emerald/20 flex items-center justify-center">
                        <i class="fas fa-brain text-4xl text-tech-purple animate-ai-pulse" style="animation-delay: 0.5s;"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="px-2 py-1 bg-tech-purple/10 text-tech-purple text-xs rounded-full border border-tech-purple/20">Machine Learning</span>
                            <span class="ml-auto text-xs text-gray-500 dark:text-gray-400">7 min read</span>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            Fine-tuning LLMs for Domain-Specific Tasks
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            A practical approach to customizing large language models for specialized applications with limited training data and computational resources.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                <i class="fas fa-calendar mr-1"></i>Dec 8, 2024
                            </span>
                            <a href="#" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors text-sm font-medium" data-mock="true">
                                Read More →
                            </a>
                        </div>
                    </div>
                </article>
                
                <!-- Article 3: Full-Stack Development -->
                <article class="tech-card bg-white dark:bg-dark-tertiary rounded-xl overflow-hidden group hover:shadow-xl transition-all duration-300">
                    <div class="h-48 bg-gradient-to-br from-tech-emerald/20 to-tech-orange/20 flex items-center justify-center">
                        <i class="fas fa-code text-4xl text-tech-emerald animate-ai-pulse" style="animation-delay: 1s;"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="px-2 py-1 bg-tech-emerald/10 text-tech-emerald text-xs rounded-full border border-tech-emerald/20">Full-Stack</span>
                            <span class="ml-auto text-xs text-gray-500 dark:text-gray-400">5 min read</span>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            Next.js 14 App Router with AI Integration
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            Building modern web applications that seamlessly integrate AI capabilities with server-side rendering and optimal performance.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                <i class="fas fa-calendar mr-1"></i>Dec 5, 2024
                            </span>
                            <a href="#" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors text-sm font-medium" data-mock="true">
                                Read More →
                            </a>
                        </div>
                    </div>
                </article>
                
                <!-- Article 4: Business Intelligence -->
                <article class="tech-card bg-white dark:bg-dark-tertiary rounded-xl overflow-hidden group hover:shadow-xl transition-all duration-300">
                    <div class="h-48 bg-gradient-to-br from-tech-orange/20 to-primary-600/20 flex items-center justify-center">
                        <i class="fas fa-chart-bar text-4xl text-tech-orange animate-ai-pulse" style="animation-delay: 1.5s;"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="px-2 py-1 bg-tech-orange/10 text-tech-orange text-xs rounded-full border border-tech-orange/20">Business Intelligence</span>
                            <span class="ml-auto text-xs text-gray-500 dark:text-gray-400">4 min read</span>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            Advanced DAX Patterns for Power BI
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            Complex calculations and time intelligence functions that unlock deeper insights in your business intelligence dashboards.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                <i class="fas fa-calendar mr-1"></i>Dec 3, 2024
                            </span>
                            <a href="#" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors text-sm font-medium" data-mock="true">
                                Read More →
                            </a>
                        </div>
                    </div>
                </article>
                
                <!-- Article 5: CFD/FEA -->
                <article class="tech-card bg-white dark:bg-dark-tertiary rounded-xl overflow-hidden group hover:shadow-xl transition-all duration-300">
                    <div class="h-48 bg-gradient-to-br from-gray-600/20 to-tech-cyan/20 flex items-center justify-center">
                        <i class="fas fa-calculator text-4xl text-gray-600 animate-ai-pulse" style="animation-delay: 2s;"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="px-2 py-1 bg-gray-600/10 text-gray-600 text-xs rounded-full border border-gray-600/20">CFD/FEA</span>
                            <span class="ml-auto text-xs text-gray-500 dark:text-gray-400">6 min read</span>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            Automating ANSYS Simulations with Python
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            Streamlining computational fluid dynamics workflows through Python scripting and batch processing for improved efficiency.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                <i class="fas fa-calendar mr-1"></i>Nov 30, 2024
                            </span>
                            <a href="#" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors text-sm font-medium" data-mock="true">
                                Read More →
                            </a>
                        </div>
                    </div>
                </article>
                
                <!-- Article 6: DevOps -->
                <article class="tech-card bg-white dark:bg-dark-tertiary rounded-xl overflow-hidden group hover:shadow-xl transition-all duration-300">
                    <div class="h-48 bg-gradient-to-br from-primary-600/20 to-tech-purple/20 flex items-center justify-center">
                        <i class="fas fa-rocket text-4xl text-primary-600 animate-ai-pulse" style="animation-delay: 2.5s;"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="px-2 py-1 bg-primary-600/10 text-primary-600 text-xs rounded-full border border-primary-600/20">DevOps</span>
                            <span class="ml-auto text-xs text-gray-500 dark:text-gray-400">8 min read</span>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            Container Orchestration for AI Workloads
                        </h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            Best practices for deploying and scaling machine learning applications using Docker and Kubernetes in production environments.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                <i class="fas fa-calendar mr-1"></i>Nov 28, 2024
                            </span>
                            <a href="#" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors text-sm font-medium" data-mock="true">
                                Read More →
                            </a>
                        </div>
                    </div>
                </article>
            </div>
            <!-- @END_MAP )) -->
            
            <!-- Newsletter Subscription -->
            <div class="mt-16">
                <div class="tech-card bg-gradient-to-r from-primary-600 to-tech-cyan p-8 rounded-2xl text-center">
                    <h3 class="text-2xl font-bold text-white mb-4">
                        Stay Updated with Latest Insights
                    </h3>
                    <p class="text-primary-100 mb-6 max-w-2xl mx-auto">
                        Get notified when I publish new articles about data engineering, AI/ML, and software development. 
                        No spam, just valuable technical content.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                        <input 
                            type="email" 
                            placeholder="Enter your email" 
                            class="flex-1 px-4 py-3 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white focus:outline-none"
                            data-mock="true"
                        />
                        <button class="px-6 py-3 bg-white text-primary-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors whitespace-nowrap" data-implementation="Should integrate with newsletter API">
                            Subscribe Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: Blog Section -->

    <!-- @COMPONENT: Contact Section -->
    <section id="contact" class="py-24 bg-gradient-to-br from-neutral-50 to-primary-50/20 dark:from-dark-primary dark:to-dark-secondary/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-display font-bold mb-6">
                    <span class="gradient-tech-primary bg-clip-text text-transparent">Get In Touch</span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                    Ready to discuss your next data engineering or AI/ML project? I'm available for freelance work, 
                    consulting, and collaborative opportunities. Let's build something amazing together.
                </p>
            </div>
            
            <div class="grid lg:grid-cols-2 gap-16">
                <!-- Contact Information -->
                <div class="space-y-8">
                    <div class="tech-card bg-white/80 dark:bg-dark-tertiary/80 p-8 rounded-2xl">
                        <h3 class="text-2xl font-bold mb-6 text-gray-800 dark:text-gray-100">
                            <i class="fas fa-user-circle mr-3 text-primary-600"></i>Contact Information
                        </h3>
                        
                        <div class="space-y-6">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-tech-cyan/20 rounded-xl flex items-center justify-center mr-4">
                                    <i class="fas fa-envelope text-tech-cyan text-xl"></i>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-800 dark:text-gray-100">Email</div>
                                    <div class="text-gray-600 dark:text-gray-400"><EMAIL></div>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-tech-emerald/20 rounded-xl flex items-center justify-center mr-4">
                                    <i class="fas fa-globe text-tech-emerald text-xl"></i>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-800 dark:text-gray-100">Website</div>
                                    <div class="text-gray-600 dark:text-gray-400">getintheq.space</div>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-tech-purple/20 rounded-xl flex items-center justify-center mr-4">
                                    <i class="fas fa-map-marker-alt text-tech-purple text-xl"></i>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-800 dark:text-gray-100">Location</div>
                                    <div class="text-gray-600 dark:text-gray-400">Thailand | Remote Worldwide</div>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-tech-orange/20 rounded-xl flex items-center justify-center mr-4">
                                    <i class="fas fa-clock text-tech-orange text-xl"></i>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-800 dark:text-gray-100">Availability</div>
                                    <div class="text-gray-600 dark:text-gray-400">24/7 Response • UTC+7 Timezone</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Expertise Areas -->
                    <div class="tech-card bg-white/80 dark:bg-dark-tertiary/80 p-8 rounded-2xl">
                        <h3 class="text-2xl font-bold mb-6 text-gray-800 dark:text-gray-100">
                            <i class="fas fa-cogs mr-3 text-primary-600"></i>Services Offered
                        </h3>
                        
                        <div class="grid grid-cols-1 gap-4">
                            <div class="flex items-center p-3 bg-tech-cyan/10 rounded-lg border border-tech-cyan/20">
                                <i class="fas fa-database text-tech-cyan mr-3"></i>
                                <span class="text-gray-700 dark:text-gray-300">Data Engineering & ETL Development</span>
                            </div>
                            <div class="flex items-center p-3 bg-tech-purple/10 rounded-lg border border-tech-purple/20">
                                <i class="fas fa-brain text-tech-purple mr-3"></i>
                                <span class="text-gray-700 dark:text-gray-300">AI/ML Model Development & Deployment</span>
                            </div>
                            <div class="flex items-center p-3 bg-tech-emerald/10 rounded-lg border border-tech-emerald/20">
                                <i class="fas fa-code text-tech-emerald mr-3"></i>
                                <span class="text-gray-700 dark:text-gray-300">Full-Stack Application Development</span>
                            </div>
                            <div class="flex items-center p-3 bg-tech-orange/10 rounded-lg border border-tech-orange/20">
                                <i class="fas fa-chart-line text-tech-orange mr-3"></i>
                                <span class="text-gray-700 dark:text-gray-300">Business Intelligence & Analytics</span>
                            </div>
                            <div class="flex items-center p-3 bg-primary-600/10 rounded-lg border border-primary-600/20">
                                <i class="fas fa-rocket text-primary-600 mr-3"></i>
                                <span class="text-gray-700 dark:text-gray-300">MLOps & Cloud Architecture</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Form -->
                <div class="tech-card bg-white/90 dark:bg-dark-tertiary/90 p-8 rounded-2xl">
                    <h3 class="text-2xl font-bold mb-6 text-gray-800 dark:text-gray-100">
                        <i class="fas fa-paper-plane mr-3 text-primary-600"></i>Send a Message
                    </h3>
                    
                    <form class="space-y-6" data-implementation="Should integrate with contact form API">
                        <!-- @STATE: formData:object = {} -->
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Full Name *
                                </label>
                                <input 
                                    type="text" 
                                    required
                                    class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-dark-primary text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-600 focus:border-primary-600 transition-colors"
                                    placeholder="Your full name"
                                    data-bind="formData.name"
                                />
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Email Address *
                                </label>
                                <input 
                                    type="email" 
                                    required
                                    class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-dark-primary text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-600 focus:border-primary-600 transition-colors"
                                    placeholder="<EMAIL>"
                                    data-bind="formData.email"
                                />
                            </div>
                        </div>
                        
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Company/Organization
                                </label>
                                <input 
                                    type="text" 
                                    class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-dark-primary text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-600 focus:border-primary-600 transition-colors"
                                    placeholder="Your company name"
                                    data-bind="formData.company"
                                />
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Project Type
                                </label>
                                <select class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-dark-primary text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-600 focus:border-primary-600 transition-colors" data-bind="formData.projectType">
                                    <option value="">Select project type</option>
                                    <option value="data-engineering">Data Engineering</option>
                                    <option value="ai-ml">AI/ML Development</option>
                                    <option value="full-stack">Full-Stack Development</option>
                                    <option value="business-intelligence">Business Intelligence</option>
                                    <option value="consulting">Technical Consulting</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Project Budget Range
                            </label>
                            <select class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-dark-primary text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-600 focus:border-primary-600 transition-colors" data-bind="formData.budget">
                                <option value="">Select budget range</option>
                                <option value="under-10k">Under $10,000</option>
                                <option value="10k-25k">$10,000 - $25,000</option>
                                <option value="25k-50k">$25,000 - $50,000</option>
                                <option value="50k-100k">$50,000 - $100,000</option>
                                <option value="over-100k">Over $100,000</option>
                                <option value="consulting">Hourly Consulting</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Project Description *
                            </label>
                            <textarea 
                                required
                                rows="5"
                                class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-dark-primary text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-600 focus:border-primary-600 resize-none transition-colors"
                                placeholder="Please describe your project requirements, goals, and any specific technologies or constraints..."
                                data-bind="formData.description"
                            ></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Timeline
                            </label>
                            <select class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-dark-primary text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-600 focus:border-primary-600 transition-colors" data-bind="formData.timeline">
                                <option value="">Select timeline</option>
                                <option value="asap">As soon as possible</option>
                                <option value="1-month">Within 1 month</option>
                                <option value="3-months">Within 3 months</option>
                                <option value="6-months">Within 6 months</option>
                                <option value="flexible">Flexible timeline</option>
                            </select>
                        </div>
                        
                        <!-- Form Actions -->
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button 
                                type="submit"
                                class="flex-1 bg-gradient-to-r from-primary-600 to-tech-cyan text-white py-4 px-8 rounded-lg font-semibold hover:shadow-xl hover:-translate-y-0.5 transition-all duration-300 processing-animation group"
                                data-event="click:handleSubmit"
                                data-implementation="Should validate form before submission"
                            >
                                <i class="fas fa-paper-plane mr-2 group-hover:animate-bounce"></i>
                                Send Message
                            </button>
                            <button 
                                type="button"
                                class="px-6 py-4 border-2 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                                data-event="click:clearForm"
                            >
                                <i class="fas fa-undo mr-2"></i>
                                Reset Form
                            </button>
                        </div>
                        
                        <!-- Form Status -->
                        <div class="text-sm text-gray-500 dark:text-gray-400 text-center">
                            <i class="fas fa-shield-alt mr-1"></i>
                            Your information is secure and will never be shared with third parties.
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Quick Contact Options -->
            <div class="mt-16 text-center">
                <div class="tech-card bg-white/90 dark:bg-dark-tertiary/90 p-8 rounded-2xl max-w-4xl mx-auto">
                    <h3 class="text-xl font-bold mb-6 text-gray-800 dark:text-gray-100">
                        Prefer a Quick Chat?
                    </h3>
                    <div class="grid md:grid-cols-3 gap-6">
                        <a href="mailto:<EMAIL>" class="flex items-center justify-center p-4 bg-tech-cyan/10 hover:bg-tech-cyan/20 border border-tech-cyan/20 rounded-lg transition-colors group" data-mock="true">
                            <i class="fas fa-envelope text-tech-cyan text-xl mr-3 group-hover:animate-bounce"></i>
                            <div class="text-left">
                                <div class="font-semibold text-gray-800 dark:text-gray-100">Email Me</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Quick response</div>
                            </div>
                        </a>
                        <a href="#" class="flex items-center justify-center p-4 bg-tech-emerald/10 hover:bg-tech-emerald/20 border border-tech-emerald/20 rounded-lg transition-colors group" data-mock="true">
                            <i class="fab fa-linkedin text-tech-emerald text-xl mr-3 group-hover:animate-bounce"></i>
                            <div class="text-left">
                                <div class="font-semibold text-gray-800 dark:text-gray-100">LinkedIn</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Professional network</div>
                            </div>
                        </a>
                        <a href="#" class="flex items-center justify-center p-4 bg-tech-purple/10 hover:bg-tech-purple/20 border border-tech-purple/20 rounded-lg transition-colors group" data-mock="true">
                            <i class="fab fa-github text-tech-purple text-xl mr-3 group-hover:animate-bounce"></i>
                            <div class="text-left">
                                <div class="font-semibold text-gray-800 dark:text-gray-100">GitHub</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">View my code</div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: Contact Section -->

    <!-- @COMPONENT: Footer -->
    <footer class="bg-dark-primary dark:bg-gray-900 text-gray-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="lg:col-span-2">
                    <div class="flex items-center mb-6">
                        <span class="text-3xl font-display font-bold gradient-tech-primary bg-clip-text text-transparent">
                            Khiw.dev
                        </span>
                    </div>
                    <p class="text-gray-400 mb-6 max-w-md">
                        Professional Data Engineer and AI/ML Specialist crafting intelligent solutions 
                        for modern businesses. Building the future with data, one algorithm at a time.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-tech-cyan/20 hover:bg-tech-cyan/30 rounded-lg flex items-center justify-center text-tech-cyan hover:text-white transition-colors" data-mock="true">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-tech-purple/20 hover:bg-tech-purple/30 rounded-lg flex items-center justify-center text-tech-purple hover:text-white transition-colors" data-mock="true">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-tech-emerald/20 hover:bg-tech-emerald/30 rounded-lg flex items-center justify-center text-tech-emerald hover:text-white transition-colors" data-mock="true">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="mailto:<EMAIL>" class="w-10 h-10 bg-tech-orange/20 hover:bg-tech-orange/30 rounded-lg flex items-center justify-center text-tech-orange hover:text-white transition-colors">
                            <i class="fas fa-envelope"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-white font-semibold mb-6">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="#about" class="text-gray-400 hover:text-tech-cyan transition-colors">About</a></li>
                        <li><a href="#expertise" class="text-gray-400 hover:text-tech-cyan transition-colors">Expertise</a></li>
                        <li><a href="#projects" class="text-gray-400 hover:text-tech-cyan transition-colors">Projects</a></li>
                        <li><a href="#ai-playground" class="text-gray-400 hover:text-tech-cyan transition-colors">AI Playground</a></li>
                        <li><a href="#blog" class="text-gray-400 hover:text-tech-cyan transition-colors">Blog</a></li>
                        <li><a href="#contact" class="text-gray-400 hover:text-tech-cyan transition-colors">Contact</a></li>
                    </ul>
                </div>
                
                <!-- Services -->
                <div>
                    <h3 class="text-white font-semibold mb-6">Services</h3>
                    <ul class="space-y-3">
                        <li><span class="text-gray-400">Data Engineering</span></li>
                        <li><span class="text-gray-400">AI/ML Development</span></li>
                        <li><span class="text-gray-400">Full-Stack Development</span></li>
                        <li><span class="text-gray-400">Business Intelligence</span></li>
                        <li><span class="text-gray-400">MLOps & DevOps</span></li>
                        <li><span class="text-gray-400">Technical Consulting</span></li>
                    </ul>
                </div>
            </div>
            
            <!-- Bottom Bar -->
            <div class="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <div class="text-gray<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Khiw (Ikkyu) Nitithadachot - AI/ML & Data Engineering Portfolio</title>
    <meta name="description" content="Professional AI/ML Engineer & Data Specialist - Azure, Python, Next.js, and advanced analytics solutions">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        tech: {
                            cyan: '#06b6d4',
                            emerald: '#10b981',
                            purple: '#8b5cf6',
                            orange: '#f97316',
                        },
                        neutral: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            800: '#1e293b',
                            900: '#0f172a',
                        },
                    },
                    fontFamily: {
                        display: ['Poppins', 'system-ui', 'sans-serif'],
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                        mono: ['JetBrains Mono', 'Consolas', 'monospace'],
                    },
                    animation: {
                        'data-flow': 'dataFlow 3s ease-in-out infinite',
                        'ai-pulse': 'aiPulse 2s ease-in-out infinite',
                        'code-type': 'codeType 4s steps(40) infinite',
                        'neural-network': 'neuralNetwork 8s linear infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'gradient': 'gradient 15s ease infinite',
                    },
                    keyframes: {
                        dataFlow: {
                            '0%, 100%': { transform: 'translateX(-100%)', opacity: '0' },
                            '50%': { transform: 'translateX(0%)', opacity: '1' },
                        },
                        aiPulse: {
                            '0%, 100%': { transform: 'scale(1)', opacity: '0.7' },
                            '50%': { transform: 'scale(1.05)', opacity: '1' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                        gradient: {
                            '0%, 100%': { backgroundPosition: '0% 50%' },
                            '50%': { backgroundPosition: '100% 50%' },
                        }
                    },
                    backgroundSize: {
                        '400%': '400% 400%',
                    }
                }
            }
        };
    </script>

    <style>
        :root {
            --gradient-tech-primary: linear-gradient(135deg, #1e40af 0%, #06b6d4 100%);
            --gradient-ai-ml: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 50%, #10b981 100%);
            --gradient-data-flow: linear-gradient(90deg, #06b6d4, #10b981, #8b5cf6, #f97316);
        }

        .gradient-tech-primary {
            background: var(--gradient-tech-primary);
        }

        .gradient-ai-ml {
            background: var(--gradient-ai-ml);
        }

        .glass-morphism {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dark .glass-morphism {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .typing-animation {
            overflow: hidden;
            border-right: 2px solid #06b6d4;
            white-space: nowrap;
            animation: typing 4s steps(40) infinite, blink 1s infinite;
        }

        @keyframes typing {
            0%, 20% { width: 0; }
            40%, 60% { width: 100%; }
            80%, 100% { width: 0; }
        }

        @keyframes blink {
            50% { border-color: transparent; }
        }

        .tech-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .tech-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .skill-bar {
            position: relative;
            overflow: hidden;
        }

        .skill-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: skill-shine 2s infinite;
        }

        @keyframes skill-shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .neural-dot {
            width: 6px;
            height: 6px;
            background: #06b6d4;
            border-radius: 50%;
            position: absolute;
            animation: neural-pulse 2s infinite;
        }

        @keyframes neural-pulse {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
    </style>
</head>

<body class="font-sans bg-neutral-50 dark:bg-neutral-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 glass-morphism">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <div class="font-display font-bold text-xl">
                        <span class="bg-gradient-to-r from-primary-800 to-tech-cyan bg-clip-text text-transparent">
                            Khiw.dev
                        </span>
                    </div>
                </div>
                
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="#home" class="px-3 py-2 text-sm font-medium hover:text-tech-cyan transition-colors">Home</a>
                        <a href="#about" class="px-3 py-2 text-sm font-medium hover:text-tech-cyan transition-colors">About</a>
                        <a href="#projects" class="px-3 py-2 text-sm font-medium hover:text-tech-cyan transition-colors">Projects</a>
                        <a href="#skills" class="px-3 py-2 text-sm font-medium hover:text-tech-cyan transition-colors">Skills</a>
                        <a href="#ai-playground" class="px-3 py-2 text-sm font-medium hover:text-tech-cyan transition-colors">AI Playground</a>
                        <a href="#blog" class="px-3 py-2 text-sm font-medium hover:text-tech-cyan transition-colors">Blog</a>
                        <a href="#contact" class="px-3 py-2 text-sm font-medium hover:text-tech-cyan transition-colors">Contact</a>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <button id="theme-toggle" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                        <i class="fas fa-moon dark:hidden text-lg"></i>
                        <i class="fas fa-sun hidden dark:block text-lg"></i>
                    </button>
                    
                    <!-- Mobile menu button -->
                    <button class="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800">
                        <i class="fas fa-bars text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden pt-16">
        <!-- Background Animation -->
        <div class="absolute inset-0 opacity-10">
            <div class="neural-dot" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
            <div class="neural-dot" style="top: 40%; left: 20%; animation-delay: 0.5s;"></div>
            <div class="neural-dot" style="top: 60%; left: 15%; animation-delay: 1s;"></div>
            <div class="neural-dot" style="top: 30%; right: 20%; animation-delay: 1.5s;"></div>
            <div class="neural-dot" style="top: 70%; right: 10%; animation-delay: 2s;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="max-w-4xl mx-auto">
                <h1 class="font-display text-4xl sm:text-5xl lg:text-7xl font-bold mb-6">
                    <span class="bg-gradient-to-r from-primary-800 via-tech-cyan to-tech-purple bg-clip-text text-transparent animate-gradient bg-400%">
                        Khiw (Ikkyu) Nitithadachot
                    </span>
                </h1>
                
                <div class="text-xl sm:text-2xl lg:text-3xl font-medium mb-8 h-16 flex items-center justify-center">
                    <span class="typing-animation font-mono text-tech-cyan">
                        Data Engineer & AI/ML Specialist
                    </span>
                </div>
                
                <p class="text-lg sm:text-xl text-gray-600 dark:text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
                    Transforming complex data into intelligent solutions through advanced analytics, 
                    machine learning, and scalable engineering. Specialized in Azure ecosystems, 
                    LLM applications, and enterprise-grade AI implementations.
                </p>

                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
                    <button class="gradient-tech-primary text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl">
                        <i class="fas fa-rocket mr-2"></i>
                        Explore AI Playground
                    </button>
                    <button class="border-2 border-tech-cyan text-tech-cyan px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:bg-tech-cyan hover:text-white">
                        <i class="fas fa-download mr-2"></i>
                        Download Resume
                    </button>
                </div>

                <!-- Tech Stack Preview -->
                <div class="grid grid-cols-2 sm:grid-cols-4 gap-6 max-w-4xl mx-auto">
                    <div class="tech-card p-6 bg-white dark:bg-neutral-800 rounded-xl shadow-lg">
                        <i class="fab fa-microsoft text-3xl text-tech-cyan mb-2 block"></i>
                        <h3 class="font-semibold text-sm">Azure Ecosystem</h3>
                    </div>
                    <div class="tech-card p-6 bg-white dark:bg-neutral-800 rounded-xl shadow-lg">
                        <i class="fab fa-python text-3xl text-tech-emerald mb-2 block"></i>
                        <h3 class="font-semibold text-sm">Python/FastAPI</h3>
                    </div>
                    <div class="tech-card p-6 bg-white dark:bg-neutral-800 rounded-xl shadow-lg">
                        <i class="fas fa-brain text-3xl text-tech-purple mb-2 block"></i>
                        <h3 class="font-semibold text-sm">AI/ML Models</h3>
                    </div>
                    <div class="tech-card p-6 bg-white dark:bg-neutral-800 rounded-xl shadow-lg">
                        <i class="fas fa-chart-line text-3xl text-tech-orange mb-2 block"></i>
                        <h3 class="font-semibold text-sm">Data Analytics</h3>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-24 bg-white dark:bg-neutral-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-display text-4xl sm:text-5xl font-bold mb-6">
                    <span class="bg-gradient-to-r from-primary-800 to-tech-cyan bg-clip-text text-transparent">
                        About Me
                    </span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    Passionate about leveraging cutting-edge technology to solve complex business challenges 
                    through data-driven insights and intelligent automation.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <div class="space-y-6">
                    <!-- Professional image placeholder -->
                    <div class="w-full h-96 bg-gradient-to-br from-tech-cyan to-tech-purple rounded-2xl animate-float shadow-2xl relative overflow-hidden">
                        <div class="absolute inset-4 bg-white dark:bg-neutral-900 rounded-xl flex items-center justify-center">
                            <i class="fas fa-user text-6xl text-gray-300"></i>
                        </div>
                    </div>
                    
                    <div class="flex justify-center space-x-6">
                        <a href="mailto:<EMAIL>" class="text-tech-cyan hover:text-tech-purple transition-colors">
                            <i class="fas fa-envelope text-2xl"></i>
                        </a>
                        <a href="https://linkedin.com" class="text-tech-cyan hover:text-tech-purple transition-colors">
                            <i class="fab fa-linkedin text-2xl"></i>
                        </a>
                        <a href="https://github.com" class="text-tech-cyan hover:text-tech-purple transition-colors">
                            <i class="fab fa-github text-2xl"></i>
                        </a>
                    </div>
                </div>

                <div class="space-y-8">
                    <div>
                        <h3 class="font-display text-2xl font-bold mb-4">Professional Journey</h3>
                        <p class="text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                            With extensive experience in data engineering and AI/ML development, I specialize in creating 
                            scalable solutions that bridge the gap between complex data challenges and business objectives. 
                            My expertise spans from traditional CFD/FEA analysis to cutting-edge LLM applications.
                        </p>
                    </div>

                    <div class="grid sm:grid-cols-2 gap-6">
                        <div class="p-6 bg-neutral-50 dark:bg-neutral-700 rounded-xl">
                            <h4 class="font-semibold text-lg mb-2 text-tech-cyan">Data Engineering</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-300">
                                Azure Data Factory, Synapse Analytics, ETL pipelines, and real-time data processing
                            </p>
                        </div>
                        <div class="p-6 bg-neutral-50 dark:bg-neutral-700 rounded-xl">
                            <h4 class="font-semibold text-lg mb-2 text-tech-purple">AI/ML Development</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-300">
                                LLMs, NLP, Computer Vision, and predictive analytics with production deployment
                            </p>
                        </div>
                        <div class="p-6 bg-neutral-50 dark:bg-neutral-700 rounded-xl">
                            <h4 class="font-semibold text-lg mb-2 text-tech-emerald">Full-Stack Development</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-300">
                                Next.js, FastAPI, TypeScript, and modern web application architectures
                            </p>
                        </div>
                        <div class="p-6 bg-neutral-50 dark:bg-neutral-700 rounded-xl">
                            <h4 class="font-semibold text-lg mb-2 text-tech-orange">Engineering Analysis</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-300">
                                CFD/FEA simulation, ANSYS, COMSOL, and mechanical engineering solutions
                            </p>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-semibold text-lg mb-4">Industry Experience</h4>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-tech-cyan text-white rounded-full text-sm">Nuclear Technology</span>
                            <span class="px-3 py-1 bg-tech-emerald text-white rounded-full text-sm">Food Packaging</span>
                            <span class="px-3 py-1 bg-tech-purple text-white rounded-full text-sm">Oil & Gas</span>
                            <span class="px-3 py-1 bg-tech-orange text-white rounded-full text-sm">Business Intelligence</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="py-24 bg-neutral-50 dark:bg-neutral-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-display text-4xl sm:text-5xl font-bold mb-6">
                    <span class="bg-gradient-to-r from-primary-800 to-tech-purple bg-clip-text text-transparent">
                        Technical Expertise
                    </span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    Comprehensive skill set spanning modern data engineering, AI/ML development, and full-stack solutions
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-12">
                <!-- Technical Skills -->
                <div>
                    <h3 class="font-display text-2xl font-bold mb-8 text-center">Technical Proficiency</h3>
                    <div class="space-y-6">
                        <!-- Azure Data Engineering -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-semibold text-tech-cyan">Azure Data Engineering</span>
                                <span class="text-sm font-mono">95%</span>
                            </div>
                            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden skill-bar">
                                <div class="h-full bg-gradient-to-r from-tech-cyan to-primary-600 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>

                        <!-- Python/ML -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-semibold text-tech-purple">Python & ML Frameworks</span>
                                <span class="text-sm font-mono">92%</span>
                            </div>
                            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden skill-bar">
                                <div class="h-full bg-gradient-to-r from-tech-purple to-tech-cyan rounded-full" style="width: 92%"></div>
                            </div>
                        </div>

                        <!-- Next.js/React -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-semibold text-tech-emerald">Next.js & React</span>
                                <span class="text-sm font-mono">88%</span>
                            </div>
                            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden skill-bar">
                                <div class="h-full bg-gradient-to-r from-tech-emerald to-tech-cyan rounded-full" style="width: 88%"></div>
                            </div>
                        </div>

                        <!-- FastAPI -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-semibold text-tech-orange">FastAPI & Backend</span>
                                <span class="text-sm font-mono">90%</span>
                            </div>
                            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden skill-bar">
                                <div class="h-full bg-gradient-to-r from-tech-orange to-primary-600 rounded-full" style="width: 90%"></div>
                            </div>
                        </div>

                        <!-- CFD/FEA -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-semibold text-primary-700">CFD/FEA Analysis</span>
                                <span class="text-sm font-mono">85%</span>
                            </div>
                            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden skill-bar">
                                <div class="h-full bg-gradient-to-r from-primary-700 to-tech-purple rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technology Categories -->
                <div>
                    <h3 class="font-display text-2xl font-bold mb-8 text-center">Technology Stack</h3>
                    <div class="grid gap-4">
                        <div class="tech-card p-6 bg-white dark:bg-neutral-800 rounded-xl shadow-lg">
                            <h4 class="font-semibold text-lg mb-3 text-tech-cyan flex items-center">
                                <i class="fas fa-database mr-2"></i>
                                Data Engineering
                            </h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-tech-cyan bg-opacity-10 text-tech-cyan rounded-full text-sm">Azure Data Factory</span>
                                <span class="px-3 py-1 bg-tech-cyan bg-opacity-10 text-tech-cyan rounded-full text-sm">Synapse Analytics</span>
                                <span class="px-3 py-1 bg-tech-cyan bg-opacity-10 text-tech-cyan rounded-full text-sm">Apache Spark</span>
                                <span class="px-3 py-1 bg-tech-cyan bg-opacity-10 text-tech-cyan rounded-full text-sm">Kubernetes</span>
                            </div>
                        </div>

                        <div class="tech-card p-6 bg-white dark:bg-neutral-800 rounded-xl shadow-lg">
                            <h4 class="font-semibold text-lg mb-3 text-tech-purple flex items-center">
                                <i class="fas fa-brain mr-2"></i>
                                AI/ML Development
                            </h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-tech-purple bg-opacity-10 text-tech-purple rounded-full text-sm">TensorFlow</span>
                                <span class="px-3 py-1 bg-tech-purple bg-opacity-10 text-tech-purple rounded-full text-sm">PyTorch</span>
                                <span class="px-3 py-1 bg-tech-purple bg-opacity-10 text-tech-purple rounded-full text-sm">LLMs</span>
                                <span class="px-3 py-1 bg-tech-purple bg-opacity-10 text-tech-purple rounded-full text-sm">Computer Vision</span>
                            </div>
                        </div>

                        <div class="tech-card p-6 bg-white dark:bg-neutral-800 rounded-xl shadow-lg">
                            <h4 class="font-semibold text-lg mb-3 text-tech-emerald flex items-center">
                                <i class="fas fa-code mr-2"></i>
                                Full-Stack Development
                            </h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-tech-emerald bg-opacity-10 text-tech-emerald rounded-full text-sm">Next.js 14</span>
                                <span class="px-3 py-1 bg-tech-emerald bg-opacity-10 text-tech-emerald rounded-full text-sm">TypeScript</span>
                                <span class="px-3 py-1 bg-tech-emerald bg-opacity-10 text-tech-emerald rounded-full text-sm">Tailwind CSS</span>
                                <span class="px-3 py-1 bg-tech-emerald bg-opacity-10 text-tech-emerald rounded-full text-sm">PostgreSQL</span>
                            </div>
                        </div>

                        <div class="tech-card p-6 bg-white dark:bg-neutral-800 rounded-xl shadow-lg">
                            <h4 class="font-semibold text-lg mb-3 text-tech-orange flex items-center">
                                <i class="fas fa-cogs mr-2"></i>
                                Engineering Tools
                            </h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-tech-orange bg-opacity-10 text-tech-orange rounded-full text-sm">ANSYS</span>
                                <span class="px-3 py-1 bg-tech-orange bg-opacity-10 text-tech-orange rounded-full text-sm">COMSOL</span>
                                <span class="px-3 py-1 bg-tech-orange bg-opacity-10 text-tech-orange rounded-full text-sm">Power BI</span>
                                <span class="px-3 py-1 bg-tech-orange bg-opacity-10 text-tech-orange rounded-full text-sm">Docker</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-24 bg-white dark:bg-neutral-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-display text-4xl sm:text-5xl font-bold mb-6">
                    <span class="bg-gradient-to-r from-primary-800 to-tech-emerald bg-clip-text text-transparent">
                        Featured Projects
                    </span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    Showcase of innovative solutions spanning data engineering, AI/ML, and full-stack development
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Project 1: AI-Powered Portfolio -->
                <div class="tech-card bg-white dark:bg-neutral-700 rounded-2xl shadow-lg overflow-hidden">
                    <!-- Project showcase image representing a modern portfolio website interface -->
                    <div class="h-48 bg-gradient-to-br from-tech-purple to-tech-cyan relative overflow-hidden">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                            <i class="fas fa-laptop-code text-4xl text-white"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="px-2 py-1 bg-tech-purple text-white text-xs rounded-full">Live</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="font-display text-xl font-bold mb-2">AI-Powered Portfolio Platform</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            Next.js 14 portfolio with integrated AI playground, featuring FastAPI backend and Strapi CMS
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-2 py-1 bg-tech-emerald bg-opacity-10 text-tech-emerald rounded text-xs">Next.js</span>
                            <span class="px-2 py-1 bg-tech-orange bg-opacity-10 text-tech-orange rounded text-xs">FastAPI</span>
                            <span class="px-2 py-1 bg-tech-purple bg-opacity-10 text-tech-purple rounded text-xs">AI/ML</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <a href="#" class="text-tech-cyan hover:text-tech-purple transition-colors">
                                <i class="fas fa-external-link-alt mr-1"></i> View Demo
                            </a>
                            <a href="#" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                                <i class="fab fa-github text-lg"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project 2: Azure Data Pipeline -->
                <div class="tech-card bg-white dark:bg-neutral-700 rounded-2xl shadow-lg overflow-hidden">
                    <!-- Industrial data pipeline visualization -->
                    <div class="h-48 bg-gradient-to-br from-tech-cyan to-primary-600 relative overflow-hidden">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                            <i class="fas fa-project-diagram text-4xl text-white"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="px-2 py-1 bg-tech-cyan text-white text-xs rounded-full">Production</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="font-display text-xl font-bold mb-2">Enterprise Data Pipeline</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            Scalable Azure Data Factory pipeline processing 1TB+ daily with real-time analytics
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-2 py-1 bg-tech-cyan bg-opacity-10 text-tech-cyan rounded text-xs">Azure ADF</span>
                            <span class="px-2 py-1 bg-tech-cyan bg-opacity-10 text-tech-cyan rounded text-xs">Synapse</span>
                            <span class="px-2 py-1 bg-tech-orange bg-opacity-10 text-tech-orange rounded text-xs">Apache Spark</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <a href="#" class="text-tech-cyan hover:text-tech-purple transition-colors">
                                <i class="fas fa-info-circle mr-1"></i> Case Study
                            </a>
                            <span class="text-gray-400 text-sm">Enterprise</span>
                        </div>
                    </div>
                </div>

                <!-- Project 3: ML Model Deployment -->
                <div class="tech-card bg-white dark:bg-neutral-700 rounded-2xl shadow-lg overflow-hidden">
                    <!-- Machine learning model visualization with neural network patterns -->
                    <div class="h-48 bg-gradient-to-br from-tech-purple to-tech-emerald relative overflow-hidden">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                            <i class="fas fa-brain text-4xl text-white"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="px-2 py-1 bg-tech-purple text-white text-xs rounded-full">AI/ML</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="font-display text-xl font-bold mb-2">Predictive Analytics Platform</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            Production ML pipeline with automated retraining and A/B testing for business intelligence
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-2 py-1 bg-tech-purple bg-opacity-10 text-tech-purple rounded text-xs">TensorFlow</span>
                            <span class="px-2 py-1 bg-tech-purple bg-opacity-10 text-tech-purple rounded text-xs">MLOps</span>
                            <span class="px-2 py-1 bg-tech-cyan bg-opacity-10 text-tech-cyan rounded text-xs">Kubernetes</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <a href="#" class="text-tech-cyan hover:text-tech-purple transition-colors">
                                <i class="fas fa-chart-line mr-1"></i> Analytics
                            </a>
                            <a href="#" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                                <i class="fab fa-github text-lg"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project 4: CFD Analysis Tool -->
                <div class="tech-card bg-white dark:bg-neutral-700 rounded-2xl shadow-lg overflow-hidden">
                    <!-- Engineering simulation visualization showing fluid dynamics -->
                    <div class="h-48 bg-gradient-to-br from-tech-orange to-primary-700 relative overflow-hidden">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                            <i class="fas fa-wind text-4xl text-white"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="px-2 py-1 bg-tech-orange text-white text-xs rounded-full">Engineering</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="font-display text-xl font-bold mb-2">CFD Analysis Platform</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            Web-based CFD simulation tool with ANSYS integration for nuclear reactor thermal analysis
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-2 py-1 bg-tech-orange bg-opacity-10 text-tech-orange rounded text-xs">ANSYS</span>
                            <span class="px-2 py-1 bg-tech-orange bg-opacity-10 text-tech-orange rounded text-xs">COMSOL</span>
                            <span class="px-2 py-1 bg-tech-emerald bg-opacity-10 text-tech-emerald rounded text-xs">React</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <a href="#" class="text-tech-cyan hover:text-tech-purple transition-colors">
                                <i class="fas fa-cogs mr-1"></i> Simulation
                            </a>
                            <span class="text-gray-400 text-sm">Research</span>
                        </div>
                    </div>
                </div>

                <!-- Project 5: Real-time Dashboard -->
                <div class="tech-card bg-white dark:bg-neutral-700 rounded-2xl shadow-lg overflow-hidden">
                    <!-- Business intelligence dashboard with charts and metrics -->
                    <div class="h-48 bg-gradient-to-br from-tech-emerald to-tech-cyan relative overflow-hidden">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                            <i class="fas fa-chart-bar text-4xl text-white"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="px-2 py-1 bg-tech-emerald text-white text-xs rounded-full">Dashboard</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="font-display text-xl font-bold mb-2">Real-time BI Dashboard</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            Interactive business intelligence platform with real-time data visualization and alerting
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-2 py-1 bg-tech-emerald bg-opacity-10 text-tech-emerald rounded text-xs">Power BI</span>
                            <span class="px-2 py-1 bg-tech-cyan bg-opacity-10 text-tech-cyan rounded text-xs">Real-time</span>
                            <span class="px-2 py-1 bg-primary-600 bg-opacity-10 text-primary-600 rounded text-xs">Analytics</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <a href="#" class="text-tech-cyan hover:text-tech-purple transition-colors">
                                <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                            </a>
                            <a href="#" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project 6: LLM Application -->
                <div class="tech-card bg-white dark:bg-neutral-700 rounded-2xl shadow-lg overflow-hidden">
                    <!-- AI language model interface with chat bubbles and processing indicators -->
                    <div class="h-48 bg-gradient-to-br from-tech-purple via-tech-cyan to-tech-emerald relative overflow-hidden">
                        <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                            <i class="fas fa-comments text-4xl text-white"></i>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="px-2 py-1 bg-tech-purple text-white text-xs rounded-full">LLM</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="font-display text-xl font-bold mb-2">Enterprise LLM Solution</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            Custom LLM application for document analysis and automated report generation
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-2 py-1 bg-tech-purple bg-opacity-10 text-tech-purple rounded text-xs">OpenAI</span>
                            <span class="px-2 py-1 bg-tech-purple bg-opacity-10 text-tech-purple rounded text-xs">LangChain</span>
                            <span class="px-2 py-1 bg-tech-orange bg-opacity-10 text-tech-orange rounded text-xs">FastAPI</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <a href="#" class="text-tech-cyan hover:text-tech-purple transition-colors">
                                <i class="fas fa-robot mr-1"></i> Demo
                            </a>
                            <a href="#" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                                <i class="fab fa-github text-lg"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="gradient-tech-primary text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                    <i class="fab fa-github mr-2"></i>
                    View All Projects on GitHub
                </button>
            </div>
        </div>
    </section>

    <!-- AI Playground Section -->
    <section id="ai-playground" class="py-24 bg-neutral-50 dark:bg-neutral-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-display text-4xl sm:text-5xl font-bold mb-6">
                    <span class="bg-gradient-to-r from-tech-purple via-tech-cyan to-tech-emerald bg-clip-text text-transparent">
                        AI Playground
                    </span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    Interactive demonstrations of AI/ML capabilities and data processing tools
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- NLP Text Analysis -->
                <div class="tech-card bg-white dark:bg-neutral-800 rounded-2xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-tech-purple bg-opacity-10 rounded-2xl mb-4">
                            <i class="fas fa-language text-2xl text-tech-purple"></i>
                        </div>
                        <h3 class="font-display text-xl font-bold mb-2">NLP Text Analysis</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">
                            Sentiment analysis, entity extraction, and text classification
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <textarea 
                            class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700 resize-none"
                            rows="3"
                            placeholder="Enter text to analyze..."
                            data-mock="true"
                        >The AI revolution is transforming how we approach data analysis and business intelligence.</textarea>
                        
                        <button class="w-full gradient-tech-primary text-white py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105"
                                data-event="click:analyzeText" 
                                data-implementation="Should analyze text using NLP models">
                            <i class="fas fa-play mr-2"></i>
                            Analyze Text
                        </button>

                        <div class="space-y-2" data-mock="true">
                            <div class="flex justify-between text-sm">
                                <span>Sentiment</span>
                                <span class="text-tech-emerald font-semibold">Positive (0.85)</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span>Entities</span>
                                <span class="text-tech-cyan">AI, data analysis</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span>Category</span>
                                <span class="text-tech-purple">Technology</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Image Classification -->
                <div class="tech-card bg-white dark:bg-neutral-800 rounded-2xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-tech-cyan bg-opacity-10 rounded-2xl mb-4">
                            <i class="fas fa-image text-2xl text-tech-cyan"></i>
                        </div>
                        <h3 class="font-display text-xl font-bold mb-2">Image Classification</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">
                            Computer vision for object detection and image analysis
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                            <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-500">Upload or drag an image</p>
                            <input type="file" class="hidden" accept="image/*" data-mock-image="true" data-implementation="Use FileUpload component here" />
                        </div>
                        
                        <button class="w-full gradient-tech-primary text-white py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105"
                                data-event="click:classifyImage"
                                data-implementation="Should process image using computer vision models">
                            <i class="fas fa-eye mr-2"></i>
                            Classify Image
                        </button>

                        <div class="space-y-2" data-mock="true">
                            <div class="flex justify-between text-sm">
                                <span>Object</span>
                                <span class="text-tech-emerald font-semibold">Technology (92%)</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span>Scene</span>
                                <span class="text-tech-cyan">Office Environment</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span>Color Palette</span>
                                <span class="text-tech-purple">Blue, Gray, White</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Analysis -->
                <div class="tech-card bg-white dark:bg-neutral-800 rounded-2xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-tech-emerald bg-opacity-10 rounded-2xl mb-4">
                            <i class="fas fa-chart-line text-2xl text-tech-emerald"></i>
                        </div>
                        <h3 class="font-display text-xl font-bold mb-2">Data Analysis</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">
                            Statistical analysis and predictive modeling
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <select class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700" data-mock="true">
                            <option>Sales Performance Dataset</option>
                            <option>Customer Behavior Analytics</option>
                            <option>Market Trends Analysis</option>
                        </select>
                        
                        <button class="w-full gradient-tech-primary text-white py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105"
                                data-event="click:analyzeData"
                                data-implementation="Should perform statistical analysis and generate predictions">
                            <i class="fas fa-calculator mr-2"></i>
                            Analyze Dataset
                        </button>

                        <div class="bg-neutral-50 dark:bg-neutral-700 rounded-lg p-4" data-mock="true">
                            <div class="text-center mb-3">
                                <div class="text-2xl font-bold text-tech-emerald">87.5%</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Accuracy Score</div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span>R² Score</span>
                                    <span class="font-semibold">0.923</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span>Features</span>
                                    <span class="font-semibold">12</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CFD Simulation -->
                <div class="tech-card bg-white dark:bg-neutral-800 rounded-2xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-tech-orange bg-opacity-10 rounded-2xl mb-4">
                            <i class="fas fa-wind text-2xl text-tech-orange"></i>
                        </div>
                        <h3 class="font-display text-xl font-bold mb-2">CFD Simulation</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">
                            Fluid dynamics and heat transfer analysis
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-3">
                            <input type="number" placeholder="Velocity" class="p-2 border border-gray-200 dark:border-gray-600 rounded bg-white dark:bg-neutral-700 text-sm" data-mock="true" />
                            <input type="number" placeholder="Pressure" class="p-2 border border-gray-200 dark:border-gray-600 rounded bg-white dark:bg-neutral-700 text-sm" data-mock="true" />
                        </div>
                        
                        <button class="w-full gradient-tech-primary text-white py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105"
                                data-event="click:runCFDSimulation"
                                data-implementation="Should initiate CFD simulation with ANSYS integration">
                            <i class="fas fa-play mr-2"></i>
                            Run Simulation
                        </button>

                        <div class="bg-neutral-50 dark:bg-neutral-700 rounded-lg p-4" data-mock="true">
                            <!-- @FUNCTIONALITY: This visualization should show real CFD results -->
                            <div class="h-32 bg-gradient-to-r from-blue-400 via-green-500 to-red-500 rounded opacity-75 flex items-center justify-center">
                                <span class="text-white font-semibold">Flow Visualization</span>
                            </div>
                            <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600 dark:text-gray-300">Max Velocity:</span>
                                    <span class="font-semibold ml-1">45.2 m/s</span>
                                </div>
                                <div>
                                    <span class="text-gray-600 dark:text-gray-300">Pressure Drop:</span>
                                    <span class="font-semibold ml-1">1250 Pa</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Code Generator -->
                <div class="tech-card bg-white dark:bg-neutral-800 rounded-2xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-primary-600 bg-opacity-10 rounded-2xl mb-4">
                            <i class="fas fa-code text-2xl text-primary-600"></i>
                        </div>
                        <h3 class="font-display text-xl font-bold mb-2">Code Generator</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">
                            AI-powered code generation and optimization
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <select class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700" data-mock="true">
                            <option>Python Data Analysis</option>
                            <option>SQL Query Generator</option>
                            <option>API Endpoint Creator</option>
                        </select>
                        
                        <textarea 
                            class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700 resize-none font-mono text-sm"
                            rows="2"
                            placeholder="Describe what you want to build..."
                            data-mock="true"
                        >Create a function to calculate moving average</textarea>
                        
                        <button class="w-full gradient-tech-primary text-white py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105"
                                data-event="click:generateCode"
                                data-implementation="Should generate code using LLM integration">
                            <i class="fas fa-magic mr-2"></i>
                            Generate Code
                        </button>

                        <div class="bg-neutral-900 rounded-lg p-4 font-mono text-sm" data-mock="true">
                            <div class="text-tech-emerald"># Generated Python Code</div>
                            <div class="text-gray-300">def moving_average(data, window):</div>
                            <div class="text-gray-300 ml-4">return data.rolling(window).mean()</div>
                        </div>
                    </div>
                </div>

                <!-- Real-time Analytics -->
                <div class="tech-card bg-white dark:bg-neutral-800 rounded-2xl shadow-lg p-6">
                    <div class="text-center mb-6">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-pink-500 bg-opacity-10 rounded-2xl mb-4 animate-ai-pulse">
                            <i class="fas fa-tachometer-alt text-2xl text-pink-500"></i>
                        </div>
                        <h3 class="font-display text-xl font-bold mb-2">Real-time Analytics</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">
                            Live data processing and visualization
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-3 bg-neutral-50 dark:bg-neutral-700 rounded-lg">
                                <div class="text-xl font-bold text-tech-cyan">1,247</div>
                                <div class="text-xs text-gray-600 dark:text-gray-300">Active Users</div>
                            </div>
                            <div class="text-center p-3 bg-neutral-50 dark:bg-neutral-700 rounded-lg">
                                <div class="text-xl font-bold text-tech-emerald">98.5%</div>
                                <div class="text-xs text-gray-600 dark:text-gray-300">Uptime</div>
                            </div>
                        </div>
                        
                        <button class="w-full gradient-tech-primary text-white py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105"
                                data-event="click:viewRealTimeData"
                                data-implementation="Should display real-time dashboard with live data">
                            <i class="fas fa-chart-bar mr-2"></i>
                            View Dashboard
                        </button>

                        <div class="space-y-2" data-mock="true">
                            <div class="flex items-center justify-between">
                                <span class="text-sm">Data Processing</span>
                                <div class="flex-1 mx-3 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                    <div class="bg-tech-cyan h-2 rounded-full animate-pulse" style="width: 75%"></div>
                                </div>
                                <span class="text-sm font-semibold">75%</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm">Model Training</span>
                                <div class="flex-1 mx-3 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                    <div class="bg-tech-purple h-2 rounded-full animate-pulse" style="width: 45%"></div>
                                </div>
                                <span class="text-sm font-semibold">45%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    These are interactive demonstrations of AI/ML capabilities. Full implementations are available for enterprise clients.
                </p>
                <button class="gradient-tech-primary text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                    <i class="fas fa-rocket mr-2"></i>
                    Request Custom AI Solution
                </button>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <section id="blog" class="py-24 bg-white dark:bg-neutral-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-display text-4xl sm:text-5xl font-bold mb-6">
                    <span class="bg-gradient-to-r from-primary-800 to-tech-emerald bg-clip-text text-transparent">
                        Technical Blog
                    </span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    Insights, tutorials, and deep dives into AI/ML, data engineering, and modern web development
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Featured Article -->
                <div class="lg:col-span-2 tech-card bg-white dark:bg-neutral-700 rounded-2xl shadow-lg overflow-hidden">
                    <!-- Featured blog post image showing data visualization concepts -->
                    <div class="h-64 bg-gradient-to-br from-tech-purple to-tech-cyan relative">
                        <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                            <div class="text-center text-white">
                                <i class="fas fa-chart-bar text-4xl mb-4"></i>
                                <h3 class="text-xl font-bold">Featured Article</h3>
                            </div>
                        </div>
                    </div>
                    <div class="p-8">
                        <div class="flex items-center gap-2 mb-4">
                            <span class="px-3 py-1 bg-tech-purple bg-opacity-10 text-tech-purple rounded-full text-sm">AI/ML</span>
                            <span class="px-3 py-1 bg-tech-cyan bg-opacity-10 text-tech-cyan rounded-full text-sm">Data Engineering</span>
                            <span class="text-gray-500 text-sm">5 min read</span>
                        </div>
                        <h3 class="font-display text-2xl font-bold mb-4">Building Production-Ready ML Pipelines with Azure and Python</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                            A comprehensive guide to designing, implementing, and deploying scalable machine learning pipelines 
                            using Azure Data Factory, MLflow, and modern DevOps practices. Learn how to automate model training, 
                            validation, and deployment while ensuring data quality and model performance monitoring.
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-tech-cyan to-tech-purple rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-sm">K</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-sm">Khiw Nitithadachot</div>
                                    <div class="text-gray-500 text-xs">December 15, 2024</div>
                                </div>
                            </div>
                            <a href="#" class="text-tech-cyan hover:text-tech-purple transition-colors font-semibold">
                                Read More <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recent Articles -->
                <div class="space-y-8">
                    <div class="tech-card bg-white dark:bg-neutral-700 rounded-2xl shadow-lg overflow-hidden">
                        <!-- Technical tutorial image for Next.js and TypeScript development -->
                        <div class="h-32 bg-gradient-to-br from-tech-emerald to-tech-cyan relative">
                            <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                                <i class="fab fa-js-square text-2xl text-white"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center gap-2 mb-3">
                                <span class="px-2 py-1 bg-tech-emerald bg-opacity-10 text-tech-emerald rounded text-xs">Frontend</span>
                                <span class="text-gray-500 text-xs">3 min read</span>
                            </div>
                            <h4 class="font-display text-lg font-bold mb-2">Next.js 14 App Router: Advanced Patterns</h4>
                            <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                                Exploring advanced routing patterns, server components, and performance optimizations.
                            </p>
                            <div class="text-xs text-gray-500">Dec 10, 2024</div>
                        </div>
                    </div>

                    <div class="tech-card bg-white dark:bg-neutral-700 rounded-2xl shadow-lg overflow-hidden">
                        <!-- CFD analysis visualization for engineering blog post -->
                        <div class="h-32 bg-gradient-to-br from-tech-orange to-primary-700 relative">
                            <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                                <i class="fas fa-wind text-2xl text-white"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center gap-2 mb-3">
                                <span class="px-2 py-1 bg-tech-orange bg-opacity-10 text-tech-orange rounded text-xs">Engineering</span>
                                <span class="text-gray-500 text-xs">7 min read</span>
                            </div>
                            <h4 class="font-display text-lg font-bold mb-2">CFD Analysis for Nuclear Reactor Design</h4>
                            <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                                Using ANSYS Fluent for thermal-hydraulic analysis in nuclear reactor components.
                            </p>
                            <div class="text-xs text-gray-500">Dec 5, 2024</div>
                        </div>
                    </div>

                    <div class="tech-card bg-white dark:bg-neutral-700 rounded-2xl shadow-lg overflow-hidden">
                        <!-- Data pipeline architecture diagram for Azure blog post -->
                        <div class="h-32 bg-gradient-to-br from-tech-cyan to-primary-600 relative">
                            <div class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                                <i class="fas fa-database text-2xl text-white"></i>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center gap-2 mb-3">
                                <span class="px-2 py-1 bg-tech-cyan bg-opacity-10 text-tech-cyan rounded text-xs">Data Engineering</span>
                                <span class="text-gray-500 text-xs">6 min read</span>
                            </div>
                            <h4 class="font-display text-lg font-bold mb-2">Azure Synapse Analytics Best Practices</h4>
                            <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                                Optimizing data warehouse performance and cost management strategies.
                            </p>
                            <div class="text-xs text-gray-500">Nov 28, 2024</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="gradient-tech-primary text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                    <i class="fas fa-blog mr-2"></i>
                    View All Articles
                </button>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-24 bg-neutral-50 dark:bg-neutral-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-display text-4xl sm:text-5xl font-bold mb-6">
                    <span class="bg-gradient-to-r from-primary-800 to-tech-cyan bg-clip-text text-transparent">
                        Let's Build Something Amazing
                    </span>
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    Ready to transform your data challenges into intelligent solutions? Let's discuss your next project.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-16 max-w-6xl mx-auto">
                <!-- Contact Information -->
                <div>
                    <h3 class="font-display text-2xl font-bold mb-8">Get In Touch</h3>
                    <div class="space-y-6">
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-tech-cyan bg-opacity-10 rounded-xl flex items-center justify-center">
                                <i class="fas fa-envelope text-tech-cyan"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-2">Email</h4>
                                <p class="text-gray-600 dark:text-gray-300"><EMAIL></p>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-tech-purple bg-opacity-10 rounded-xl flex items-center justify-center">
                                <i class="fas fa-globe text-tech-purple"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-2">Website</h4>
                                <p class="text-gray-600 dark:text-gray-300">getintheq.space</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-tech-emerald bg-opacity-10 rounded-xl flex items-center justify-center">
                                <i class="fas fa-map-marker-alt text-tech-emerald"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-2">Location</h4>
                                <p class="text-gray-600 dark:text-gray-300">Available for remote & on-site projects</p>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-tech-orange bg-opacity-10 rounded-xl flex items-center justify-center">
                                <i class="fas fa-clock text-tech-orange"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-2">Response Time</h4>
                                <p class="text-gray-600 dark:text-gray-300">Within 24 hours for project inquiries</p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-12">
                        <h4 class="font-semibold mb-6">Areas of Expertise</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="p-4 bg-white dark:bg-neutral-800 rounded-xl">
                                <i class="fas fa-database text-tech-cyan mb-2 block"></i>
                                <div class="font-semibold text-sm">Data Engineering</div>
                            </div>
                            <div class="p-4 bg-white dark:bg-neutral-800 rounded-xl">
                                <i class="fas fa-brain text-tech-purple mb-2 block"></i>
                                <div class="font-semibold text-sm">AI/ML Solutions</div>
                            </div>
                            <div class="p-4 bg-white dark:bg-neutral-800 rounded-xl">
                                <i class="fas fa-code text-tech-emerald mb-2 block"></i>
                                <div class="font-semibold text-sm">Full-Stack Dev</div>
                            </div>
                            <div class="p-4 bg-white dark:bg-neutral-800 rounded-xl">
                                <i class="fas fa-wind text-tech-orange mb-2 block"></i>
                                <div class="font-semibold text-sm">CFD/FEA Analysis</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="bg-white dark:bg-neutral-800 rounded-2xl shadow-xl p-8">
                    <h3 class="font-display text-2xl font-bold mb-6">Start Your Project</h3>
                    
                    <!-- @COMPONENT: ContactForm [handles form submission and validation] -->
                    <form class="space-y-6" data-event="submit:handleContactSubmit" data-implementation="Should validate and submit contact form">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-semibold mb-2">Name *</label>
                                <input 
                                    type="text" 
                                    required
                                    class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700 focus:ring-2 focus:ring-tech-cyan focus:border-transparent transition-all"
                                    data-bind="form.name"
                                    placeholder="Your full name"
                                />
                            </div>
                            <div>
                                <label class="block text-sm font-semibold mb-2">Email *</label>
                                <input 
                                    type="email" 
                                    required
                                    class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700 focus:ring-2 focus:ring-tech-cyan focus:border-transparent transition-all"
                                    data-bind="form.email"
                                    placeholder="<EMAIL>"
                                />
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold mb-2">Company</label>
                            <input 
                                type="text" 
                                class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700 focus:ring-2 focus:ring-tech-cyan focus:border-transparent transition-all"
                                data-bind="form.company"
                                placeholder="Your company name"
                            />
                        </div>

                        <div>
                            <label class="block text-sm font-semibold mb-2">Project Type *</label>
                            <select 
                                required
                                class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700 focus:ring-2 focus:ring-tech-cyan focus:border-transparent transition-all"
                                data-bind="form.projectType"
                            >
                                <option value="">Select project type...</option>
                                <option value="data-engineering">Data Engineering & ETL</option>
                                <option value="ai-ml">AI/ML Development</option>
                                <option value="full-stack">Full-Stack Application</option>
                                <option value="cfd-fea">CFD/FEA Analysis</option>
                                <option value="consulting">Technical Consulting</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold mb-2">Project Budget</label>
                            <select 
                                class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700 focus:ring-2 focus:ring-tech-cyan focus:border-transparent transition-all"
                                data-bind="form.budget"
                            >
                                <option value="">Select budget range...</option>
                                <option value="5k-10k">$5,000 - $10,000</option>
                                <option value="10k-25k">$10,000 - $25,000</option>
                                <option value="25k-50k">$25,000 - $50,000</option>
                                <option value="50k+">$50,000+</option>
                                <option value="consulting">Hourly Consulting</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold mb-2">Project Description *</label>
                            <textarea 
                                required
                                rows="4" 
                                class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700 focus:ring-2 focus:ring-tech-cyan focus:border-transparent transition-all resize-none"
                                data-bind="form.description"
                                placeholder="Describe your project requirements, goals, and any technical specifications..."
                            ></textarea>
                        </div>

                        <button 
                            type="submit"
                            class="w-full gradient-tech-primary text-white py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl"
                        >
                            <i class="fas fa-paper-plane mr-2"></i>
                            Send Project Inquiry
                        </button>
                    </form>
                    <!-- @END_COMPONENT: ContactForm -->

                    <div class="mt-6 text-center text-sm text-gray-600 dark:text-gray-300">
                        <i class="fas fa-shield-alt text-tech-emerald mr-1"></i>
                        Your information is secure and will only be used to respond to your inquiry.
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-white dark:bg-neutral-800 border-t border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <div class="font-display font-bold text-2xl mb-4">
                        <span class="bg-gradient-to-r from-primary-800 to-tech-cyan bg-clip-text text-transparent">
                            Khiw.dev
                        </span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 mb-6 max-w-md">
                        Transforming complex data challenges into intelligent solutions through advanced AI/ML engineering and scalable architecture.
                    </p>
                    <div class="flex space-x-4">
                        <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-tech-cyan transition-colors">
                            <i class="fas fa-envelope text-xl"></i>
                        </a>
                        <a href="https://linkedin.com" class="text-gray-400 hover:text-tech-cyan transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="https://github.com" class="text-gray-400 hover:text-tech-cyan transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="https://twitter.com" class="text-gray-400 hover:text-tech-cyan transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h4 class="font-semibold mb-4">Services</h4>
                    <ul class="space-y-2 text-gray-600 dark:text-gray-300">
                        <li><a href="#" class="hover:text-tech-cyan transition-colors">Data Engineering</a></li>
                        <li><a href="#" class="hover:text-tech-cyan transition-colors">AI/ML Development</a></li>
                        <li><a href="#" class="hover:text-tech-cyan transition-colors">Full-Stack Apps</a></li>
                        <li><a href="#" class="hover:text-tech-cyan transition-colors">CFD/FEA Analysis</a></li>
                        <li><a href="#" class="hover:text-tech-cyan transition-colors">Technical Consulting</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-semibold mb-4">Resources</h4>
                    <ul class="space-y-2 text-gray-600 dark:text-gray-300">
                        <li><a href="#blog" class="hover:text-tech-cyan transition-colors">Technical Blog</a></li>
                        <li><a href="#ai-playground" class="hover:text-tech-cyan transition-colors">AI Playground</a></li>
                        <li><a href="#projects" class="hover:text-tech-cyan transition-colors">Case Studies</a></li>
                        <li><a href="#" class="hover:text-tech-cyan transition-colors">Documentation</a></li>
                        <li><a href="#contact" class="hover:text-tech-cyan transition-colors">Contact</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-200 dark:border-gray-700 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-600 dark:text-gray-300 text-sm">
                        © 2024 Khiw (Ikkyu) Nitithadachot. All rights reserved.
                    </p>
                    <div class="flex space-x-6 mt-4 md:mt-0">
                        <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-tech-cyan text-sm transition-colors">Privacy Policy</a>
                        <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-tech-cyan text-sm transition-colors">Terms of Service</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Theme toggle functionality
        (function() {
            const themeToggle = document.getElementById('theme-toggle');
            const html = document.documentElement;
            
            // Check for saved theme preference or default to light mode
            const savedTheme = localStorage.getItem('theme') || 'light';
            html.classList.toggle('dark', savedTheme === 'dark');
            
            themeToggle?.addEventListener('click', () => {
                const isDark = html.classList.contains('dark');
                html.classList.toggle('dark', !isDark);
                localStorage.setItem('theme', !isDark ? 'dark' : 'light');
            });
        })();

        // Smooth scrolling for navigation links
        (function() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        })();

        // Add intersection observer for animations
        (function() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                    }
                });
            }, { threshold: 0.1 });

            document.querySelectorAll('.tech-card').forEach(card => {
                observer.observe(card);
            });
        })();

        // TODO: Implement AI playground functionality
        // TODO: Add form validation and submission handling
        // TODO: Implement real-time data updates for analytics section
        // TODO: Add loading states for interactive components
        // TODO: Implement blog post loading from CMS
        // TODO: Add search and filtering for projects
        // TODO: Implement email subscription for blog updates
    </script>
</body>
</html>