================================================================================
                      FRONTEND IMPLEMENTATION GUIDE
                         Next.js 14 + TypeScript + Tailwind
================================================================================

┌─────────────────────────────────────────────────────────────────────────────┐
│                         PROJECT INITIALIZATION                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ # Create Next.js 14 project with all modern features                       │
│ npx create-next-app@latest portfolio-frontend \                            │
│   --typescript \                                                           │
│   --tailwind \                                                             │
│   --eslint \                                                               │
│   --app \                                                                  │
│   --src-dir \                                                              │
│   --import-alias "@/*"                                                     │
│                                                                            │
│ cd portfolio-frontend                                                      │
│                                                                            │
│ # Install essential dependencies                                           │
│ npm install \                                                              │
│   @radix-ui/react-accordion \                                             │
│   @radix-ui/react-dialog \                                                │
│   @radix-ui/react-dropdown-menu \                                         │
│   @radix-ui/react-navigation-menu \                                       │
│   @radix-ui/react-tabs \                                                  │
│   framer-motion \                                                          │
│   zustand \                                                               │
│   @tanstack/react-query \                                                 │
│   react-hook-form \                                                       │
│   @hookform/resolvers \                                                    │
│   zod \                                                                    │
│   lucide-react \                                                           │
│   next-themes \                                                            │
│   class-variance-authority \                                               │
│   clsx \                                                                   │
│   tailwind-merge                                                           │
│                                                                            │
│ # Install development dependencies                                         │
│ npm install -D \                                                           │
│   @types/node \                                                            │
│   prettier \                                                               │
│   eslint-config-prettier \                                                │
│   @tailwindcss/typography \                                                │
│   @tailwindcss/forms                                                       │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                          CONFIGURATION FILES                               │
├─────────────────────────────────────────────────────────────────────────────┤
│ next.config.js:                                                           │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ /** @type {import('next').NextConfig} */                               │ │
│ │ const nextConfig = {                                                   │ │
│ │   experimental: {                                                      │ │
│ │     typedRoutes: true,                                                 │ │
│ │   },                                                                   │ │
│ │   images: {                                                            │ │
│ │     remotePatterns: [                                                  │ │
│ │       {                                                                │ │
│ │         protocol: 'https',                                             │ │
│ │         hostname: 'getintheq.space',                                  │ │
│ │       },                                                               │ │
│ │       {                                                                │ │
│ │         protocol: 'https',                                             │ │
│ │         hostname: '*.railway.app',                                     │ │
│ │       },                                                               │ │
│ │     ],                                                                 │ │
│ │     formats: ['image/webp', 'image/avif'],                           │ │
│ │   },                                                                   │ │
│ │   async rewrites() {                                                   │ │
│ │     return [                                                           │ │
│ │       {                                                                │ │
│ │         source: '/api/cms/:path*',                                     │ │
│ │         destination: `${process.env.CMS_URL}/api/:path*`,             │ │
│ │       },                                                               │ │
│ │       {                                                                │ │
│ │         source: '/api/ai/:path*',                                      │ │
│ │         destination: `${process.env.AI_API_URL}/:path*`,              │ │
│ │       },                                                               │ │
│ │     ]                                                                  │ │
│ │   },                                                                   │ │
│ │ }                                                                      │ │
│ │ module.exports = nextConfig                                            │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                            │
│ tailwind.config.js (Professional Tech Theme):                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ module.exports = {                                                     │ │
│ │   content: ['./src/**/*.{js,ts,jsx,tsx,mdx}'],                        │ │
│ │   darkMode: 'class',                                                   │ │
│ │   theme: {                                                             │ │
│ │     extend: {                                                          │ │
│ │       colors: {                                                        │ │
│ │         primary: {                                                     │ │
│ │           50: '#eff6ff',                                               │ │
│ │           100: '#dbeafe',                                              │ │
│ │           500: '#3b82f6',                                              │ │
│ │           600: '#2563eb',                                              │ │
│ │           700: '#1d4ed8',                                              │ │
│ │           800: '#1e40af',                                              │ │
│ │           900: '#1e3a8a',                                              │ │
│ │         },                                                             │ │
│ │         tech: {                                                        │ │
│ │           cyan: '#06b6d4',                                             │ │
│ │           emerald: '#10b981',                                          │ │
│ │           purple: '#8b5cf6',                                           │ │
│ │           orange: '#f97316',                                           │ │
│ │         },                                                             │ │
│ │         neutral: {                                                     │ │
│ │           50: '#f8fafc',                                               │ │
│ │           100: '#f1f5f9',                                              │ │
│ │           900: '#0f172a',                                              │ │
│ │         },                                                             │ │
│ │       },                                                               │ │
│ │       fontFamily: {                                                    │ │
│ │         display: ['Poppins', 'system-ui', 'sans-serif'],             │ │
│ │         sans: ['Inter', 'system-ui', 'sans-serif'],                   │ │
│ │         mono: ['JetBrains Mono', 'Consolas', 'monospace'],            │ │
│ │       },                                                               │ │
│ │       animation: {                                                     │ │
│ │         'data-flow': 'dataFlow 3s ease-in-out infinite',              │ │
│ │         'ai-pulse': 'aiPulse 2s ease-in-out infinite',                │ │
│ │         'code-type': 'codeType 4s steps(40) infinite',                │ │
│ │         'neural-network': 'neuralNetwork 8s linear infinite',         │ │
│ │       },                                                               │ │
│ │       keyframes: {                                                     │ │
│ │         dataFlow: {                                                    │ │
│ │           '0%, 100%': { transform: 'translateX(-100%)', opacity: '0' }, │ │
│ │           '50%': { transform: 'translateX(0%)', opacity: '1' },        │ │
│ │         },                                                             │ │
│ │         aiPulse: {                                                     │ │
│ │           '0%, 100%': { transform: 'scale(1)', opacity: '0.7' },      │ │
│ │           '50%': { transform: 'scale(1.05)', opacity: '1' },          │ │
│ │         },                                                             │ │
│ │       },                                                               │ │
│ │     },                                                                 │ │
│ │   },                                                                   │ │
│ │   plugins: [                                                           │ │
│ │     require('@tailwindcss/typography'),                               │ │
│ │     require('@tailwindcss/forms'),                                     │ │
│ │   ],                                                                   │ │
│ │ }                                                                      │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                          CORE LAYOUT STRUCTURE                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ src/app/layout.tsx:                                                        │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ import type { Metadata } from 'next'                                   │ │
│ │ import { Inter, JetBrains_Mono } from 'next/font/google'               │ │
│ │ import './globals.css'                                                 │ │
│ │ import { Providers } from '@/components/providers'                     │ │
│ │ import { Header } from '@/components/layout/header'                    │ │
│ │ import { Footer } from '@/components/layout/footer'                    │ │
│ │ import { cn } from '@/lib/utils'                                       │ │
│ │                                                                        │ │
│ │ const inter = Inter({                                                  │ │
│ │   subsets: ['latin'],                                                  │ │
│ │   variable: '--font-sans',                                             │ │
│ │ })                                                                     │ │
│ │                                                                        │ │
│ │ const jetbrainsMono = JetBrains_Mono({                                 │ │
│ │   subsets: ['latin'],                                                  │ │
│ │   variable: '--font-mono',                                             │ │
│ │ })                                                                     │ │
│ │                                                                        │ │
│ │ export const metadata: Metadata = {                                    │ │
│ │   title: {                                                             │ │
│ │     default: 'Khiw Nitithadachot | Data Engineer & AI Specialist',    │ │
│ │     template: '%s | Khiw Nitithadachot'                               │ │
│ │   },                                                                   │ │
│ │   description: 'Professional portfolio showcasing expertise in...',   │ │
│ │   keywords: ['Data Engineering', 'AI', 'Machine Learning', 'CFD'],    │ │
│ │   authors: [{ name: 'Khiw Nitithadachot' }],                          │ │
│ │   creator: 'Khiw Nitithadachot',                                       │ │
│ │   openGraph: {                                                         │ │
│ │     type: 'website',                                                   │ │
│ │     locale: 'en_US',                                                   │ │
│ │     url: 'https://getintheq.com',                                      │ │
│ │     siteName: 'Khiw Nitithadachot Portfolio',                         │ │
│ │   },                                                                   │ │
│ │   robots: {                                                            │ │
│ │     index: true,                                                       │ │
│ │     follow: true,                                                      │ │
│ │   },                                                                   │ │
│ │ }                                                                      │ │
│ │                                                                        │ │
│ │ export default function RootLayout({                                   │ │
│ │   children,                                                            │ │
│ │ }: {                                                                   │ │
│ │   children: React.ReactNode                                            │ │
│ │ }) {                                                                   │ │
│ │   return (                                                             │ │
│ │     <html lang="en" suppressHydrationWarning>                         │ │
│ │       <body                                                            │ │
│ │         className={cn(                                                 │ │
│ │           'min-h-screen bg-background font-sans antialiased',          │ │
│ │           inter.variable,                                              │ │
│ │           jetbrainsMono.variable                                       │ │
│ │         )}                                                             │ │
│ │       >                                                                │ │
│ │         <Providers>                                                    │ │
│ │           <div className="relative flex min-h-screen flex-col">       │ │
│ │             <Header />                                                 │ │
│ │             <main className="flex-1">{children}</main>                │ │
│ │             <Footer />                                                 │ │
│ │           </div>                                                       │ │
│ │         </Providers>                                                   │ │
│ │       </body>                                                          │ │
│ │     </html>                                                            │ │
│ │   )                                                                    │ │
│ │ }                                                                      │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                          COMPONENT ARCHITECTURE                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ src/components/                                                            │
│ ├── ui/                          # Reusable UI components                 │
│ │   ├── button.tsx                # CVA-based button variants             │
│ │   ├── card.tsx                  # Consistent card layouts               │
│ │   ├── input.tsx                 # Form input components                 │
│ │   ├── badge.tsx                 # Status and tag badges                 │
│ │   ├── skeleton.tsx              # Loading skeletons                     │
│ │   └── ...                       # Other Radix UI integrations           │
│ │                                                                         │
│ ├── layout/                      # Layout components                      │
│ │   ├── header.tsx                # Navigation header                     │
│ │   ├── footer.tsx                # Site footer                          │
│ │   ├── sidebar.tsx               # Responsive sidebar                    │
│ │   └── breadcrumb.tsx            # Navigation breadcrumbs                │
│ │                                                                         │
│ ├── sections/                    # Page sections                          │
│ │   ├── hero-section.tsx          # Landing page hero                     │
│ │   ├── about-section.tsx         # About/bio section                     │
│ │   ├── skills-section.tsx        # Technical skills showcase             │
│ │   ├── experience-section.tsx    # Work experience timeline              │
│ │   └── contact-section.tsx       # Contact form                          │
│ │                                                                         │
│ ├── ai-playground/               # AI demo components                     │
│ │   ├── text-summarizer.tsx       # NLP summarization demo                │
│ │   ├── object-detector.tsx       # Computer vision demo                  │
│ │   ├── data-visualizer.tsx       # Data science dashboard                │
│ │   ├── cfd-simulator.tsx         # Engineering analysis                  │
│ │   └── playground-layout.tsx     # Shared playground layout              │
│ │                                                                         │
│ ├── blog/                        # Blog-specific components               │
│ │   ├── post-list.tsx             # Blog post listing                     │
│ │   ├── post-card.tsx             # Individual post preview               │
│ │   ├── post-content.tsx          # Article content renderer              │
│ │   ├── post-navigation.tsx       # Previous/next navigation              │
│ │   └── blog-sidebar.tsx          # Categories and tags                   │
│ │                                                                         │
│ ├── projects/                    # Project showcase components            │
│ │   ├── project-grid.tsx          # Projects grid layout                  │
│ │   ├── project-card.tsx          # Individual project preview            │
│ │   ├── project-details.tsx       # Detailed project view                 │
│ │   ├── tech-stack-badge.tsx      # Technology badges                     │
│ │   └── github-stats.tsx          # Repository statistics                 │
│ │                                                                         │
│ └── animations/                  # Framer Motion components               │
│     ├── page-transition.tsx       # Page transition animations            │
│     ├── reveal-animation.tsx      # Scroll-triggered reveals              │
│     ├── stagger-container.tsx     # Staggered child animations            │
│     └── loading-spinner.tsx       # Custom loading animations             │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                          STATE MANAGEMENT                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ src/store/                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ // src/store/theme-store.ts                                            │ │
│ │ import { create } from 'zustand'                                       │ │
│ │ import { persist } from 'zustand/middleware'                           │ │
│ │                                                                        │ │
│ │ interface ThemeStore {                                                 │ │
│ │   theme: 'light' | 'dark' | 'system'                                  │ │
│ │   setTheme: (theme: 'light' | 'dark' | 'system') => void              │ │
│ │ }                                                                      │ │
│ │                                                                        │ │
│ │ export const useThemeStore = create<ThemeStore>()(                     │ │
│ │   persist(                                                             │ │
│ │     (set) => ({                                                        │ │
│ │       theme: 'system',                                                 │ │
│ │       setTheme: (theme) => set({ theme }),                             │ │
│ │     }),                                                                │ │
│ │     {                                                                  │ │
│ │       name: 'theme-storage',                                           │ │
│ │     }                                                                  │ │
│ │   )                                                                    │ │
│ │ )                                                                      │ │
│ │                                                                        │ │
│ │ // src/store/ui-store.ts                                               │ │
│ │ import { create } from 'zustand'                                       │ │
│ │                                                                        │ │
│ │ interface UIStore {                                                    │ │
│ │   sidebarOpen: boolean                                                 │ │
│ │   setSidebarOpen: (open: boolean) => void                             │ │
│ │   loading: boolean                                                     │ │
│ │   setLoading: (loading: boolean) => void                              │ │
│ │   notification: string | null                                          │ │
│ │   setNotification: (message: string | null) => void                   │ │
│ │ }                                                                      │ │
│ │                                                                        │ │
│ │ export const useUIStore = create<UIStore>((set) => ({                 │ │
│ │   sidebarOpen: false,                                                  │ │
│ │   setSidebarOpen: (open) => set({ sidebarOpen: open }),               │ │
│ │   loading: false,                                                      │ │
│ │   setLoading: (loading) => set({ loading }),                          │ │
│ │   notification: null,                                                  │ │
│ │   setNotification: (message) => set({ notification: message }),       │ │
│ │ }))                                                                    │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                          API CLIENT SETUP                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ src/lib/api.ts:                                                           │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ // API client configuration                                            │ │
│ │ const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://...'   │ │
│ │ const CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || 'http://...'   │ │
│ │                                                                        │ │
│ │ // CMS API functions                                                   │ │
│ │ export async function getBlogPosts() {                                 │ │
│ │   const response = await fetch(`${CMS_BASE_URL}/api/blog-posts`)       │ │
│ │   return response.json()                                               │ │
│ │ }                                                                      │ │
│ │                                                                        │ │
│ │ export async function getBlogPost(slug: string) {                      │ │
│ │   const response = await fetch(                                        │ │
│ │     `${CMS_BASE_URL}/api/blog-posts?filters[slug][$eq]=${slug}`       │ │
│ │   )                                                                    │ │
│ │   return response.json()                                               │ │
│ │ }                                                                      │ │
│ │                                                                        │ │
│ │ export async function getProjects() {                                  │ │
│ │   const response = await fetch(`${CMS_BASE_URL}/api/projects`)         │ │
│ │   return response.json()                                               │ │
│ │ }                                                                      │ │
│ │                                                                        │ │
│ │ // AI API functions                                                    │ │
│ │ export async function summarizeText(text: string) {                    │ │
│ │   const response = await fetch(`${API_BASE_URL}/nlp/summarize`, {     │ │
│ │     method: 'POST',                                                    │ │
│ │     headers: { 'Content-Type': 'application/json' },                  │ │
│ │     body: JSON.stringify({ text }),                                    │ │
│ │   })                                                                   │ │
│ │   return response.json()                                               │ │
│ │ }                                                                      │ │
│ │                                                                        │ │
│ │ export async function detectObjects(imageFile: File) {                 │ │
│ │   const formData = new FormData()                                      │ │
│ │   formData.append('file', imageFile)                                   │ │
│ │                                                                        │ │
│ │   const response = await fetch(`${API_BASE_URL}/cv/detect`, {         │ │
│ │     method: 'POST',                                                    │ │
│ │     body: formData,                                                    │ │
│ │   })                                                                   │ │
│ │   return response.json()                                               │ │
│ │ }                                                                      │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                       FRAMER MOTION ANIMATIONS                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ src/components/animations/page-transition.tsx:                            │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 'use client'                                                           │ │
│ │ import { motion, AnimatePresence } from 'framer-motion'                │ │
│ │ import { usePathname } from 'next/navigation'                          │ │
│ │                                                                        │ │
│ │ const pageVariants = {                                                 │ │
│ │   initial: {                                                           │ │
│ │     opacity: 0,                                                        │ │
│ │     y: 20,                                                             │ │
│ │   },                                                                   │ │
│ │   in: {                                                                │ │
│ │     opacity: 1,                                                        │ │
│ │     y: 0,                                                              │ │
│ │   },                                                                   │ │
│ │   out: {                                                               │ │
│ │     opacity: 0,                                                        │ │
│ │     y: -20,                                                            │ │
│ │   },                                                                   │ │
│ │ }                                                                      │ │
│ │                                                                        │ │
│ │ const pageTransition = {                                               │ │
│ │   type: 'tween',                                                       │ │
│ │   ease: 'anticipate',                                                  │ │
│ │   duration: 0.5,                                                       │ │
│ │ }                                                                      │ │
│ │                                                                        │ │
│ │ export function PageTransition({                                       │ │
│ │   children,                                                            │ │
│ │ }: {                                                                   │ │
│ │   children: React.ReactNode                                            │ │
│ │ }) {                                                                   │ │
│ │   const pathname = usePathname()                                       │ │
│ │                                                                        │ │
│ │   return (                                                             │ │
│ │     <AnimatePresence mode="wait">                                      │ │
│ │       <motion.div                                                      │ │
│ │         key={pathname}                                                 │ │
│ │         initial="initial"                                              │ │
│ │         animate="in"                                                   │ │
│ │         exit="out"                                                     │ │
│ │         variants={pageVariants}                                        │ │
│ │         transition={pageTransition}                                    │ │
│ │       >                                                                │ │
│ │         {children}                                                     │ │
│ │       </motion.div>                                                    │ │
│ │     </AnimatePresence>                                                 │ │
│ │   )                                                                    │ │
│ │ }                                                                      │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘

This frontend implementation guide provides the foundation for building a 
modern, performant, and visually stunning portfolio website using Next.js 14, 
TypeScript, and Tailwind CSS with sophisticated animations and state management.

The architecture is designed for scalability, maintainability, and optimal 
user experience across all devices and interaction patterns.