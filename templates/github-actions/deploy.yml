name: Deploy to <PERSON>flare

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run type checking
        run: npx tsc --noEmit

      - name: Run unit tests
        run: npm run test

      - name: Build application
        run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        if: github.ref == 'refs/heads/main'
        with:
          name: build-artifacts
          path: dist/
          retention-days: 1

  deploy-pages:
    name: Deploy to Cloudflare Pages
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: ${{ steps.deploy.outputs.url }}
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: dist/

      - name: Deploy to Cloudflare Pages
        id: deploy
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: ${{ vars.PROJECT_NAME }}
          directory: dist
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}

      - name: Add deployment status comment
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const deployUrl = '${{ steps.deploy.outputs.url }}';
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **Deployment Preview**\n\n✅ Successfully deployed to: ${deployUrl}\n\n*Deployment will be automatically updated on new commits.*`
            });

  deploy-workers:
    name: Deploy Cloudflare Workers
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && contains(github.repository, 'ai-portfolio-platform') || contains(github.repository, 'enterprise-data-pipeline') || contains(github.repository, 'realtime-bi-dashboard') || contains(github.repository, 'enterprise-llm-solution')
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Deploy Workers
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy

  performance-audit:
    name: Performance Audit
    needs: deploy-pages
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          urls: |
            https://${{ vars.PROJECT_NAME }}.pages.dev
          budgetPath: ./lighthouse-budget.json
          uploadArtifacts: true
          temporaryPublicStorage: true

      - name: Comment Lighthouse results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = './lhci_reports/manifest.json';
            
            if (fs.existsSync(path)) {
              const manifest = JSON.parse(fs.readFileSync(path, 'utf8'));
              const summary = manifest[0].summary;
              
              const comment = `## 🚀 Lighthouse Performance Report
              
              | Category | Score |
              |----------|-------|
              | Performance | ${Math.round(summary.performance * 100)}/100 |
              | Accessibility | ${Math.round(summary.accessibility * 100)}/100 |
              | Best Practices | ${Math.round(summary['best-practices'] * 100)}/100 |
              | SEO | ${Math.round(summary.seo * 100)}/100 |
              
              [View detailed report](${manifest[0].reportUrl})`;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --audit-level=moderate

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  notify:
    name: Notify Deployment Status
    needs: [deploy-pages, deploy-workers]
    runs-on: ubuntu-latest
    if: always() && github.ref == 'refs/heads/main'
    
    steps:
      - name: Notify success
        if: needs.deploy-pages.result == 'success'
        run: |
          echo "✅ Deployment successful for ${{ github.repository }}"
          echo "🔗 Live URL: https://${{ vars.PROJECT_NAME }}.pages.dev"

      - name: Notify failure
        if: needs.deploy-pages.result == 'failure' || needs.deploy-workers.result == 'failure'
        run: |
          echo "❌ Deployment failed for ${{ github.repository }}"
          exit 1