# Portfolio Website - Khiw (Ikkyu) Nitithadachot

## Overview

This is a professional AI-powered portfolio website for Khiw (Ikkyu) <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a Data Engineer and AI/ML Specialist. The site showcases expertise in data engineering, machine learning, full-stack development, and CFD/FEA analysis. Built as a modern single-page application with interactive components including an AI playground, project portfolio, technical blog, and contact functionality.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript using Vite as the build tool
- **Styling**: Tailwind CSS with custom design system featuring deep blue color scheme
- **UI Components**: Radix UI primitives with shadcn/ui component library for consistent design
- **Routing**: Wouter for lightweight client-side routing
- **State Management**: TanStack React Query for server state and custom hooks for component state
- **Theme System**: Custom theme provider with light/dark mode support
- **Typography**: Custom font stack including Inter, Poppins, and JetBrains Mono

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **Development**: Hot module replacement via Vite middleware in development
- **API Design**: RESTful endpoints with proper error handling and request logging
- **Validation**: Zod schemas for runtime type checking and data validation

### Database Design
- **ORM**: Drizzle ORM for type-safe database operations
- **Database**: PostgreSQL with Neon serverless configuration
- **Schema**: Two main entities - users and contacts with proper relationships and constraints
- **Migrations**: Drizzle Kit for database schema management

### Component Architecture
- **Design System**: Comprehensive UI component library with consistent styling
- **Layout**: Modular section-based layout (Hero, About, Skills, Projects, etc.)
- **Responsive Design**: Mobile-first approach with Tailwind breakpoints
- **Accessibility**: WCAG 2.1 AA compliance with proper ARIA labels and keyboard navigation
- **Animation**: CSS-based animations with Tailwind utilities for smooth interactions

### Data Layer
- **Contact Management**: Form submission system for client inquiries with validation
- **Content Structure**: Static content with potential for CMS integration
- **Type Safety**: Shared TypeScript types between frontend and backend
- **Validation**: Input sanitization and validation on both client and server sides

## External Dependencies

### Core Framework Dependencies
- **React Ecosystem**: React 18, React DOM for UI rendering
- **Build Tools**: Vite for development and bundling, TypeScript compiler
- **Routing**: Wouter for lightweight client-side routing

### Database & ORM
- **Database**: Neon PostgreSQL serverless database
- **ORM**: Drizzle ORM with PostgreSQL dialect
- **Validation**: Drizzle-Zod for schema validation integration

### UI & Styling
- **CSS Framework**: Tailwind CSS with custom configuration
- **Component Library**: Radix UI primitives for accessible components
- **Icon Library**: Lucide React for consistent iconography
- **Styling Utilities**: clsx and tailwind-merge for conditional styling

### State Management & Data Fetching
- **Server State**: TanStack React Query for caching and synchronization
- **Forms**: React Hook Form with Hookform Resolvers for form management
- **Validation**: Zod for runtime type validation

### Development Tools
- **Hot Reload**: Vite HMR for fast development iteration
- **Error Handling**: Replit-specific error overlay for development
- **Code Mapping**: Source map support for debugging

### Deployment & Hosting
- **Platform**: Configured for Replit deployment with custom scripts
- **Environment**: Environment variable management for database connections
- **Asset Handling**: Static asset optimization through Vite

### Optional Integrations
- **Analytics**: Ready for integration with tracking services
- **Email**: Contact form backend ready for email service integration
- **CMS**: Architecture supports future headless CMS integration for blog content