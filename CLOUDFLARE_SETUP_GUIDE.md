# Cloudflare Integration Setup Guide

## Overview

This guide provides step-by-step instructions for deploying all 7 portfolio repositories to Cloudflare's serverless infrastructure.

## Prerequisites

- ✅ GitHub repositories created (via setup scripts)
- ☁️ Cloudflare account
- 🔑 GitHub secrets configured
- 🌐 Domain names (or use Cloudflare-provided domains)

## 1. Cloudflare Account Setup

### Create Account
1. Visit [https://cloudflare.com](https://cloudflare.com)
2. Sign up for a free account
3. Verify email address

### Get API Credentials
1. Go to [API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. Create token with permissions:
   - Zone: Read
   - Page Rules: Edit
   - Zone Settings: Edit
   - Cloudflare Pages: Edit
   - Account: Read
3. Note your **Account ID** from the dashboard

## 2. Domain Configuration

### Option A: Use Custom Domains
For each project, register domains:
- `portfolio-hub.dev`
- `ai-portfolio-platform.dev`
- `enterprise-data-pipeline.dev`
- `predictive-analytics-platform.dev`
- `cfd-analysis-platform.dev`
- `realtime-bi-dashboard.dev`
- `enterprise-llm-solution.dev`

### Option B: Use Cloudflare Pages Domains
Use auto-generated `*.pages.dev` domains for free deployment.

## 3. GitHub Secrets Configuration

For each repository, add these secrets in GitHub:

### Repository Settings → Secrets and Variables → Actions

```bash
# Required for all repositories
CLOUDFLARE_API_TOKEN=your_api_token_here
CLOUDFLARE_ACCOUNT_ID=your_account_id_here

# Project-specific variables (set as repository variables)
PROJECT_NAME=repository-name-here
```

### Repository Variables
Set these as **variables** (not secrets):
```bash
PROJECT_NAME=portfolio-hub  # Match repository name
```

## 4. Cloudflare Pages Setup

### For Frontend Projects
1. **portfolio-hub**
2. **ai-portfolio-platform** 
3. **predictive-analytics-platform**
4. **cfd-analysis-platform**
5. **realtime-bi-dashboard**

#### Setup Steps:
1. Go to [Cloudflare Pages](https://dash.cloudflare.com/pages)
2. Click "Create a project"
3. Connect to GitHub repository
4. Configure build settings:
   ```
   Build command: npm run build
   Build output directory: dist
   Root directory: /
   ```
5. Add environment variables if needed
6. Deploy

### For Fullstack Projects (Pages + Workers)
1. **ai-portfolio-platform**
2. **enterprise-data-pipeline**
3. **realtime-bi-dashboard**
4. **enterprise-llm-solution**

#### Additional Steps:
1. Set up Cloudflare Pages (as above)
2. Set up Cloudflare Workers (see section 5)
3. Configure D1 databases (see section 6)
4. Set up R2 storage (see section 7)

## 5. Cloudflare Workers Setup

### Install Wrangler CLI
```bash
npm install -g wrangler
wrangler login
```

### For Each Fullstack Project:

#### 1. Create D1 Database
```bash
# Navigate to project directory
cd path/to/repository

# Create database
wrangler d1 create project-name-db

# Note the database ID and update wrangler.toml
```

#### 2. Create R2 Bucket
```bash
wrangler r2 bucket create project-name-storage
```

#### 3. Update wrangler.toml
Replace placeholders in `wrangler.toml`:
```toml
name = "project-name-worker"
main = "src/workers/api.ts"
compatibility_date = "2024-01-01"

[[d1_databases]]
binding = "DB"
database_name = "project-name-db"
database_id = "your-actual-database-id"

[[r2_buckets]]
binding = "BUCKET"
bucket_name = "project-name-storage"
```

#### 4. Deploy Worker
```bash
wrangler deploy
```

## 6. Database Setup (D1)

### For Each Database:

#### 1. Create Schema
```sql
-- Example schema for portfolio projects
CREATE TABLE IF NOT EXISTS projects (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'active',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS analytics (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  project_id INTEGER,
  event_type TEXT,
  event_data TEXT,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (project_id) REFERENCES projects(id)
);
```

#### 2. Run Migrations
```bash
# Create migration file
wrangler d1 migrations create initial-schema

# Apply migration
wrangler d1 migrations apply project-name-db --local
wrangler d1 migrations apply project-name-db --remote
```

## 7. R2 Storage Setup

### Configure Buckets
For each project requiring file storage:

```bash
# Create bucket
wrangler r2 bucket create project-name-storage

# Set CORS policy (if needed)
wrangler r2 bucket cors put project-name-storage --cors-config cors.json
```

### CORS Configuration (cors.json)
```json
[
  {
    "AllowedOrigins": ["https://project-name.dev"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
    "AllowedHeaders": ["*"],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3000
  }
]
```

## 8. Environment Variables

### For Each Project:

#### Frontend-Only Projects
```bash
# .env.production
VITE_APP_NAME=project-name
VITE_API_BASE_URL=https://project-name.dev/api
```

#### Fullstack Projects
```bash
# .env.production
VITE_APP_NAME=project-name
VITE_API_BASE_URL=https://project-name.dev/api

# Worker environment (set in wrangler.toml or Cloudflare dashboard)
DATABASE_URL=your-d1-connection-string
R2_BUCKET_NAME=project-name-storage
```

## 9. Custom Domains (Optional)

### If Using Custom Domains:

1. **Add Domain to Cloudflare**
   - Add domain to Cloudflare DNS
   - Update nameservers with registrar

2. **Configure Pages Custom Domain**
   - Go to Pages → Custom domains
   - Add your domain
   - Set up SSL/TLS

3. **Configure Worker Routes**
   - Go to Workers → Routes
   - Add route: `project-name.dev/api/*`
   - Select appropriate worker

## 10. Monitoring Setup

### Enable Analytics
1. Go to Analytics & Logs → Web Analytics
2. Enable for each domain
3. Add tracking code to pages

### Set Up Uptime Monitoring
1. Use Cloudflare's uptime monitoring
2. Configure alerts for downtime
3. Set up status page (optional)

## 11. GitHub Actions Deployment

### Automatic Deployment Flow
When you push to main branch:

1. **Tests Run** → Unit tests, linting, type checking
2. **Build** → Creates production build
3. **Deploy Pages** → Deploys to Cloudflare Pages
4. **Deploy Workers** → Deploys API workers (if applicable)
5. **Performance Audit** → Lighthouse testing
6. **Notify** → Deployment status updates

### Manual Deployment
```bash
# For Pages
npm run build
wrangler pages deploy dist

# For Workers
wrangler deploy
```

## 12. Verification Checklist

### For Each Project:
- [ ] Repository exists on GitHub
- [ ] GitHub Actions workflows are working
- [ ] Cloudflare Pages deployment succeeds
- [ ] Workers are deployed (if applicable)
- [ ] Database is created and migrated
- [ ] Custom domain is configured (if using)
- [ ] SSL certificate is active
- [ ] Site loads correctly
- [ ] API endpoints respond (for fullstack)

### Portfolio Hub Integration:
- [ ] All project links are updated
- [ ] Status monitoring is working
- [ ] Live demos are accessible
- [ ] Performance metrics are tracking

## 13. Cost Optimization

### Cloudflare Free Tier Limits:
- **Pages**: 500 builds/month, 20,000 requests/day
- **Workers**: 100,000 requests/day
- **D1**: 100,000 read/write operations/day
- **R2**: 10GB storage, 1 million Class A operations/month

### Optimization Tips:
1. Use caching extensively
2. Optimize images and assets
3. Implement lazy loading
4. Use Cloudflare's image optimization
5. Monitor usage in dashboard

## 14. Troubleshooting

### Common Issues:

#### Build Failures
- Check Node.js version compatibility
- Verify all dependencies are listed
- Check for TypeScript errors

#### Deployment Issues
- Verify API token permissions
- Check account ID is correct
- Ensure wrangler.toml is properly configured

#### DNS Issues
- Verify nameservers are updated
- Check DNS propagation
- Ensure SSL is properly configured

### Support Resources:
- [Cloudflare Docs](https://developers.cloudflare.com/)
- [GitHub Actions Docs](https://docs.github.com/en/actions)
- [Wrangler CLI Docs](https://developers.cloudflare.com/workers/wrangler/)

## 15. Next Steps After Deployment

1. **Monitor Performance**
   - Set up alerts for downtime
   - Monitor Core Web Vitals
   - Track user engagement

2. **Optimize for SEO**
   - Add meta tags
   - Implement structured data
   - Optimize for mobile

3. **Enhance Security**
   - Configure WAF rules
   - Set up rate limiting
   - Implement CSRF protection

4. **Scale as Needed**
   - Monitor usage patterns
   - Upgrade plans if necessary
   - Optimize database queries

---

## Quick Reference Commands

```bash
# Test GitHub connection
cd scripts && node test-github-connection.js

# Create all repositories
cd scripts && npm run setup

# Deploy individual project
cd project-directory
npm run build
wrangler deploy

# Check deployment status
wrangler deployments list

# View logs
wrangler tail

# Database operations
wrangler d1 execute DB --command "SELECT * FROM projects"
```

This setup creates a professional, scalable infrastructure suitable for showcasing enterprise-grade development capabilities to potential clients and employers.